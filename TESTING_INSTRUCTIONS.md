# Инструкция по тестированию стабильности предсказаний

## Быстрый тест стабильности

### 1. Запуск приложения
```bash
flutter run -d chrome
```

### 2. Переход к экрану тестирования
1. Откройте приложение
2. Перейдите на третью вкладку (Sinusoid/Reactor)
3. Дождитесь полной загрузки данных

### 3. Запись базовых значений
Запишите следующие значения:

**Основные метрики (верхняя часть экрана):**
- Fear & Greed Index: `____`
- Volume Score: `____`
- Holders Score: `____`
- Social Engagement: `____`
- Price Volatility: `____`
- News Sentiment: `____`
- Bitcoin Dominance: `____`

**Центральный индикатор:**
- Значение индикатора: `____`
- Уровень (Crash/Anxiety/Stasis/Lift/Surge): `____`

**Продвинутые метрики (нижняя часть):**
- Trend Strength: `____`
- Volatility: `____`
- Momentum: `____`
- Market Efficiency: `____`
- Support Level: `____`
- Resistance Level: `____`
- RSI: `____`
- MACD: `____`

### 4. Тестирование стабильности

#### Тест 1: Обновление страницы
1. Нажмите F5 или Ctrl+R для обновления страницы
2. Дождитесь загрузки
3. Сравните все значения с записанными
4. **Ожидаемый результат**: Все значения идентичны

#### Тест 2: Кнопка обновления
1. Нажмите кнопку "Refresh" (🔄) в верхней части экрана
2. Дождитесь загрузки
3. Сравните все значения
4. **Ожидаемый результат**: Все значения идентичны

#### Тест 3: Очистка кэша
1. Нажмите кнопку "Clear Cache" (⚡) в верхней части экрана
2. Дождитесь сообщения "Cache cleared!"
3. Дождитесь перезагрузки данных
4. Сравните все значения
5. **Ожидаемый результат**: Все значения идентичны

#### Тест 4: Переключение режимов
1. Переключите между "Regular" и "Enhanced" режимами
2. Запишите значения в Enhanced режиме
3. Переключитесь обратно в Regular
4. Переключитесь снова в Enhanced
5. **Ожидаемый результат**: Значения в каждом режиме стабильны

### 5. Проверка логов
Откройте Developer Tools (F12) и проверьте консоль на наличие сообщений:
- `MarketAnalytics: Using cached stable metrics`
- `PredictionEngine: Generating stable predictions`
- `ReactorSinusoidScreen: Stable standard/risky metrics set`

### 6. Результат теста
✅ **УСПЕХ**: Все значения остаются идентичными при всех тестах
❌ **НЕУДАЧА**: Любые значения изменяются между тестами

## Дополнительные тесты

### Тест на разные дни
1. Измените системную дату на завтра
2. Перезапустите приложение
3. **Ожидаемый результат**: Значения должны немного отличаться (но стабильно для нового дня)

### Тест производительности
1. Выполните 10 обновлений подряд
2. Проверьте время отклика
3. **Ожидаемый результат**: Быстрая загрузка из кэша

## Устранение неполадок

### Если значения все еще меняются:
1. Проверьте консоль на ошибки
2. Убедитесь, что используются стабильные сервисы
3. Проверьте, что кэш работает корректно

### Если приложение не запускается:
```bash
flutter clean
flutter pub get
flutter run -d chrome
```

## Контакты
При обнаружении проблем сообщите:
- Какой тест не прошел
- Какие значения изменились
- Сообщения из консоли
- Скриншоты до/после 