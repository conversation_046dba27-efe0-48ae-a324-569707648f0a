import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;

/// A customizable cosmic background widget with animated stars and optional effects.
/// 
/// This widget creates a beautiful animated star field background that can be used
/// across different screens in the application. It supports various customization
/// options including star colors, density, animation speed, and additional effects.
class CosmicBackground extends StatefulWidget {
  /// The number of stars to display in the background
  final int starCount;
  
  /// The base color of the stars
  final Color baseStarColor;
  
  /// Additional colors for star variety
  final List<Color> starColors;
  
  /// The minimum size of stars
  final double minStarSize;
  
  /// The maximum size of stars
  final double maxStarSize;
  
  /// The duration of the star twinkling animation
  final Duration animationDuration;
  
  /// Whether to enable the comet effect
  final bool enableComet;
  
  /// The background color
  final Color backgroundColor;
  
  /// Whether to enable parallax effect on mouse movement
  final bool enableParallax;
  
  /// The intensity of the parallax effect
  final double parallaxIntensity;

  const CosmicBackground({
    Key? key,
    this.starCount = 200,
    this.baseStarColor = Colors.white,
    this.starColors = const [],
    this.minStarSize = 1.0,
    this.maxStarSize = 3.0,
    this.animationDuration = const Duration(milliseconds: 5000),
    this.enableComet = true,
    this.backgroundColor = Colors.black,
    this.enableParallax = true,
    this.parallaxIntensity = 30.0,
  }) : super(key: key);

  @override
  State<CosmicBackground> createState() => _CosmicBackgroundState();
}

class _CosmicBackgroundState extends State<CosmicBackground> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final List<Star> _stars = [];
  final List<Comet> _comets = [];
  Offset _pointerPosition = Offset.zero;

  @override
  void initState() {
    super.initState();
    _initializeStars();
    _initializeComets();
    _animationController = AnimationController(
      vsync: this,
      duration: widget.animationDuration,
    )..repeat(reverse: true);
  }

  void _initializeStars() {
    final random = math.Random();
    final allColors = [widget.baseStarColor, ...widget.starColors];
    
    for (int i = 0; i < widget.starCount; i++) {
      _stars.add(Star(
        x: random.nextDouble(),
        y: random.nextDouble(),
        size: widget.minStarSize + random.nextDouble() * (widget.maxStarSize - widget.minStarSize),
        color: allColors[random.nextInt(allColors.length)],
        blinkDuration: (random.nextDouble() * 2.0 + 3.0) * 1000,
        parallaxFactor: random.nextDouble() * 0.5 + 0.5,
      ));
    }
  }

  void _initializeComets() {
    if (widget.enableComet) {
      _comets.add(Comet(
        x: -100,
        y: 100,
        speed: 10.0,
        angle: math.pi * 0.15,
        tailLength: 150.0,
        size: 3.0,
      ));
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onHover: widget.enableParallax ? (event) {
        setState(() {
          _pointerPosition = event.localPosition;
        });
      } : null,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return CustomPaint(
            size: Size.infinite,
            painter: CosmicBackgroundPainter(
              stars: _stars,
              comets: _comets,
              animationValue: _animationController.value,
              pointerPosition: _pointerPosition,
              screenSize: MediaQuery.of(context).size,
              backgroundColor: widget.backgroundColor,
              enableParallax: widget.enableParallax,
              parallaxIntensity: widget.parallaxIntensity,
            ),
          );
        },
      ),
    );
  }
}

/// Represents a star in the cosmic background
class Star {
  final double x;
  final double y;
  final double size;
  final Color color;
  final double blinkDuration;
  final double parallaxFactor;

  Star({
    required this.x,
    required this.y,
    required this.size,
    required this.color,
    required this.blinkDuration,
    required this.parallaxFactor,
  });
}

/// Represents a comet in the cosmic background
class Comet {
  double x;
  double y;
  final double speed;
  final double angle;
  final double tailLength;
  final double size;
  double opacity = 1.0;
  bool isActive = true;

  Comet({
    required this.x,
    required this.y,
    required this.speed,
    required this.angle,
    required this.tailLength,
    required this.size,
  });

  void update(Size canvasSize, DateTime now) {
    if (!isActive) return;

    // Update position
    x += math.cos(angle) * speed;
    y += math.sin(angle) * speed;

    // Check if comet is in visible area
    final isInVisibleArea = x >= 0 && x <= canvasSize.width &&
                          y >= 0 && y <= canvasSize.height;

    // Fade in when entering visible area
    if (isInVisibleArea && opacity < 1.0) {
      opacity = math.min(1.0, opacity + 0.1);
    }

    // Fade out when leaving visible area
    if (!isInVisibleArea && opacity > 0.0) {
      opacity = math.max(0.0, opacity - 0.1);
    }

    // Check if comet is completely out of bounds
    if (x < -tailLength * 2 ||
        x > canvasSize.width + tailLength * 2 ||
        y < -tailLength * 2 ||
        y > canvasSize.height + tailLength * 2) {
      isActive = false;
    }
  }

  bool isVisible() {
    return isActive && opacity > 0.0;
  }
}

/// Custom painter for the cosmic background
class CosmicBackgroundPainter extends CustomPainter {
  final List<Star> stars;
  final List<Comet> comets;
  final double animationValue;
  final Offset pointerPosition;
  final Size screenSize;
  final Color backgroundColor;
  final bool enableParallax;
  final double parallaxIntensity;

  CosmicBackgroundPainter({
    required this.stars,
    required this.comets,
    required this.animationValue,
    required this.pointerPosition,
    required this.screenSize,
    required this.backgroundColor,
    required this.enableParallax,
    required this.parallaxIntensity,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw background
    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()..color = backgroundColor,
    );

    // Calculate parallax offset
    final parallaxX = enableParallax ? 
        (pointerPosition.dx / screenSize.width - 0.5) * parallaxIntensity : 0.0;
    final parallaxY = enableParallax ? 
        (pointerPosition.dy / screenSize.height - 0.5) * parallaxIntensity : 0.0;

    // Draw stars
    for (var star in stars) {
      // Calculate star position with parallax
      final x = star.x * size.width + parallaxX * star.parallaxFactor;
      final y = star.y * size.height + parallaxY * star.parallaxFactor;

      // Calculate star opacity based on animation
      final opacity = 0.1 + (0.9 - 0.1) *
          (math.sin(2 * math.pi * (animationValue + star.x * star.y) % 1) * 0.5 + 0.5);

      // Draw star glow for larger stars
      if (star.size > 2.0) {
        final glowPaint = Paint()
          ..color = star.color.withAlpha((opacity * 100).toInt())
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3.0);

        canvas.drawCircle(
          Offset(x, y),
          star.size * 1.8,
          glowPaint,
        );
      }

      // Draw star
      final starPaint = Paint()
        ..color = star.color.withAlpha((opacity * 255).toInt());

      canvas.drawCircle(
        Offset(x, y),
        star.size,
        starPaint,
      );
    }

    // Update and draw comets
    for (var comet in comets) {
      comet.update(size, DateTime.now());
      if (comet.isVisible()) {
        _drawComet(canvas, comet);
      }
    }
  }

  void _drawComet(Canvas canvas, Comet comet) {
    // Create gradient for comet tail
    final tailGradient = ui.Gradient.linear(
      Offset(comet.x, comet.y),
      Offset(
        comet.x - comet.tailLength * math.cos(comet.angle),
        comet.y - comet.tailLength * math.sin(comet.angle),
      ),
      [
        Colors.white.withAlpha((comet.opacity * 255).toInt()),
        Colors.white.withAlpha((comet.opacity * 0.8 * 255).toInt()),
        Colors.blue.withAlpha((comet.opacity * 0.6 * 255).toInt()),
        Colors.transparent,
      ],
      [0.0, 0.3, 0.6, 1.0],
    );

    // Draw comet tail
    final tailPaint = Paint()
      ..shader = tailGradient
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round;

    canvas.drawLine(
      Offset(comet.x, comet.y),
      Offset(
        comet.x - comet.tailLength * math.cos(comet.angle),
        comet.y - comet.tailLength * math.sin(comet.angle),
      ),
      tailPaint,
    );

    // Draw comet head
    final headPaint = Paint()
      ..color = Colors.white.withAlpha((comet.opacity * 255).toInt())
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 2.0);

    canvas.drawCircle(
      Offset(comet.x, comet.y),
      comet.size,
      headPaint,
    );
  }

  @override
  bool shouldRepaint(CosmicBackgroundPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue ||
           oldDelegate.pointerPosition != pointerPosition;
  }
} 