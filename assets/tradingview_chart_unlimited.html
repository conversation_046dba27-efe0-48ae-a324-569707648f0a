<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>TradingView Chart - Unlimited Scrolling</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #131722;
            color: #d1d4dc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            position: fixed;
            touch-action: none;
        }

        #chart-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            cursor: grab;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            overflow: hidden;
        }

        #chart-container:active {
            cursor: grabbing;
        }

        #drag-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 1000;
            display: flex;
            align-items: center;
            opacity: 0.9;
            transition: opacity 0.3s;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        }

        #drag-indicator svg {
            margin-right: 8px;
            width: 18px;
            height: 18px;
        }

        #result-popup {
            position: absolute;
            top: 50%;
            left: 25%;
            transform: translate(-50%, -50%);
            background-color: rgba(30, 34, 45, 0.9);
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            color: white;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            min-width: 200px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: none;
        }

        .result-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 12px;
        }

        .result-success {
            color: #4CAF50;
        }

        .result-failure {
            color: #F44336;
        }

        .result-profit {
            font-size: 20px;
            font-weight: bold;
            margin: 12px 0;
        }

        .profit-positive {
            color: #4CAF50;
        }

        .profit-negative {
            color: #F44336;
        }
    </style>
</head>
<body>
    <div id="chart-container"></div>
    <div id="drag-indicator">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
            <path d="M10 9h4V6h3l-5-5-5 5h3v3zm-1 1H6V7l-5 5 5 5v-3h3v-4zm14 2l-5-5v3h-3v4h3v3l5-5zm-9 3h-4v3H7l5 5 5-5h-3v-3z"/>
        </svg>
        <span>Chart is fully draggable in ALL directions</span>
    </div>
    <div id="result-popup">
        <div class="result-title">Результат</div>
        <div id="result-status"></div>
        <div id="result-profit" class="result-profit"></div>
    </div>
    <script type="text/javascript" src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
    <script>
        // Глобальные переменные
        let chart;
        let candleSeries;
        let entryPointPrice = null;
        let entryPointTime = null;
        let horizontalLine = null;
        let allCandles = [];
        let visibleCandlesCount = 243;
        let extraCandlesCount = 300; // Большое количество дополнительных свечей справа
        let userHasMovedChart = false;
        let chartInitialized = false;

        // Обработчик сообщений от Flutter
        window.addEventListener('message', function(event) {
            console.log('Received message from Flutter:', event.data);
            try {
                const message = JSON.parse(event.data);

                switch (message.action) {
                    case 'loadCandles':
                        loadCandles(message.data);
                        break;
                    case 'showInitialCandles':
                        showInitialCandles();
                        break;
                    case 'showAllCandles':
                        showAllCandles();
                        break;
                    case 'setEntryPoint':
                        setEntryPoint();
                        break;
                    case 'determineResult':
                        determineResult();
                        break;
                    case 'clearChartElements':
                        clearChartElements();
                        break;
                    case 'resetChartPosition':
                        resetChartPosition();
                        break;
                    case 'centerLastCandle':
                        centerLastCandle();
                        break;
                    default:
                        console.log('Unknown action:', message.action);
                }
            } catch (e) {
                console.error('Error processing message:', e);
            }
        });

        // Инициализация графика
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
        });

        function initChart() {
            console.log('Initializing chart...');
            const chartContainer = document.getElementById('chart-container');

            if (typeof LightweightCharts === 'undefined') {
                console.error('LightweightCharts library is not loaded!');
                setTimeout(initChart, 500);
                return;
            }

            // Создаем график с настройками для свободного перемещения
            chart = LightweightCharts.createChart(chartContainer, {
                width: chartContainer.clientWidth,
                height: chartContainer.clientHeight,
                layout: {
                    backgroundColor: '#131722',
                    textColor: '#d1d4dc',
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                },
                grid: {
                    vertLines: { color: 'rgba(42, 46, 57, 0.5)' },
                    horzLines: { color: 'rgba(42, 46, 57, 0.5)' },
                },
                timeScale: {
                    timeVisible: true,
                    secondsVisible: false,
                    barSpacing: 6,
                    minBarSpacing: 4,
                    fixRightEdge: false,
                    fixLeftEdge: false,
                    rightBarStaysOnScroll: false,
                    lockVisibleTimeRangeOnResize: false,
                    shiftVisibleRangeOnNewBar: false,
                    rightOffset: 0,
                },
                rightPriceScale: {
                    autoScale: true,
                    scaleMargins: {
                        top: 0.1,
                        bottom: 0.1,
                    },
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                },
                handleScroll: true,
                handleScale: true,
            });

            // Создаем серию свечей
            candleSeries = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });

            // Настраиваем обработчики событий
            setupEventHandlers();

            // Показываем индикатор перемещения
            showDragIndicator();

            // Устанавливаем флаг инициализации
            chartInitialized = true;

            // Сообщаем Flutter, что график инициализирован
            sendMessageToFlutter('chartInitialized', []);
        }

        // Настройка обработчиков событий
        function setupEventHandlers() {
            if (!chart) return;

            const chartContainer = document.getElementById('chart-container');

            // Обработчик изменения размера окна
            window.addEventListener('resize', () => {
                if (!chart) return;

                chart.resize(
                    chartContainer.clientWidth,
                    chartContainer.clientHeight
                );

                // Центрируем последнюю свечу при изменении размера
                setTimeout(centerLastCandle, 100);
            });

            // Обработчики для отслеживания взаимодействия с графиком
            chartContainer.addEventListener('mousedown', () => {
                userHasMovedChart = true;
                chartContainer.style.cursor = 'grabbing';
            });

            chartContainer.addEventListener('mouseup', () => {
                chartContainer.style.cursor = 'grab';
            });

            chartContainer.addEventListener('touchstart', () => {
                userHasMovedChart = true;
            });

            // Отслеживаем изменения видимого диапазона
            chart.timeScale().subscribeVisibleLogicalRangeChange(() => {
                // Проверяем, что перемещение включено
                const options = chart.options();
                if (!options.handleScroll || !options.handleScale) {
                    console.log('Re-enabling dragging after range change');
                    chart.applyOptions({
                        handleScroll: true,
                        handleScale: true,
                    });
                }
            });
        }

        // Показать индикатор перемещения
        function showDragIndicator() {
            const dragIndicator = document.getElementById('drag-indicator');
            if (!dragIndicator) return;

            dragIndicator.style.display = 'flex';
            dragIndicator.style.opacity = '0.9';

            setTimeout(() => {
                dragIndicator.style.opacity = '0';
                setTimeout(() => {
                    dragIndicator.style.display = 'none';
                }, 300);
            }, 5000);
        }

        // Загрузка свечей
        function loadCandles(candles) {
            console.log('Loading candles...');
            try {
                // Если candles уже объект, используем его напрямую
                if (typeof candles === 'object') {
                    allCandles = candles;
                } else {
                    // Иначе пытаемся распарсить строку
                    allCandles = JSON.parse(candles);
                }

                console.log('Loaded', allCandles.length, 'candles');

                // Показываем начальные свечи
                showInitialCandles();

                // Сообщаем Flutter, что свечи загружены
                sendMessageToFlutter('candlesLoaded', [allCandles.length]);
            } catch (error) {
                console.error('Error loading candles:', error);
            }
        }

        // Создание дополнительных свечей для прокрутки вправо
        function createExtraCandles() {
            if (!allCandles || allCandles.length < 2) return [];

            const extraCandles = [];
            const lastCandle = allCandles[allCandles.length - 1];
            const secondLastCandle = allCandles[allCandles.length - 2];
            const timeStep = lastCandle.time - secondLastCandle.time;

            // Создаем дополнительные свечи с реалистичными данными
            let prevClose = lastCandle.close;
            for (let i = 1; i <= extraCandlesCount; i++) {
                // Генерируем случайное изменение цены в пределах ±2%
                const changePercent = (Math.random() * 4 - 2) * 1.0;
                const change = prevClose * (changePercent / 100);

                const close = prevClose + change;
                const open = prevClose;
                const high = Math.max(open, close) * (1 + Math.random() * 0.01);
                const low = Math.min(open, close) * (1 - Math.random() * 0.01);
                const volume = lastCandle.volume * (0.5 + Math.random());

                extraCandles.push({
                    time: lastCandle.time + timeStep * i,
                    open: open,
                    high: high,
                    low: low,
                    close: close,
                    volume: volume
                });

                prevClose = close;
            }

            console.log('Created', extraCandles.length, 'extra candles for right scrolling');
            return extraCandles;
        }

        // Показать только первые 243 свечи
        function showInitialCandles() {
            console.log('Showing initial candles...');

            if (!allCandles || allCandles.length === 0) {
                console.error('No candles available');
                return;
            }

            try {
                // Проверяем, что у нас достаточно свечей
                if (allCandles.length < visibleCandlesCount) {
                    console.error('Not enough candles:', allCandles.length, 'need', visibleCandlesCount);
                    return;
                }

                // Берем только первые 243 свечи
                const initialCandles = allCandles.slice(0, visibleCandlesCount);

                // Создаем дополнительные свечи для прокрутки вправо
                const extraCandles = createExtraCandles();

                // Объединяем начальные свечи с дополнительными
                const extendedCandles = [...initialCandles, ...extraCandles];

                // Устанавливаем расширенные данные на график
                candleSeries.setData(extendedCandles);

                // Центрируем последнюю видимую свечу
                setTimeout(() => {
                    centerLastCandle();

                    // Сообщаем Flutter, что начальные свечи отображены
                    sendMessageToFlutter('initialCandlesShown', []);
                }, 100);

                console.log('Initial candles shown');
            } catch (error) {
                console.error('Error showing initial candles:', error);
            }
        }

        // Показать все 250 свечей
        function showAllCandles() {
            console.log('Showing all candles...');

            if (!allCandles || allCandles.length === 0) {
                console.error('No candles available');
                return;
            }

            try {
                // Создаем дополнительные свечи для прокрутки вправо
                const extraCandles = createExtraCandles();

                // Объединяем все свечи с дополнительными
                const extendedCandles = [...allCandles, ...extraCandles];

                // Устанавливаем расширенные данные на график
                candleSeries.setData(extendedCandles);

                // Центрируем последнюю видимую свечу
                setTimeout(() => {
                    centerLastCandle();

                    // Сообщаем Flutter, что все свечи отображены
                    sendMessageToFlutter('allCandlesShown', []);
                }, 100);

                console.log('All candles shown');
            } catch (error) {
                console.error('Error showing all candles:', error);
            }
        }

        // Центрирование последней свечи
        function centerLastCandle() {
            console.log('Centering last candle...');

            if (!chart || !candleSeries) {
                console.error('Chart or series not available');
                return;
            }

            try {
                // Получаем данные свечей
                const candles = candleSeries.dataByIndex();
                if (!candles || candles.length === 0) {
                    console.error('No candles available for centering');
                    return;
                }

                // Получаем размер контейнера
                const container = document.getElementById('chart-container');
                if (!container) {
                    console.error('Chart container not found');
                    return;
                }

                const containerWidth = container.clientWidth;

                // Получаем индекс последней видимой свечи (без учета дополнительных)
                const lastVisibleCandleIndex = Math.min(visibleCandlesCount, allCandles.length) - 1;

                // Рассчитываем количество свечей, которые должны быть видны
                const barSpacing = 6; // Расстояние между свечами в пикселях
                const visibleBarsCount = Math.floor(containerWidth / barSpacing);

                // Рассчитываем, сколько свечей должно быть слева и справа от центральной
                const barsToLeft = Math.floor(visibleBarsCount * 0.5); // 50% слева
                const barsToRight = Math.floor(visibleBarsCount * 0.5); // 50% справа

                // Устанавливаем видимый диапазон так, чтобы последняя свеча была в центре
                const fromIndex = Math.max(0, lastVisibleCandleIndex - barsToLeft);
                const toIndex = lastVisibleCandleIndex + barsToRight;

                console.log('Setting visible range:', {
                    from: fromIndex,
                    to: toIndex,
                    lastVisibleCandleIndex: lastVisibleCandleIndex
                });

                // Устанавливаем видимый диапазон
                chart.timeScale().setVisibleLogicalRange({
                    from: fromIndex,
                    to: toIndex
                });

                // Показываем индикатор перемещения
                showDragIndicator();

                // Отправляем сообщение во Flutter
                sendMessageToFlutter('lastCandleCentered', []);

                console.log('Last candle centered');
            } catch (error) {
                console.error('Error centering last candle:', error);
            }
        }

        // Сброс позиции графика
        function resetChartPosition() {
            console.log('Resetting chart position...');

            // Просто вызываем центрирование последней свечи
            centerLastCandle();

            // Показываем индикатор перемещения
            showDragIndicator();

            // Отправляем сообщение во Flutter
            sendMessageToFlutter('chartPositionReset', []);
        }

        // Установка точки входа
        function setEntryPoint() {
            console.log('Setting entry point...');

            if (!allCandles || allCandles.length < visibleCandlesCount) {
                console.error('Not enough candles for entry point');
                return;
            }

            try {
                // Точка входа - последняя видимая свеча (243-я)
                const entryCandle = allCandles[visibleCandlesCount - 1];
                if (!entryCandle) {
                    console.error('Entry candle not found');
                    return;
                }

                entryPointPrice = entryCandle.close;
                entryPointTime = entryCandle.time;

                console.log('Entry point set at price:', entryPointPrice);

                // Удаляем предыдущую линию, если она есть
                if (horizontalLine) {
                    candleSeries.removePriceLine(horizontalLine);
                }

                // Создаем горизонтальную линию для точки входа
                horizontalLine = candleSeries.createPriceLine({
                    price: entryPointPrice,
                    color: 'rgba(255, 255, 255, 0.7)',
                    lineWidth: 1,
                    lineStyle: LightweightCharts.LineStyle.Dashed,
                    axisLabelVisible: true,
                    title: 'Entry',
                });

                // Сообщаем Flutter, что точка входа установлена
                sendMessageToFlutter('entryPointSet', [entryPointPrice, entryPointTime]);
            } catch (error) {
                console.error('Error setting entry point:', error);
            }
        }

        // Определение результата
        function determineResult() {
            console.log('Determining result...');

            if (!entryPointPrice) {
                console.error('Entry point not set');
                return;
            }

            if (allCandles.length < visibleCandlesCount + 7) {
                console.error('Not enough candles for result');
                return;
            }

            try {
                // Берем 7-ю свечу после точки входа
                const resultCandle = allCandles[visibleCandlesCount + 6];
                if (!resultCandle) {
                    console.error('Result candle not found');
                    return;
                }

                // Сравниваем цену закрытия с ценой входа
                const priceChange = resultCandle.close - entryPointPrice;
                const percentChange = (priceChange / entryPointPrice) * 100;
                const isUp = priceChange > 0;

                console.log('Result determined:', {
                    isUp: isUp,
                    percentChange: percentChange.toFixed(2) + '%',
                    finalPrice: resultCandle.close
                });

                // Показываем всплывающее окно с результатом
                showResultPopup(isUp, percentChange);

                // Сообщаем Flutter о результате
                sendMessageToFlutter('tradeResult', [
                    isUp,
                    percentChange,
                    resultCandle.close
                ]);
            } catch (error) {
                console.error('Error determining result:', error);
            }
        }

        // Показать всплывающее окно с результатом
        function showResultPopup(isSuccess, percentChange) {
            const popup = document.getElementById('result-popup');
            const statusElement = document.getElementById('result-status');
            const profitElement = document.getElementById('result-profit');

            if (!popup || !statusElement || !profitElement) {
                console.error('Result popup elements not found');
                return;
            }

            // Устанавливаем статус
            statusElement.innerHTML = isSuccess ?
                '<span class="result-success">Правильный выбор</span>' :
                '<span class="result-failure">Неправильный выбор</span>';

            // Форматируем процент изменения
            const formattedPercent = Math.abs(percentChange).toFixed(2) + '%';

            // Устанавливаем прибыль/убыток
            profitElement.innerHTML = percentChange >= 0 ?
                '+' + formattedPercent :
                '-' + formattedPercent;
            profitElement.className = 'result-profit ' +
                (percentChange >= 0 ? 'profit-positive' : 'profit-negative');

            // Показываем всплывающее окно
            popup.style.display = 'block';

            // Скрываем через 5 секунд
            setTimeout(() => {
                popup.style.display = 'none';
            }, 5000);
        }

        // Очистка всех элементов графика
        function clearChartElements() {
            console.log('Clearing chart elements...');

            // Сбрасываем точку входа
            entryPointPrice = null;
            entryPointTime = null;

            // Удаляем горизонтальную линию
            if (horizontalLine) {
                candleSeries.removePriceLine(horizontalLine);
                horizontalLine = null;
            }

            // Скрываем всплывающее окно с результатом
            const popup = document.getElementById('result-popup');
            if (popup) {
                popup.style.display = 'none';
            }
        }

        // Отправка сообщения в Flutter
        function sendMessageToFlutter(handler, args) {
            try {
                const message = JSON.stringify({
                    handler: handler,
                    args: args
                });

                if (window.flutter_inappwebview) {
                    // Для мобильных платформ
                    window.flutter_inappwebview.postMessage(message);
                } else if (window.parent && window.parent !== window) {
                    // Для веб-платформы (iframe)
                    window.parent.postMessage(message, '*');
                } else {
                    console.log('Flutter interface not available');
                }
            } catch (error) {
                console.error('Error sending message to Flutter:', error);
            }
        }
    </script>
</body>
</html>
