<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Super Simple Chart</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #131722;
            color: #d1d4dc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        #chart-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            cursor: grab;
        }

        #chart-container:active {
            cursor: grabbing;
        }

        #entry-point-label {
            position: absolute;
            right: 10px;
            top: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            color: #fff;
            z-index: 100;
        }

        #result-popup {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background-color: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            padding: 20px;
            color: white;
            text-align: center;
            display: none;
            z-index: 1000;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
        }

        .result-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .result-profit {
            font-size: 24px;
            font-weight: bold;
            margin-top: 10px;
        }

        .result-profit.positive {
            color: #26a69a;
        }

        .result-profit.negative {
            color: #ef5350;
        }
    </style>
</head>
<body>
    <div id="chart-container"></div>
    <div id="entry-point-label"></div>
    <div id="result-popup">
        <div class="result-title">Result</div>
        <div id="result-status"></div>
        <div id="result-profit" class="result-profit"></div>
    </div>

    <script src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
    <script>
        // Global variables
        let chart;
        let candleSeries;
        let allCandles = [];
        let futureCandlesCount = 7; // Количество будущих свечей, которые не показываются на старте
        let entryPointIndex = null; // Индекс свечи точки входа
        let entryPointPrice = null;
        let entryPointTime = null;
        let horizontalLine = null;
        let extraCandlesCount = 300;
        let chartInitialized = false;
        let candlesLoaded = false;

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Initializing chart with SUPER SIMPLE approach');
            initChart();
        });

        // Handle messages from Flutter
        window.addEventListener('message', function(event) {
            console.log('Received message:', event.data);
            try {
                const message = JSON.parse(event.data);

                switch (message.action) {
                    case 'loadCandles':
                        loadCandles(message.data);
                        break;
                    case 'showInitialCandles':
                        showInitialCandles();
                        break;
                    case 'showAllCandles':
                        showAllCandles();
                        break;
                    case 'setEntryPoint':
                        setEntryPoint();
                        break;
                    case 'determineResult':
                        determineResult(message.direction);
                        break;
                    case 'clearChartElements':
                        clearChartElements();
                        break;
                }
            } catch (e) {
                console.error('Error processing message:', e);
            }
        });

        // Initialize chart
        function initChart() {
            console.log('Container dimensions:');
            const container = document.getElementById('chart-container');

            chart = LightweightCharts.createChart(container, {
                width: container.clientWidth,
                height: container.clientHeight,
                layout: {
                    backgroundColor: '#131722',
                    textColor: '#d1d4dc',
                },
                grid: {
                    vertLines: {
                        color: 'rgba(42, 46, 57, 0.5)',
                    },
                    horzLines: {
                        color: 'rgba(42, 46, 57, 0.5)',
                    },
                },
                timeScale: {
                    timeVisible: true,
                    secondsVisible: false,
                    borderColor: '#2a2e39',
                },
                rightPriceScale: {
                    borderColor: '#2a2e39',
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                },
                handleScroll: true,
                handleScale: true,
            });
            console.log('Chart created successfully');

            // Create candlestick series
            candleSeries = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });
            console.log('Candle series created successfully');

            // Handle window resize
            window.addEventListener('resize', function() {
                if (chart) {
                    chart.resize(
                        container.clientWidth,
                        container.clientHeight
                    );
                }
            });

            chartInitialized = true;
            console.log('Chart initialization complete');

            // Notify Flutter that chart is initialized
            sendMessageToFlutter('chartInitialized', []);
        }

        // Load candles data
        function loadCandles(candles) {
            console.log('Loading candles');

            try {
                allCandles = candles;
                console.log('Loaded ' + allCandles.length + ' candles');

                candlesLoaded = true;

                // Notify Flutter that candles are loaded
                sendMessageToFlutter('candlesLoaded', [allCandles.length]);
            } catch (e) {
                console.error('Error loading candles:', e);
            }
        }

        // Show only initial candles (all except last 7)
        function showInitialCandles() {
            if (!chart || !candleSeries || !allCandles.length) return;

            console.log('Showing initial candles with new centering approach');

            // Вычисляем индекс точки входа (последняя видимая свеча)
            entryPointIndex = allCandles.length - futureCandlesCount - 1;
            console.log('Entry point index: ' + entryPointIndex);

            // Получаем начальные свечи (все кроме последних 7)
            const initialCandles = allCandles.slice(0, allCandles.length - futureCandlesCount);
            console.log('Using ' + initialCandles.length + ' initial candles');

            // Create extra candles for right scrolling space
            const lastCandle = initialCandles[initialCandles.length - 1];
            const extraCandles = createExtraCandles(lastCandle, extraCandlesCount);
            console.log('Created ' + extraCandles.length + ' extra candles for right scrolling');

            // Combine initial candles with extra candles
            const extendedCandles = [...initialCandles, ...extraCandles];
            console.log('Total extended candles: ' + extendedCandles.length);

            // Set data to chart
            candleSeries.setData(extendedCandles);

            // Fit content and center the chart
            chart.timeScale().fitContent();

            // Scroll to position where the last real candle is visible
            setTimeout(() => {
                const visibleLogicalRange = chart.timeScale().getVisibleLogicalRange();
                if (visibleLogicalRange) {
                    const rangeWidth = visibleLogicalRange.to - visibleLogicalRange.from;
                    const targetPosition = initialCandles.length - 1 - rangeWidth * 0.15;
                    chart.timeScale().scrollToPosition(targetPosition, false);
                }
            }, 50);

            console.log('Initial candles shown with new centering approach');

            // Notify Flutter
            sendMessageToFlutter('initialCandlesShown', []);

            // Автоматически устанавливаем точку входа
            setEntryPoint();
        }

        // Show all candles (including the 7 future candles)
        function showAllCandles() {
            if (!chart || !candleSeries || !allCandles.length) return;

            console.log('Showing all candles with new centering approach');

            // Create extra candles for right scrolling space
            const lastCandle = allCandles[allCandles.length - 1];
            const extraCandles = createExtraCandles(lastCandle, extraCandlesCount);
            console.log('Created ' + extraCandles.length + ' extra candles for right scrolling');

            // Combine all candles with extra candles
            const extendedCandles = [...allCandles, ...extraCandles];
            console.log('Total extended candles: ' + extendedCandles.length);

            // Set data to chart
            candleSeries.setData(extendedCandles);

            // Fit content and center the chart
            chart.timeScale().fitContent();

            // Scroll to position where the entry point and future candles are visible
            setTimeout(() => {
                const visibleLogicalRange = chart.timeScale().getVisibleLogicalRange();
                if (visibleLogicalRange) {
                    const rangeWidth = visibleLogicalRange.to - visibleLogicalRange.from;
                    // Position to show entry point and future candles
                    const targetPosition = entryPointIndex - rangeWidth * 0.3;
                    chart.timeScale().scrollToPosition(targetPosition, false);
                }
            }, 50);

            console.log('All candles shown with new centering approach');

            // Notify Flutter
            sendMessageToFlutter('allCandlesShown', []);
        }

        // Create extra candles for right scrolling space
        function createExtraCandles(lastCandle, count) {
            const extraCandles = [];
            let lastTime = lastCandle.time;
            let lastClose = lastCandle.close;

            // Calculate time interval based on the last two candles
            const timeInterval = 3600; // Default to 1 hour

            for (let i = 0; i < count; i++) {
                lastTime += timeInterval;

                // Create a flat candle with the same price
                const extraCandle = {
                    time: lastTime,
                    open: lastClose,
                    high: lastClose,
                    low: lastClose,
                    close: lastClose
                };

                extraCandles.push(extraCandle);
            }

            return extraCandles;
        }

        // Set entry point at the last visible candle
        function setEntryPoint() {
            if (!chart || !candleSeries || !allCandles.length) return;

            console.log('Setting entry point');

            // Clear previous entry point if exists
            if (horizontalLine) {
                candleSeries.removePriceLine(horizontalLine);
                horizontalLine = null;
            }

            // Проверяем, что индекс точки входа установлен
            if (entryPointIndex === null) {
                entryPointIndex = allCandles.length - futureCandlesCount - 1;
            }

            // Get entry point candle
            const entryCandle = allCandles[entryPointIndex];

            if (!entryCandle) {
                console.error('Entry candle not found at index', entryPointIndex);
                return;
            }

            // Set entry point price and time
            entryPointPrice = entryCandle.close;
            entryPointTime = entryCandle.time;

            console.log('Entry point set at price:', entryPointPrice, 'at index:', entryPointIndex);

            // Add horizontal line at entry price
            horizontalLine = candleSeries.createPriceLine({
                price: entryPointPrice,
                color: '#2196F3',
                lineWidth: 2,
                lineStyle: LightweightCharts.LineStyle.Solid,
                axisLabelVisible: true,
                title: 'Entry',
            });

            // Update entry point label
            document.getElementById('entry-point-label').textContent = 'Entry: ' + entryPointPrice.toFixed(2);

            // Notify Flutter about entry point
            sendMessageToFlutter('entryPointSet', [entryPointPrice, entryPointTime]);
        }

        // Determine trade result
        function determineResult(direction) {
            if (!entryPointPrice || !entryPointTime || entryPointIndex === null) return;

            try {
                // Получаем свечу результата (точка входа + 7 свечей)
                const resultIndex = entryPointIndex + futureCandlesCount;
                const resultCandle = allCandles[resultIndex];

                if (!resultCandle) {
                    console.error('Result candle not found at index', resultIndex);
                    return;
                }

                console.log('Result determined using candle at index:', resultIndex, 'price:', resultCandle.close);

                // Compare closing price with entry price
                const priceChange = resultCandle.close - entryPointPrice;
                const percentChange = (priceChange / entryPointPrice) * 100;
                const isUp = priceChange > 0;
                const isWin = (direction === 'buy' && isUp) || (direction === 'sell' && !isUp);

                // Show result popup
                showResultPopup(isWin, percentChange);

                // Notify Flutter about result
                sendMessageToFlutter('tradeResult', [
                    isUp,
                    percentChange,
                    resultCandle.close,
                    isWin
                ]);

                // Add markers for entry and result
                const entryMarker = {
                    time: entryPointTime,
                    position: 'belowBar',
                    color: '#2196F3',
                    shape: 'circle',
                    text: 'ENTRY'
                };

                const resultMarker = {
                    time: resultCandle.time,
                    position: 'aboveBar',
                    color: isWin ? '#26a69a' : '#ef5350',
                    shape: 'circle',
                    text: isWin ? 'WIN' : 'LOSS'
                };

                candleSeries.setMarkers([entryMarker, resultMarker]);

            } catch (e) {
                console.error('Error determining result:', e);
            }
        }

        // Show result popup
        function showResultPopup(isWin, percentChange) {
            const popup = document.getElementById('result-popup');
            const status = document.getElementById('result-status');
            const profit = document.getElementById('result-profit');

            status.textContent = isWin ? 'Correct Prediction!' : 'Wrong Prediction!';
            profit.textContent = percentChange.toFixed(2) + '%';
            profit.className = 'result-profit ' + (percentChange >= 0 ? 'positive' : 'negative');

            popup.style.display = 'block';

            // Hide popup after 3 seconds
            setTimeout(() => {
                popup.style.display = 'none';
            }, 3000);
        }

        // Clear chart elements
        function clearChartElements() {
            console.log('Clearing chart elements');

            // Remove horizontal line
            if (horizontalLine && candleSeries) {
                candleSeries.removePriceLine(horizontalLine);
                horizontalLine = null;
            }

            // Reset entry point
            entryPointPrice = null;
            entryPointTime = null;

            // Clear entry point label
            document.getElementById('entry-point-label').textContent = '';

            // Hide result popup
            document.getElementById('result-popup').style.display = 'none';
        }

        // Send message to Flutter
        function sendMessageToFlutter(handler, args) {
            try {
                if (window.flutter_inappwebview) {
                    // For mobile platforms
                    window.flutter_inappwebview.callHandler(handler, ...args);
                } else if (window.FlutterChannel) {
                    // For WebView on mobile
                    window.FlutterChannel.postMessage(JSON.stringify({
                        handler: handler,
                        args: args
                    }));
                } else if (window.parent && window.parent !== window) {
                    // For web platform (iframe)
                    window.parent.postMessage(JSON.stringify({
                        handler: handler,
                        args: args
                    }), '*');
                } else {
                    console.log('Flutter interface not available');
                }
            } catch (e) {
                console.error('Error sending message to Flutter:', e);
            }
        }
    </script>
</body>
</html>
