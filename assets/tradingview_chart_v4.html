<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>TradingView Chart v4</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #131722;
            color: #d1d4dc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        #chart-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        #status {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 1000;
            display: none;
        }

        #result-popup {
            position: absolute;
            top: 50%;
            left: 25%;
            transform: translate(-50%, -50%);
            background-color: rgba(30, 34, 45, 0.9);
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            color: white;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            min-width: 200px;
            display: none;
        }

        .result-title { font-size: 18px; font-weight: bold; margin-bottom: 12px; }
        .result-success { color: #4CAF50; }
        .result-failure { color: #F44336; }
        .result-profit { font-size: 20px; font-weight: bold; margin: 12px 0; }
        .profit-positive { color: #4CAF50; }
        .profit-negative { color: #F44336; }
    </style>
</head>
<body>
    <div id="chart-container"></div>
    <div id="status"></div>
    <div id="result-popup">
        <div class="result-title">Результат</div>
        <div id="result-status"></div>
        <div id="result-profit" class="result-profit"></div>
    </div>

    <!-- Используем версию 4.1.0 вместо 3.8.0 -->
    <script src="https://unpkg.com/lightweight-charts@4.1.0/dist/lightweight-charts.standalone.production.js"></script>
    <script>
        // Глобальные переменные
        let chart;
        let candleSeries;
        let allCandles = [];
        let visibleCandlesCount = 243;
        let entryPointPrice = null;
        let entryPointTime = null;
        let horizontalLine = null;
        let extraCandlesCount = 300;

        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing chart with v4.1.0...');
            initChart();
        });

        // Обработчик сообщений от Flutter
        window.addEventListener('message', function(event) {
            console.log('Received message:', event.data);
            try {
                const message = JSON.parse(event.data);

                switch (message.action) {
                    case 'loadCandles':
                        loadCandles(message.data);
                        break;
                    case 'showInitialCandles':
                        showInitialCandles();
                        break;
                    case 'showAllCandles':
                        showAllCandles();
                        break;
                    case 'setEntryPoint':
                        setEntryPoint();
                        break;
                    case 'determineResult':
                        determineResult();
                        break;
                    case 'clearChartElements':
                        clearChartElements();
                        break;
                    case 'resetChartPosition':
                        resetChartPosition();
                        break;
                    case 'centerLastCandle':
                        centerLastCandle();
                        break;
                }
            } catch (e) {
                console.error('Error processing message:', e);
            }
        });

        // Показать статус
        function showStatus(message, duration = 3000) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.style.display = 'block';

            setTimeout(() => {
                status.style.display = 'none';
            }, duration);
        }

        // Инициализация графика с версией 4.1.0
        function initChart() {
            console.log('Initializing chart with v4.1.0');
            const container = document.getElementById('chart-container');

            // Создаем график с настройками для версии 4.1.0
            chart = LightweightCharts.createChart(container, {
                width: container.clientWidth,
                height: container.clientHeight,
                layout: {
                    background: { color: '#131722' },
                    textColor: '#d1d4dc',
                },
                grid: {
                    vertLines: { color: 'rgba(42, 46, 57, 0.5)' },
                    horzLines: { color: 'rgba(42, 46, 57, 0.5)' },
                },
                timeScale: {
                    timeVisible: true,
                    secondsVisible: false,
                    barSpacing: 6,
                    rightOffset: 5,
                    fixRightEdge: false,
                    fixLeftEdge: false,
                    lockVisibleTimeRangeOnResize: false,
                    rightBarStaysOnScroll: false,
                },
                rightPriceScale: {
                    autoScale: true,
                },
                handleScroll: {
                    vertTouchDrag: true,
                    horzTouchDrag: true,
                    mouseWheel: true,
                    pressedMouseMove: true,
                },
                handleScale: {
                    axisPressedMouseMove: true,
                    mouseWheel: true,
                    pinch: true,
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                },
            });

            // Создаем серию свечей
            candleSeries = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });

            // Обработчик изменения размера окна
            window.addEventListener('resize', function() {
                chart.resize(container.clientWidth, container.clientHeight);

                // Центрируем последнюю свечу при изменении размера
                setTimeout(centerLastCandle, 100);
            });

            // Сообщаем Flutter, что график инициализирован
            sendMessageToFlutter('chartInitialized', []);
            showStatus('Chart initialized with v4.1.0');
        }

        // Загрузка свечей
        function loadCandles(candles) {
            console.log('Loading candles');
            try {
                allCandles = Array.isArray(candles) ? candles : JSON.parse(candles);
                console.log(`Loaded ${allCandles.length} candles`);

                // Показываем начальные свечи
                showInitialCandles();

                // Сообщаем Flutter, что свечи загружены
                sendMessageToFlutter('candlesLoaded', [allCandles.length]);
                showStatus(`Loaded ${allCandles.length} candles`);
            } catch (e) {
                console.error('Error loading candles:', e);
                showStatus('Error loading candles');
            }
        }

        // Создание дополнительных свечей для прокрутки вправо
        function createExtraCandles() {
            if (!allCandles || allCandles.length < 2) return [];

            const extraCandles = [];
            const lastCandle = allCandles[allCandles.length - 1];
            const secondLastCandle = allCandles[allCandles.length - 2];
            const timeStep = lastCandle.time - secondLastCandle.time;

            // Создаем дополнительные свечи с реалистичными данными
            let prevClose = lastCandle.close;
            for (let i = 1; i <= extraCandlesCount; i++) {
                const changePercent = (Math.random() * 4 - 2) * 1.0;
                const change = prevClose * (changePercent / 100);

                const close = prevClose + change;
                const open = prevClose;
                const high = Math.max(open, close) * (1 + Math.random() * 0.01);
                const low = Math.min(open, close) * (1 - Math.random() * 0.01);
                const volume = lastCandle.volume * (0.5 + Math.random());

                extraCandles.push({
                    time: lastCandle.time + timeStep * i,
                    open: open,
                    high: high,
                    low: low,
                    close: close,
                    volume: volume
                });

                prevClose = close;
            }

            console.log(`Created ${extraCandles.length} extra candles`);
            return extraCandles;
        }

        // Показать только первые 243 свечи - новый подход для v4.1.0
        function showInitialCandles() {
            console.log('Showing initial candles with v4.1.0');

            if (!allCandles.length) {
                console.error('No candles available');
                return;
            }

            try {
                // Берем только первые 243 свечи
                const initialCandles = allCandles.slice(0, visibleCandlesCount);

                // Создаем дополнительные свечи для прокрутки вправо
                const extraCandles = createExtraCandles();

                // Объединяем начальные свечи с дополнительными
                const extendedCandles = [...initialCandles, ...extraCandles];

                // Устанавливаем свечи на график
                candleSeries.setData(extendedCandles);

                // Центрируем последнюю свечу с новым подходом для v4.1.0
                setTimeout(() => {
                    // Новый подход для v4.1.0
                    const lastVisibleCandleIndex = Math.min(visibleCandlesCount, allCandles.length) - 1;

                    // Сначала подгоняем весь контент
                    chart.timeScale().fitContent();

                    // Затем устанавливаем видимый диапазон
                    const visibleRange = {
                        from: Math.max(0, lastVisibleCandleIndex - 100),
                        to: lastVisibleCandleIndex + 20
                    };

                    chart.timeScale().setVisibleLogicalRange(visibleRange);

                    // Сообщаем Flutter, что начальные свечи отображены
                    sendMessageToFlutter('initialCandlesShown', []);
                    showStatus('Initial candles shown with v4.1.0');
                }, 100);
            } catch (e) {
                console.error('Error showing initial candles:', e);
                showStatus('Error showing initial candles');
            }
        }

        // Показать все 250 свечей - новый подход для v4.1.0
        function showAllCandles() {
            console.log('Showing all candles with v4.1.0');

            if (!allCandles.length) {
                console.error('No candles available');
                return;
            }

            try {
                // Создаем дополнительные свечи для прокрутки вправо
                const extraCandles = createExtraCandles();

                // Объединяем все свечи с дополнительными
                const extendedCandles = [...allCandles, ...extraCandles];

                // Устанавливаем свечи на график
                candleSeries.setData(extendedCandles);

                // Центрируем последнюю свечу с новым подходом для v4.1.0
                setTimeout(() => {
                    // Новый подход для v4.1.0
                    const lastVisibleCandleIndex = Math.min(visibleCandlesCount, allCandles.length) - 1;

                    // Сначала подгоняем весь контент
                    chart.timeScale().fitContent();

                    // Затем устанавливаем видимый диапазон
                    const visibleRange = {
                        from: Math.max(0, lastVisibleCandleIndex - 100),
                        to: lastVisibleCandleIndex + 20
                    };

                    chart.timeScale().setVisibleLogicalRange(visibleRange);

                    // Сообщаем Flutter, что все свечи отображены
                    sendMessageToFlutter('allCandlesShown', []);
                    showStatus('All candles shown with v4.1.0');
                }, 100);
            } catch (e) {
                console.error('Error showing all candles:', e);
                showStatus('Error showing all candles');
            }
        }

        // Центрирование последней свечи - новый подход для v4.1.0
        function centerLastCandle() {
            console.log('Centering last candle with v4.1.0');

            if (!chart || !candleSeries) {
                console.error('Chart or candleSeries not available');
                return;
            }

            try {
                // Получаем данные свечей
                const candles = candleSeries.data();
                if (!candles || candles.length === 0) {
                    console.error('No candles available');
                    return;
                }

                // Получаем индекс последней видимой свечи
                const lastVisibleCandleIndex = Math.min(visibleCandlesCount, allCandles.length) - 1;

                // Новый подход для v4.1.0
                // Сначала подгоняем весь контент
                chart.timeScale().fitContent();

                // Затем устанавливаем видимый диапазон
                const visibleRange = {
                    from: Math.max(0, lastVisibleCandleIndex - 100),
                    to: lastVisibleCandleIndex + 20
                };

                chart.timeScale().setVisibleLogicalRange(visibleRange);

                // Отправляем сообщение во Flutter
                sendMessageToFlutter('lastCandleCentered', []);
                showStatus('Last candle centered with v4.1.0');
            } catch (e) {
                console.error('Error centering last candle:', e);
                showStatus('Error centering last candle');
            }
        }

        // Сброс позиции графика - новый подход для v4.1.0
        function resetChartPosition() {
            console.log('Resetting chart position with v4.1.0');

            if (!chart || !candleSeries) {
                console.error('Chart or candleSeries not available');
                return;
            }

            try {
                // Очищаем все элементы графика
                clearChartElements();

                // Новый подход для v4.1.0
                // Сначала подгоняем весь контент
                chart.timeScale().fitContent();

                // Затем устанавливаем видимый диапазон
                const lastVisibleCandleIndex = Math.min(visibleCandlesCount, allCandles.length) - 1;
                const visibleRange = {
                    from: Math.max(0, lastVisibleCandleIndex - 100),
                    to: lastVisibleCandleIndex + 20
                };

                chart.timeScale().setVisibleLogicalRange(visibleRange);

                // Отправляем сообщение во Flutter
                sendMessageToFlutter('chartPositionReset', []);
                showStatus('Chart position reset with v4.1.0');
            } catch (e) {
                console.error('Error resetting chart position:', e);
                showStatus('Error resetting chart position');
            }
        }

        // Установка точки входа - новый подход для v4.1.0
        function setEntryPoint() {
            console.log('Setting entry point with v4.1.0');

            if (!allCandles || allCandles.length < visibleCandlesCount) {
                console.error('Not enough candles for entry point');
                return;
            }

            try {
                // Точка входа - последняя видимая свеча (243-я)
                const entryCandle = allCandles[visibleCandlesCount - 1];
                if (!entryCandle) {
                    console.error('Entry candle not found');
                    return;
                }

                entryPointPrice = entryCandle.close;
                entryPointTime = entryCandle.time;

                console.log('Entry point set at price:', entryPointPrice);

                // Удаляем предыдущую линию, если она есть
                if (horizontalLine) {
                    candleSeries.removePriceLine(horizontalLine);
                }

                // Создаем горизонтальную линию для точки входа
                horizontalLine = candleSeries.createPriceLine({
                    price: entryPointPrice,
                    color: 'rgba(255, 255, 255, 0.7)',
                    lineWidth: 1,
                    lineStyle: LightweightCharts.LineStyle.Dashed,
                    axisLabelVisible: true,
                    title: 'Entry',
                });

                // Сообщаем Flutter, что точка входа установлена
                sendMessageToFlutter('entryPointSet', [entryPointPrice, entryPointTime]);
                showStatus('Entry point set with v4.1.0');
            } catch (e) {
                console.error('Error setting entry point:', e);
                showStatus('Error setting entry point');
            }
        }

        // Определение результата - новый подход для v4.1.0
        function determineResult() {
            console.log('Determining result with v4.1.0');

            if (!entryPointPrice) {
                console.error('Entry point not set');
                return;
            }

            if (allCandles.length < visibleCandlesCount + 7) {
                console.error('Not enough candles for result');
                return;
            }

            try {
                // Берем 7-ю свечу после точки входа
                const resultCandle = allCandles[visibleCandlesCount + 6];
                if (!resultCandle) {
                    console.error('Result candle not found');
                    return;
                }

                // Сравниваем цену закрытия с ценой входа
                const priceChange = resultCandle.close - entryPointPrice;
                const percentChange = (priceChange / entryPointPrice) * 100;
                const isUp = priceChange > 0;

                console.log('Result determined:', {
                    isUp: isUp,
                    percentChange: percentChange.toFixed(2) + '%',
                    finalPrice: resultCandle.close
                });

                // Показываем всплывающее окно с результатом
                showResultPopup(isUp, percentChange);

                // Сообщаем Flutter о результате
                sendMessageToFlutter('tradeResult', [
                    isUp,
                    percentChange,
                    resultCandle.close
                ]);
            } catch (e) {
                console.error('Error determining result:', e);
                showStatus('Error determining result');
            }
        }

        // Показать всплывающее окно с результатом
        function showResultPopup(isSuccess, percentChange) {
            const popup = document.getElementById('result-popup');
            const statusElement = document.getElementById('result-status');
            const profitElement = document.getElementById('result-profit');

            if (!popup || !statusElement || !profitElement) {
                console.error('Result popup elements not found');
                return;
            }

            // Устанавливаем статус
            statusElement.innerHTML = isSuccess ?
                '<span class="result-success">Правильный выбор</span>' :
                '<span class="result-failure">Неправильный выбор</span>';

            // Форматируем процент изменения
            const formattedPercent = Math.abs(percentChange).toFixed(2) + '%';

            // Устанавливаем прибыль/убыток
            profitElement.innerHTML = percentChange >= 0 ?
                '+' + formattedPercent :
                '-' + formattedPercent;
            profitElement.className = 'result-profit ' +
                (percentChange >= 0 ? 'profit-positive' : 'profit-negative');

            // Показываем всплывающее окно
            popup.style.display = 'block';

            // Скрываем через 5 секунд
            setTimeout(() => {
                popup.style.display = 'none';
            }, 5000);
        }

        // Очистка всех элементов графика
        function clearChartElements() {
            console.log('Clearing chart elements with v4.1.0');

            // Сбрасываем точку входа
            entryPointPrice = null;
            entryPointTime = null;

            // Удаляем горизонтальную линию
            if (horizontalLine) {
                candleSeries.removePriceLine(horizontalLine);
                horizontalLine = null;
            }

            // Скрываем всплывающее окно с результатом
            const popup = document.getElementById('result-popup');
            if (popup) {
                popup.style.display = 'none';
            }
        }

        // Отправка сообщения в Flutter
        function sendMessageToFlutter(handler, args) {
            try {
                const message = JSON.stringify({
                    handler: handler,
                    args: args
                });

                if (window.flutter_inappwebview) {
                    // Для мобильных платформ
                    window.flutter_inappwebview.postMessage(message);
                } else if (window.parent && window.parent !== window) {
                    // Для веб-платформы (iframe)
                    window.parent.postMessage(message, '*');
                } else {
                    console.log('Flutter interface not available');
                }
            } catch (e) {
                console.error('Error sending message to Flutter:', e);
            }
        }
    </script>
</body>
</html>
