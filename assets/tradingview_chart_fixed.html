<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView Chart</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #131722;
            color: #d1d4dc;
            overflow: hidden;
        }

        #chart-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }

        /* Стили для всплывающего окна с результатами */
        #result-popup {
            position: absolute; /* Изменено с fixed на absolute для правильного позиционирования */
            top: 50%;
            left: 25%;
            transform: translate(-50%, -50%);
            background-color: rgba(30, 34, 45, 0.95);
            border-radius: 12px; /* Увеличен радиус скругления */
            padding: 24px; /* Увеличены отступы */
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.6); /* Усилена тень */
            color: white;
            font-size: 20px; /* Увеличен размер шрифта */
            text-align: center;
            z-index: 9999;
            min-width: 280px; /* Увеличена минимальная ширина */
            border: 1px solid rgba(255, 255, 255, 0.3); /* Усилена граница */
            display: none;
            opacity: 0; /* Начальная прозрачность для анимации */
            transition: opacity 0.3s ease-in-out; /* Анимация появления/исчезновения */
        }

        .result-title {
            font-size: 22px;
            font-weight: bold;
            margin-bottom: 18px;
            text-transform: uppercase; /* Заголовок заглавными буквами */
            letter-spacing: 1px; /* Увеличен интервал между буквами */
        }

        .result-success {
            color: #4CAF50;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(76, 175, 80, 0.5); /* Добавлено свечение */
        }

        .result-failure {
            color: #F44336;
            font-weight: bold;
            text-shadow: 0 0 10px rgba(244, 67, 54, 0.5); /* Добавлено свечение */
        }

        .result-profit {
            font-size: 28px; /* Увеличен размер шрифта */
            font-weight: bold;
            margin: 18px 0;
        }

        .profit-positive {
            color: #4CAF50;
            text-shadow: 0 0 10px rgba(76, 175, 80, 0.5); /* Добавлено свечение */
        }

        .profit-negative {
            color: #F44336;
            text-shadow: 0 0 10px rgba(244, 67, 54, 0.5); /* Добавлено свечение */
        }
    </style>
</head>
<body>
    <div id="chart-container"></div>
    <div id="chart-controls" style="position: absolute; top: 10px; right: 10px; z-index: 1000;">
        <button id="reset-position-btn" onclick="resetChartPosition()" style="background-color: rgba(30, 30, 30, 0.8); color: white; border: 1px solid rgba(100, 100, 100, 0.8); border-radius: 4px; padding: 8px 12px; cursor: pointer; font-weight: bold; box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3); transition: all 0.2s ease;">Сбросить позицию</button>
    </div>
    <div id="result-popup">
        <div class="result-title">Результат</div>
        <div id="result-status"></div>
        <div id="result-profit" class="result-profit"></div>
    </div>

    <script src="https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js"></script>
    <script>
        // Глобальные переменные
        let chart;
        let candleSeries;
        let entryPointPrice = null;
        let entryPointTime = null;
        let horizontalLine = null;
        let verticalLine = null;
        let allCandles = []; // Все свечи (250)
        let visibleCandlesCount = 243; // Количество видимых свечей
        let entryPointSet = false; // Флаг установки точки входа
        let userHasMovedChart = false; // Флаг для отслеживания перемещения графика пользователем

        // Инициализация графика
        function initChart() {
            console.log('Initializing chart...');
            const container = document.getElementById('chart-container');

            // Создаем график с поддержкой перемещения и масштабирования
            chart = LightweightCharts.createChart(container, {
                width: container.offsetWidth,
                height: container.offsetHeight,
                layout: {
                    backgroundColor: '#131722',
                    textColor: '#d1d4dc',
                },
                grid: {
                    vertLines: {
                        color: 'rgba(42, 46, 57, 0.5)',
                    },
                    horzLines: {
                        color: 'rgba(42, 46, 57, 0.5)',
                    },
                },
                timeScale: {
                    timeVisible: true,
                    secondsVisible: false,
                    borderColor: '#2a2e39',
                    // Настройки для перемещения
                    lockVisibleTimeRangeOnResize: false,
                    rightBarStaysOnScroll: false,
                    fixLeftEdge: false,
                    fixRightEdge: false,
                },
                rightPriceScale: {
                    borderColor: '#2a2e39',
                    autoScale: true,
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                },
                // Включаем перемещение графика - ВАЖНО для перемещения
                handleScroll: true,
                // Включаем масштабирование - ВАЖНО для масштабирования
                handleScale: true,
            });

            // Создаем серию свечей
            candleSeries = chart.addCandlestickSeries({
                upColor: '#4CAF50',
                downColor: '#F44336',
                borderDownColor: '#F44336',
                borderUpColor: '#4CAF50',
                wickDownColor: '#F44336',
                wickUpColor: '#4CAF50',
            });

            // Обработчик изменения размера окна
            window.addEventListener('resize', () => {
                if (chart) {
                    // Изменяем размер графика
                    chart.resize(
                        container.offsetWidth,
                        container.offsetHeight
                    );

                    // Центрируем последнюю свечу только если пользователь не перемещал график
                    if (!userHasMovedChart) {
                        // Даем небольшую задержку для завершения изменения размера
                        setTimeout(() => {
                            centerLastCandle();
                        }, 100);
                    }
                }
            });

            // Добавляем обработчики событий для отслеживания перемещения графика

            // 1. Отслеживаем изменение видимого диапазона времени
            chart.timeScale().subscribeVisibleTimeRangeChange(() => {
                userHasMovedChart = true;
                console.log('User has moved chart: timeScale change');
            });

            // 2. Отслеживаем нажатие мыши для перемещения
            container.addEventListener('mousedown', (e) => {
                // Проверяем, что это не клик по кнопке
                if (e.target.tagName !== 'BUTTON') {
                    userHasMovedChart = true;
                    console.log('User has moved chart: mouse down');
                }
            });

            // 3. Отслеживаем касание для перемещения на мобильных устройствах
            container.addEventListener('touchstart', (e) => {
                // Проверяем, что это не касание кнопки
                if (e.target.tagName !== 'BUTTON') {
                    userHasMovedChart = true;
                    console.log('User has moved chart: touch start');
                }
            });

            // 4. Отслеживаем колесо мыши для масштабирования
            container.addEventListener('wheel', () => {
                userHasMovedChart = true;
                console.log('User has moved chart: wheel');
            }, { passive: true });

            // Инициализируем обработчик сообщений от Flutter
            initMessageHandler();

            console.log('Chart initialized');
        }

        // Инициализация обработчика сообщений
        function initMessageHandler() {
            // Удаляем все существующие обработчики сообщений перед добавлением нового
            // для предотвращения дублирования
            const oldHandler = window._messageHandler;
            if (oldHandler) {
                window.removeEventListener('message', oldHandler);
                console.log('Removed old message handler');
            }

            // Создаем новый обработчик и сохраняем ссылку на него
            const newHandler = function(event) {
                console.log('Received message from parent:', event.data);
                try {
                    const message = JSON.parse(event.data);

                    switch (message.action) {
                        case 'loadCandles':
                            loadCandles(message.data);
                            break;
                        case 'showAllCandles':
                            showAllCandles();
                            break;
                        case 'setEntryPoint':
                            setEntryPoint();
                            break;
                        case 'determineResult':
                            determineResult();
                            break;
                        case 'clearChartElements':
                            clearChartElements();
                            break;
                        case 'resetChartPosition':
                            resetChartPosition();
                            break;
                        case 'centerLastCandle':
                            centerLastCandle();
                            break;
                        default:
                            console.log('Unknown action:', message.action);
                    }
                } catch (e) {
                    console.error('Error processing message:', e);
                }
            };

            // Сохраняем ссылку на обработчик
            window._messageHandler = newHandler;

            // Добавляем новый обработчик
            window.addEventListener('message', newHandler);

            // Сообщаем Flutter, что график готов
            sendMessageToFlutter('chartReady', []);
        }

        // Отправка сообщения в Flutter
        function sendMessageToFlutter(action, data) {
            console.log('Sending message to Flutter:', action, data);
            const message = {
                action: action,
                data: data
            };

            try {
                if (window.FlutterChannel) {
                    window.FlutterChannel.postMessage(JSON.stringify(message));
                } else if (window.parent) {
                    window.parent.postMessage(JSON.stringify(message), '*');
                } else {
                    console.log('No channel to send message to Flutter');
                }
            } catch (e) {
                console.error('Error sending message to Flutter:', e);
            }
        }

        // Загрузка свечей
        function loadCandles(candles) {
            console.log('Loading candles:', candles.length);

            // Сохраняем все свечи
            allCandles = candles;

            // Сбрасываем точку входа
            entryPointPrice = null;
            entryPointTime = null;
            entryPointSet = false;

            // Сбрасываем флаг перемещения графика
            userHasMovedChart = false;

            // Очищаем все элементы графика
            clearChartElements();

            // Показываем только первые 243 свечи
            showInitialCandles();

            // Сообщаем Flutter, что свечи загружены
            sendMessageToFlutter('candlesLoaded', [candles.length]);
        }

        // Показать только первые 243 свечи
        function showInitialCandles() {
            if (!chart || !candleSeries || allCandles.length === 0) {
                console.error('Cannot show initial candles: chart not ready or no candles');
                return;
            }

            console.log('Showing initial candles (243)');

            try {
                // Берем только первые 243 свечи
                const initialCandles = allCandles.slice(0, visibleCandlesCount);

                // Устанавливаем свечи на график
                candleSeries.setData(initialCandles);

                // Центрируем последнюю свечу
                centerLastCandle();

                // Сообщаем Flutter, что начальные свечи отображены
                sendMessageToFlutter('initialCandlesShown', []);
            } catch (error) {
                console.error('Error showing initial candles:', error);
            }
        }

        // Показать все свечи
        function showAllCandles() {
            if (!chart || !candleSeries || allCandles.length === 0) {
                console.error('Cannot show all candles: chart not ready or no candles');
                return;
            }

            console.log('Showing all candles (250)');

            try {
                // Устанавливаем все свечи на график
                candleSeries.setData(allCandles);

                // Центрируем последнюю свечу
                centerLastCandle();

                // Сообщаем Flutter, что все свечи отображены
                sendMessageToFlutter('allCandlesShown', []);
            } catch (error) {
                console.error('Error showing all candles:', error);
            }
        }



        // Центрирование последней свечи
        function centerLastCandle() {
            if (!chart || !allCandles.length) return;

            console.log('Centering last candle');

            try {
                // Получаем последнюю свечу
                const lastCandleIndex = visibleCandlesCount - 1; // Используем индекс последней видимой свечи (243-я)
                const lastCandle = allCandles[lastCandleIndex];
                if (!lastCandle) {
                    console.error('Last candle not found at index:', lastCandleIndex);
                    return;
                }

                // Получаем размер контейнера
                const container = document.getElementById('chart-container');
                const containerWidth = container.clientWidth;

                // Устанавливаем оптимальное расстояние между свечами
                const barSpacing = 10;

                // Рассчитываем, сколько свечей должно быть видно на экране
                const visibleBarsCount = Math.floor(containerWidth / barSpacing);

                // Рассчитываем половину видимых свечей для центрирования
                const halfVisibleBars = Math.floor(visibleBarsCount / 2);

                // Добавляем пустые свечи справа для центрирования
                const emptyCandles = [];
                const timeInterval = lastCandle.time - (allCandles[lastCandleIndex - 1]?.time || lastCandle.time - 3600);

                // Добавляем пустые свечи справа от последней (для возможности прокрутки вправо)
                for (let i = 1; i <= halfVisibleBars * 2; i++) {
                    emptyCandles.push({
                        time: lastCandle.time + timeInterval * i,
                        open: null,
                        high: null,
                        low: null,
                        close: null,
                        volume: 0
                    });
                }

                // Объединяем реальные свечи с пустыми
                const allCandlesWithEmpty = [...allCandles, ...emptyCandles];

                // Устанавливаем все свечи на график
                candleSeries.setData(allCandlesWithEmpty);

                // Отключаем все ограничения прокрутки и масштабирования
                chart.applyOptions({
                    timeScale: {
                        barSpacing: barSpacing,
                        fixRightEdge: false,
                        fixLeftEdge: false,
                        rightBarStaysOnScroll: false,
                        lockVisibleTimeRangeOnResize: false,
                    },
                    handleScroll: true,
                    handleScale: true,
                });

                // Используем комбинированный подход для центрирования

                // Шаг 1: Подгоняем весь график
                chart.timeScale().fitContent();

                // Шаг 2: Устанавливаем видимый диапазон так, чтобы последняя свеча была в центре
                chart.timeScale().setVisibleLogicalRange({
                    from: lastCandleIndex - halfVisibleBars,
                    to: lastCandleIndex + halfVisibleBars,
                });

                // Шаг 3: Дополнительное центрирование с задержкой
                setTimeout(() => {
                    chart.timeScale().scrollToPosition(50, false);
                }, 300);

                console.log('Last candle centered successfully');
            } catch (error) {
                console.error('Error centering last candle:', error);
            }
        }

        // Установка точки входа (вызывается только при нажатии кнопок UP/DOWN)
        function setEntryPoint() {
            if (allCandles.length < visibleCandlesCount) {
                console.error('Not enough candles to set entry point');
                return;
            }

            // Проверяем, не установлена ли уже точка входа
            if (entryPointSet) {
                console.log('Entry point already set, skipping');
                return;
            }

            // Точка входа - последняя видимая свеча (243-я)
            const entryCandle = allCandles[visibleCandlesCount - 1];
            entryPointPrice = entryCandle.close;
            entryPointTime = entryCandle.time;
            entryPointSet = true;

            console.log('Entry point set at candle:', visibleCandlesCount - 1, 'price:', entryPointPrice);

            // Удаляем предыдущие линии
            clearEntryPointElements();

            // Создаем горизонтальную линию для точки входа
            horizontalLine = candleSeries.createPriceLine({
                price: entryPointPrice,
                color: 'rgba(255, 255, 255, 0.7)',
                lineWidth: 1,
                lineStyle: LightweightCharts.LineStyle.Dashed,
                axisLabelVisible: true,
                title: 'Entry',
            });

            // Создаем маркер для точки входа
            candleSeries.setMarkers([{
                time: entryCandle.time,
                position: 'inBar',
                color: 'rgba(255, 255, 255, 0.7)',
                shape: 'circle',
                size: 1
            }]);

            // Создаем вертикальную линию
            const container = document.getElementById('chart-container');
            verticalLine = document.createElement('div');
            verticalLine.id = 'entry-vertical-line';
            verticalLine.style.position = 'absolute';
            verticalLine.style.width = '1px';
            verticalLine.style.height = '100%';
            verticalLine.style.borderLeft = '1px dashed rgba(255, 255, 255, 0.7)';
            verticalLine.style.zIndex = '1000';
            container.appendChild(verticalLine);

            // Позиционируем вертикальную линию
            updateVerticalLinePosition();

            // Обновляем позицию при изменении масштаба
            chart.timeScale().subscribeVisibleTimeRangeChange(updateVerticalLinePosition);

            // Сообщаем Flutter, что точка входа установлена
            sendMessageToFlutter('entryPointSet', [entryPointPrice, entryPointTime]);

            // Центрируем последнюю свечу
            centerLastCandle();
        }

        // Обновление позиции вертикальной линии
        function updateVerticalLinePosition() {
            if (!verticalLine || !entryPointTime) return;

            const timeScale = chart.timeScale();
            const x = timeScale.timeToCoordinate(entryPointTime);

            if (x !== null) {
                verticalLine.style.left = x + 'px';
            }
        }

        // Очистка элементов точки входа
        function clearEntryPointElements() {
            // Удаляем горизонтальную линию
            if (horizontalLine) {
                candleSeries.removePriceLine(horizontalLine);
                horizontalLine = null;
            }

            // Удаляем вертикальную линию
            if (verticalLine) {
                verticalLine.remove();
                verticalLine = null;
            }

            // Удаляем маркеры
            candleSeries.setMarkers([]);
        }

        // Определение результата
        function determineResult() {
            if (!entryPointSet || !entryPointPrice || allCandles.length < visibleCandlesCount + 7) {
                console.error('Cannot determine result: entryPointSet:', entryPointSet,
                              'entryPointPrice:', entryPointPrice,
                              'candles length:', allCandles.length);
                return;
            }

            // Берем 7-ю свечу после точки входа
            const resultCandle = allCandles[visibleCandlesCount + 6];
            console.log('Result determined using candle:', visibleCandlesCount + 6, 'price:', resultCandle.close);

            // Сравниваем цену закрытия с ценой входа
            const priceChange = resultCandle.close - entryPointPrice;
            const percentChange = (priceChange / entryPointPrice) * 100;
            const isUp = priceChange > 0;

            // Показываем всплывающее окно с результатом
            showResultPopup(isUp, percentChange);

            // Сообщаем Flutter о результате
            sendMessageToFlutter('tradeResult', [
                isUp,
                percentChange,
                resultCandle.close
            ]);
        }

        // Показать всплывающее окно с результатом
        function showResultPopup(isSuccess, percentChange) {
            console.log('Showing result popup:', isSuccess, percentChange);

            const popup = document.getElementById('result-popup');
            const statusElement = document.getElementById('result-status');
            const profitElement = document.getElementById('result-profit');

            if (!popup || !statusElement || !profitElement) {
                console.error('Result popup elements not found');
                return;
            }

            // Устанавливаем статус
            if (isSuccess) {
                statusElement.innerHTML = '<span class="result-success">Правильный выбор</span>';
            } else {
                statusElement.innerHTML = '<span class="result-failure">Неправильный выбор</span>';
            }

            // Форматируем процент изменения
            const formattedPercent = Math.abs(percentChange).toFixed(2) + '%';

            // Устанавливаем прибыль/убыток
            if (percentChange >= 0) {
                profitElement.innerHTML = '+' + formattedPercent;
                profitElement.className = 'result-profit profit-positive';
            } else {
                profitElement.innerHTML = '-' + formattedPercent;
                profitElement.className = 'result-profit profit-negative';
            }

            // Позиционируем всплывающее окно в левой половине графика
            const container = document.getElementById('chart-container');
            if (container) {
                const containerWidth = container.clientWidth;
                const containerHeight = container.clientHeight;

                // Позиционируем окно в левой четверти графика
                popup.style.position = 'absolute';
                popup.style.left = (containerWidth * 0.25) + 'px';
                popup.style.top = (containerHeight * 0.5) + 'px';
                popup.style.transform = 'translate(-50%, -50%)';
                popup.style.zIndex = '9999';
            }

            // Показываем всплывающее окно
            popup.style.display = 'block';

            // Добавляем анимацию появления
            popup.style.opacity = '0';
            popup.style.transition = 'opacity 0.3s ease-in-out';

            // Запускаем анимацию появления
            setTimeout(() => {
                popup.style.opacity = '1';
            }, 10);

            // Скрываем всплывающее окно через 5 секунд
            setTimeout(() => {
                // Анимация исчезновения
                popup.style.opacity = '0';

                // Полностью скрываем после завершения анимации
                setTimeout(() => {
                    popup.style.display = 'none';
                }, 300);
            }, 5000);
        }

        // Сброс позиции графика
        function resetChartPosition() {
            console.log('Resetting chart position');

            try {
                // Сбрасываем флаг перемещения графика
                userHasMovedChart = false;

                // Отключаем все ограничения прокрутки и масштабирования
                chart.applyOptions({
                    timeScale: {
                        barSpacing: 10,
                        fixRightEdge: false,
                        fixLeftEdge: false,
                        rightBarStaysOnScroll: false,
                        lockVisibleTimeRangeOnResize: false,
                    },
                    handleScroll: true,
                    handleScale: true,
                });

                // Центрируем последнюю свечу
                centerLastCandle();

                // Сообщаем Flutter, что позиция графика сброшена
                sendMessageToFlutter('chartPositionReset', []);

                console.log('Chart position reset complete');
            } catch (error) {
                console.error('Error resetting chart position:', error);
            }
        }

        // Очистка всех элементов графика
        function clearChartElements() {
            console.log('Clearing all chart elements');

            // Сбрасываем точку входа
            entryPointPrice = null;
            entryPointTime = null;
            entryPointSet = false;

            // Очищаем элементы точки входа
            clearEntryPointElements();

            // Скрываем всплывающее окно с результатом
            const popup = document.getElementById('result-popup');
            if (popup) {
                popup.style.display = 'none';
            }

            // Отписываемся от событий изменения масштаба
            if (chart) {
                chart.timeScale().unsubscribeVisibleTimeRangeChange(updateVerticalLinePosition);
            }

            console.log('All chart elements cleared');
        }

        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', initChart);
    </script>
</body>
</html>
