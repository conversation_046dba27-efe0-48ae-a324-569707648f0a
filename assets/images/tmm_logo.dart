import 'package:flutter/material.dart';

class Tmm<PERSON>ogo extends StatelessWidget {
  final double size;
  final Color color;
  
  const TmmLogo({
    super.key, 
    this.size = 100, 
    this.color = Colors.blue,
  });

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      size: <PERSON><PERSON>(size, size),
      painter: T<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(color: color),
    );
  }
}

class TmmLogoPainter extends CustomPainter {
  final Color color;
  
  TmmLogoPainter({required this.color});
  
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4.0
      ..strokeCap = StrokeCap.round;
    
    // Рисуем синусоиду
    final path = Path();
    
    // Начальная точка
    path.moveTo(size.width * 0.1, size.height * 0.5);
    
    // Первая волна (вверх)
    path.cubicTo(
      size.width * 0.25, size.height * 0.3, // контрольная точка 1
      size.width * 0.35, size.height * 0.3, // контрольная точка 2
      size.width * 0.5, size.height * 0.5, // конечная точка
    );
    
    // Вторая волна (вниз)
    path.cubicTo(
      size.width * 0.65, size.height * 0.7, // контрольная точка 1
      size.width * 0.75, size.height * 0.7, // контрольная точка 2
      size.width * 0.9, size.height * 0.5, // конечная точка
    );
    
    canvas.drawPath(path, paint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
