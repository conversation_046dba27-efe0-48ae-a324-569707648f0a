<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TradingView Lightweight Chart</title>
    <script src="https://unpkg.com/lightweight-charts@3.4.0/dist/lightweight-charts.standalone.production.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: transparent;
            overflow: hidden;
        }
        #chart-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
        }
        #price-info {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 10;
        }
        #entry-price {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(33, 150, 243, 0.3);
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 10;
            border: 1px solid rgba(33, 150, 243, 0.5);
            display: none;
        }
        .up {
            color: #26a69a;
        }
        .down {
            color: #ef5350;
        }
    </style>
</head>
<body>
    <div id="chart-container"></div>
    <div id="price-info">
        <span id="current-price">0.00</span>
        <span id="price-change"></span>
    </div>
    <div id="entry-price">Entry: <span id="entry-value">0.00</span></div>

    <script>
        // Chart configuration
        let chart = null;
        let candleSeries = null;
        let volumeSeries = null;
        let priceLine = null;
        let candles = [];
        let futureCandles = [];
        let showFutureCandles = false;
        let entryPrice = null;
        let showEntryPoint = false;
        let visibleFutureCandles = 0;
        let animationTimer;
        let currentVisibleCandles = 0;

        // Initialize chart
        function initChart() {
            try {
                const chartContainer = document.getElementById('chart-container');

                // Create chart instance with simpler options for better compatibility
                chart = LightweightCharts.createChart(chartContainer, {
                    width: chartContainer.clientWidth,
                    height: chartContainer.clientHeight,
                    layout: {
                        backgroundColor: '#000000',
                        textColor: '#d1d4dc',
                    },
                    grid: {
                        vertLines: { color: '#2B2B43' },
                        horzLines: { color: '#2B2B43' },
                    },
                    crosshair: {
                        mode: LightweightCharts.CrosshairMode.Normal,
                    },
                    rightPriceScale: {
                        borderColor: '#2B2B43',
                    },
                    timeScale: {
                        borderColor: '#2B2B43',
                        timeVisible: true,
                    },
                });

                // Resize chart on window resize
                window.addEventListener('resize', () => {
                    if (chart) {
                        chart.applyOptions({
                            width: chartContainer.clientWidth,
                            height: chartContainer.clientHeight
                        });
                    }
                });

                // Create candlestick series
                candleSeries = chart.addCandlestickSeries({
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                    borderVisible: false,
                    wickUpColor: '#26a69a',
                    wickDownColor: '#ef5350',
                });

                // Create volume series
                volumeSeries = chart.addHistogramSeries({
                    color: 'rgba(56, 142, 255, 0.5)',
                    priceFormat: {
                        type: 'volume',
                    },
                    priceScaleId: '',
                    scaleMargins: {
                        top: 0.8,
                        bottom: 0,
                    },
                });

                console.log("Chart initialized successfully");
            } catch (error) {
                console.error("Error initializing chart:", error);
            }
        }

        // Update chart with new data
        function updateChart(candlesData, entryPriceValue) {
            try {
                if (!chart || !candleSeries || !volumeSeries) {
                    console.log("Chart not initialized, initializing now");
                    initChart();
                    if (!chart || !candleSeries || !volumeSeries) {
                        console.error("Failed to initialize chart");
                        return;
                    }
                }

                // Update candles array
                candles = candlesData || [];
                entryPrice = entryPriceValue;

                if (candles.length === 0) {
                    console.log("No candle data to display");
                    return;
                }

                console.log("Updating chart with", candles.length, "candles");

                // Format candles for TradingView
                const formattedCandles = candles.map(c => ({
                    time: c.time,
                    open: c.open,
                    high: c.high,
                    low: c.low,
                    close: c.close,
                }));

                // Format volumes
                const volumes = candles.map(c => ({
                    time: c.time,
                    value: c.volume || Math.abs(c.close - c.open) * 100, // Use volume or estimate
                    color: c.close >= c.open ? 'rgba(38, 166, 154, 0.5)' : 'rgba(239, 83, 80, 0.5)',
                }));

                // Set data
                candleSeries.setData(formattedCandles);
                volumeSeries.setData(volumes);

                // Update price info
                if (candles.length > 0) {
                    const lastCandle = candles[candles.length - 1];
                    const priceElement = document.getElementById('current-price');
                    const changeElement = document.getElementById('price-change');

                    priceElement.textContent = formatPrice(lastCandle.close);

                    const isUp = lastCandle.close >= lastCandle.open;
                    const changePercent = ((lastCandle.close - lastCandle.open) / lastCandle.open * 100).toFixed(2);

                    changeElement.textContent = ` ${isUp ? '▲' : '▼'} ${Math.abs(changePercent)}%`;
                    changeElement.className = isUp ? 'up' : 'down';
                }

                // Add entry price line if needed
                if (entryPrice !== null && showEntryPoint) {
                    if (priceLine) {
                        candleSeries.removePriceLine(priceLine);
                    }

                    priceLine = candleSeries.createPriceLine({
                        price: entryPrice,
                        color: '#2196F3',
                        lineWidth: 2,
                        lineStyle: 2, // Dashed line
                        title: 'Entry',
                    });

                    // Show entry price info
                    const entryElement = document.getElementById('entry-price');
                    const entryValueElement = document.getElementById('entry-value');
                    entryValueElement.textContent = formatPrice(entryPrice);
                    entryElement.style.display = 'block';
                } else {
                    if (priceLine) {
                        candleSeries.removePriceLine(priceLine);
                        priceLine = null;
                    }

                    // Hide entry price info
                    const entryElement = document.getElementById('entry-price');
                    entryElement.style.display = 'none';
                }

                // Fit content
                chart.timeScale().fitContent();

                console.log("Chart updated successfully");
            } catch (error) {
                console.error("Error updating chart:", error);
            }
        }

        // Format price for display
        function formatPrice(price) {
            if (price > 1000) {
                return price.toFixed(0);
            } else if (price > 100) {
                return price.toFixed(1);
            } else if (price > 1) {
                return price.toFixed(2);
            } else {
                return price.toFixed(4);
            }
        }

        // Show future candles with animation
        function showFutureCandlesWithAnimation(futureCandlesData) {
            try {
                futureCandles = futureCandlesData;
                currentVisibleCandles = candles.length;

                console.log("Starting animation with", futureCandles.length, "future candles");

                // Clear previous animation if any
                if (animationTimer) {
                    clearInterval(animationTimer);
                }

                // Start animation
                animationTimer = setInterval(() => {
                    if (currentVisibleCandles < candles.length + futureCandles.length) {
                        currentVisibleCandles++;

                        // Get all visible candles
                        const visibleCandles = [
                            ...candles,
                            ...futureCandles.slice(0, currentVisibleCandles - candles.length)
                        ];

                        // Update chart with visible candles
                        updateChart(visibleCandles, entryPrice);

                        // If all future candles are shown, stop animation
                        if (currentVisibleCandles >= candles.length + futureCandles.length) {
                            clearInterval(animationTimer);
                            console.log("Animation complete");
                            // Notify Flutter that animation is complete
                            try {
                                if (window.Flutter) {
                                    window.Flutter.postMessage('animationComplete');
                                } else {
                                    console.log("Flutter object not found, animation complete");
                                }
                            } catch (error) {
                                console.error("Error notifying Flutter:", error);
                            }
                        }
                    }
                }, 300);
            } catch (error) {
                console.error("Error in animation:", error);
            }
        }

        // Function to handle messages from Flutter
        function handleFlutterMessage(messageData) {
            try {
                console.log("Handling message:", messageData);

                if (typeof messageData === 'string') {
                    try {
                        messageData = JSON.parse(messageData);
                    } catch (e) {
                        console.error("Failed to parse message string:", e);
                        return;
                    }
                }

                if (messageData.type === 'updateChart') {
                    candles = messageData.candles || [];
                    entryPrice = messageData.entryPrice;
                    showEntryPoint = messageData.showEntryPoint;
                    visibleFutureCandles = messageData.visibleFutureCandles;
                    showFutureCandles = messageData.showFutureCandles;

                    console.log("Processing updateChart message:",
                        "candles:", candles.length,
                        "entryPrice:", entryPrice,
                        "showEntryPoint:", showEntryPoint,
                        "visibleFutureCandles:", visibleFutureCandles,
                        "showFutureCandles:", showFutureCandles
                    );

                    if (showFutureCandles && visibleFutureCandles > 0) {
                        // Extract future candles
                        const mainCandles = candles.slice(0, candles.length - visibleFutureCandles);
                        const futureCandlesData = candles.slice(candles.length - visibleFutureCandles);

                        console.log("Showing", mainCandles.length, "main candles and", futureCandlesData.length, "future candles");

                        // Update chart with main candles
                        updateChart(mainCandles, entryPrice);

                        // Show future candles with animation
                        showFutureCandlesWithAnimation(futureCandlesData);
                    } else {
                        // Update chart with all candles
                        updateChart(candles, entryPrice);
                    }
                }
            } catch (error) {
                console.error("Error processing message:", error);
            }
        }

        // Listen for messages from Flutter via window.postMessage
        window.addEventListener('message', function(event) {
            try {
                console.log("Received message from Flutter:", event.data);
                handleFlutterMessage(event.data);
            } catch (error) {
                console.error("Error in message event handler:", error);
            }
        });

        // Also expose a global function for direct calls
        window.updateChartData = handleFlutterMessage;

        // Initialize chart on load
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM loaded, initializing chart");
            initChart();
        });

        // Also initialize immediately in case DOMContentLoaded already fired
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
            console.log("Document already loaded, initializing chart now");
            setTimeout(initChart, 1);
        }
    </script>
</body>
</html>
