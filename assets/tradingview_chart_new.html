<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>TradingView Chart</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #131722;
            color: #d1d4dc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            position: fixed;
            touch-action: none;
        }

        #chart-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            cursor: grab;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            overflow: hidden;
        }

        #chart-container:active {
            cursor: grabbing;
        }

        /* Индикатор возможности перемещения */
        #drag-indicator {
            position: absolute;
            top: 10px;
            right: 10px;
            background-color: rgba(0, 0, 0, 0.5);
            color: white;
            padding: 5px 10px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
            display: flex;
            align-items: center;
            opacity: 0.7;
            transition: opacity 0.3s;
        }

        #drag-indicator:hover {
            opacity: 1;
        }

        #drag-indicator svg {
            margin-right: 5px;
            width: 16px;
            height: 16px;
        }

        .crosshair-label {
            position: absolute;
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 5px;
            border-radius: 3px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
        }

        .entry-point-label {
            position: absolute;
            background-color: rgba(33, 150, 243, 0.7);
            color: white;
            padding: 5px;
            border-radius: 3px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
        }

        /* Стили для всплывающего окна с результатами */
        #result-popup {
            position: absolute;
            top: 50%;
            left: 25%; /* Позиционируем в левой половине графика */
            transform: translate(-50%, -50%);
            background-color: rgba(30, 34, 45, 0.9);
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            color: white;
            font-size: 16px;
            text-align: center;
            z-index: 1000;
            min-width: 200px;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            display: none; /* Скрыто по умолчанию */
        }

        .result-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 12px;
        }

        .result-success {
            color: #4CAF50;
        }

        .result-failure {
            color: #F44336;
        }

        .result-profit {
            font-size: 20px;
            font-weight: bold;
            margin: 12px 0;
        }

        .profit-positive {
            color: #4CAF50;
        }

        .profit-negative {
            color: #F44336;
        }
    </style>
</head>
<body>
    <div id="chart-container"></div>
    <div id="drag-indicator">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white">
            <path d="M10 9h4V6h3l-5-5-5 5h3v3zm-1 1H6V7l-5 5 5 5v-3h3v-4zm14 2l-5-5v3h-3v4h3v3l5-5zm-9 3h-4v3H7l5 5 5-5h-3v-3z"/>
        </svg>
        <span>Chart is draggable in all directions</span>
    </div>
    <div id="result-popup">
        <div class="result-title">Результат</div>
        <div id="result-status"></div>
        <div id="result-profit" class="result-profit"></div>
    </div>
    <script type="text/javascript" src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
    <script>
        // Глобальные переменные
        let chart;
        let candleSeries;
        let entryPointPrice = null;
        let entryPointTime = null;
        let horizontalLine = null;
        let allCandles = []; // Все свечи (250)
        let visibleCandlesCount = 243; // Количество видимых свечей
        let zoomLevel = 3; // 300% zoom (более отдаленный вид)
        let userHasMovedChart = false; // Флаг для отслеживания, перемещал ли пользователь график
        let _areAllCandlesShown = false; // Флаг для отслеживания, показаны ли все свечи

        // Обработчик сообщений от Flutter
        window.addEventListener('message', function(event) {
            console.log('Received message from parent:', event.data);
            try {
                const message = JSON.parse(event.data);

                switch (message.action) {
                    case 'loadCandles':
                        loadAllCandles(message.data);
                        break;
                    case 'showInitialCandles':
                        showInitialCandles();
                        break;
                    case 'showAllCandles':
                        showAllCandles();
                        break;
                    case 'setEntryPoint':
                        setEntryPoint();
                        break;
                    case 'determineResult':
                        determineResult();
                        break;
                    case 'clearChartElements':
                        clearChartElements();
                        break;
                    case 'resetChartPosition':
                        resetChartPosition();
                        break;
                    case 'centerLastCandle':
                        centerLastCandle();
                        break;
                    case 'checkChartState':
                        checkChartState();
                        break;
                    default:
                        console.log('Unknown action:', message.action);
                }
            } catch (e) {
                console.error('Error processing message:', e);
            }
        });

        // Инициализация графика - полностью новая версия
        function initChart() {
            console.log('Initializing chart with completely new approach...');
            const chartContainer = document.getElementById('chart-container');

            // Проверяем, загружена ли библиотека LightweightCharts
            if (typeof LightweightCharts === 'undefined') {
                console.error('LightweightCharts library is not loaded!');
                // Пытаемся загрузить библиотеку еще раз
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js';
                script.onload = function() {
                    console.log('LightweightCharts library loaded successfully!');
                    initChart(); // Повторно вызываем инициализацию
                };
                script.onerror = function() {
                    console.error('Failed to load LightweightCharts library!');
                };
                document.head.appendChild(script);
                return;
            }

            console.log('Creating chart with new approach...');

            // Создаем график с оптимальными настройками для перемещения и прокрутки
            chart = LightweightCharts.createChart(chartContainer, {
                width: chartContainer.clientWidth,
                height: chartContainer.clientHeight,
                layout: {
                    backgroundColor: '#131722',
                    textColor: '#d1d4dc',
                    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                },
                grid: {
                    vertLines: { color: 'rgba(42, 46, 57, 0.5)' },
                    horzLines: { color: 'rgba(42, 46, 57, 0.5)' },
                },
                timeScale: {
                    timeVisible: true,
                    secondsVisible: false,
                    barSpacing: 6,
                    minBarSpacing: 4,
                    // КРИТИЧЕСКИ ВАЖНО: отключаем все ограничения прокрутки
                    fixRightEdge: false,
                    fixLeftEdge: false,
                    rightBarStaysOnScroll: false,
                    lockVisibleTimeRangeOnResize: false,
                    shiftVisibleRangeOnNewBar: false,
                    rightOffset: 5, // Небольшой отступ справа для лучшей видимости
                    borderColor: '#2a2e39',
                },
                rightPriceScale: {
                    borderColor: '#2a2e39',
                    autoScale: true,
                    scaleMargins: {
                        top: 0.1,
                        bottom: 0.1,
                    },
                },
                crosshair: {
                    mode: LightweightCharts.CrosshairMode.Normal,
                    vertLine: {
                        color: 'rgba(224, 227, 235, 0.4)',
                        style: 1,
                        width: 1,
                    },
                    horzLine: {
                        color: 'rgba(224, 227, 235, 0.4)',
                        style: 1,
                        width: 1,
                    },
                },
                // ВАЖНО: явно включаем перемещение и масштабирование
                handleScroll: true,
                handleScale: true,
            });

            // Проверяем, что график создан правильно
            if (!chart) {
                console.error('Failed to create chart!');
                return;
            }

            // Создаем серию свечей с оптимальными настройками
            candleSeries = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });

            // Проверяем, что серия создана правильно
            if (!candleSeries) {
                console.error('Failed to create candle series!');
                return;
            }

            console.log('Candle series created successfully');

            // Настраиваем обработчики событий
            setupEventHandlers();

            // Инициализируем индикатор перемещения
            initDragIndicator();

            // Сообщаем Flutter, что график инициализирован
            sendMessageToFlutter('chartInitialized', []);
        }

        // Загрузка всех свечей (250) - новая версия
        function loadAllCandles(candles) {
            console.log('Loading all candles with new approach, type:', typeof candles);
            try {
                // Если candles уже объект, используем его напрямую
                if (typeof candles === 'object') {
                    allCandles = candles;
                } else {
                    // Иначе пытаемся распарсить строку
                    allCandles = JSON.parse(candles);
                }
                console.log('Parsed all candles, count:', allCandles.length);

                // Проверяем, что у нас достаточно свечей
                if (allCandles.length < 250) {
                    console.error('Not enough candles, expected 250, got:', allCandles.length);
                }

                // Устанавливаем флаг, что показываем только начальные свечи
                _areAllCandlesShown = false;

                // Пересоздаем график с нуля для гарантированного сброса всех настроек
                recreateChart();

                // Даем время на пересоздание графика и затем центрируем последнюю свечу
                setTimeout(() => {
                    centerLastCandle();

                    // Сообщаем Flutter, что свечи загружены
                    setTimeout(() => {
                        sendMessageToFlutter('candlesLoaded', [allCandles.length]);
                    }, 200);
                }, 300);
            } catch (error) {
                console.error('Error loading all candles:', error);
                console.error('Candles type:', typeof candles);
            }
        }

        // Показать только первые 243 свечи - полностью новая версия
        function showInitialCandles() {
            console.log('Showing initial candles (243) - using new approach');

            if (!allCandles || allCandles.length === 0) {
                console.error('Cannot show initial candles: no candles available');
                return;
            }

            try {
                // Проверяем, что у нас достаточно свечей
                if (allCandles.length < visibleCandlesCount) {
                    console.error(`Not enough candles: have ${allCandles.length}, need ${visibleCandlesCount}`);
                    return;
                }

                console.log(`Preparing to show ${visibleCandlesCount} of ${allCandles.length} candles`);

                // Устанавливаем флаг, что показываем только начальные свечи
                _areAllCandlesShown = false;

                // Очищаем все элементы графика
                clearChartElements();

                // Пересоздаем график с нуля для гарантированного сброса всех настроек
                recreateChart();

                // Даем время на пересоздание графика и затем центрируем последнюю свечу
                setTimeout(() => {
                    centerLastCandle();

                    // Сообщаем Flutter, что начальные свечи отображены
                    setTimeout(() => {
                        sendMessageToFlutter('initialCandlesShown', []);
                    }, 300);
                }, 300);

                console.log('Showing initial candles - complete');
            } catch (error) {
                console.error('Error showing initial candles:', error);
            }
        }

        // Показать все 250 свечей - полностью новая версия
        function showAllCandles() {
            console.log('Showing all candles (250) - using new approach');

            if (!allCandles || allCandles.length === 0) {
                console.error('Cannot show all candles: no candles available');
                return;
            }

            try {
                console.log(`Preparing to show all ${allCandles.length} candles`);

                // Устанавливаем флаг, что показываем все свечи
                _areAllCandlesShown = true;

                // Очищаем все элементы графика
                clearChartElements();

                // Пересоздаем график с нуля для гарантированного сброса всех настроек
                recreateChart();

                // Даем время на пересоздание графика и затем центрируем последнюю свечу
                setTimeout(() => {
                    // Получаем индекс последней видимой свечи (без учета дополнительных)
                    const lastVisibleCandleIndex = allCandles.length - 1;

                    // Получаем размер контейнера
                    const container = document.getElementById('chart-container');
                    if (!container) {
                        console.error('Chart container not found');
                        return;
                    }

                    const containerWidth = container.clientWidth;

                    // Рассчитываем количество свечей, которые должны быть видны
                    const barSpacing = 6; // Расстояние между свечами в пикселях
                    const visibleBarsCount = Math.floor(containerWidth / barSpacing);

                    // Рассчитываем, сколько свечей должно быть слева и справа от центральной
                    const barsToLeft = Math.floor(visibleBarsCount * 0.5); // 50% слева
                    const barsToRight = Math.floor(visibleBarsCount * 0.5); // 50% справа

                    // Устанавливаем видимый диапазон так, чтобы последняя свеча была в центре
                    const fromIndex = Math.max(0, lastVisibleCandleIndex - barsToLeft);
                    const toIndex = lastVisibleCandleIndex + barsToRight;

                    // Устанавливаем видимый диапазон
                    chart.timeScale().setVisibleLogicalRange({
                        from: fromIndex,
                        to: toIndex
                    });

                    // Сообщаем Flutter, что все свечи отображены
                    setTimeout(() => {
                        sendMessageToFlutter('allCandlesShown', []);
                    }, 300);
                }, 300);

                console.log('Showing all candles - complete');
            } catch (error) {
                console.error('Error showing all candles:', error);
            }
        }

        // Совершенно новый подход к центрированию последней свечи
        function centerLastCandle() {
            console.log('Centering last candle using completely new approach');

            if (!chart || !candleSeries) {
                console.error('Cannot center last candle: chart or series not available');
                return;
            }

            try {
                // Полностью пересоздаем график для гарантированного сброса всех настроек
                recreateChart();

                // Даем время на пересоздание графика
                setTimeout(() => {
                    // Получаем данные свечей после пересоздания
                    const candles = candleSeries.dataByIndex();
                    if (!candles || candles.length === 0) {
                        console.error('No candles available after chart recreation');
                        return;
                    }

                    // Получаем размер контейнера
                    const container = document.getElementById('chart-container');
                    if (!container) {
                        console.error('Chart container not found');
                        return;
                    }

                    const containerWidth = container.clientWidth;
                    console.log('Container width:', containerWidth);

                    // Получаем индекс последней видимой свечи (без учета дополнительных)
                    const visibleCandleCount = Math.min(visibleCandlesCount, allCandles.length);
                    const lastVisibleCandleIndex = visibleCandleCount - 1;

                    console.log('Last visible candle index:', lastVisibleCandleIndex);

                    // Используем координатную систему графика для точного позиционирования
                    // Получаем координату X для последней свечи
                    const lastCandleTime = candles[lastVisibleCandleIndex].time;

                    // Рассчитываем количество свечей, которые должны быть видны
                    const barSpacing = 6; // Расстояние между свечами в пикселях
                    const visibleBarsCount = Math.floor(containerWidth / barSpacing);

                    // Рассчитываем, сколько свечей должно быть слева и справа от центральной
                    const barsToLeft = Math.floor(visibleBarsCount * 0.5); // 50% слева
                    const barsToRight = Math.floor(visibleBarsCount * 0.5); // 50% справа

                    console.log('Bars distribution:', {
                        total: visibleBarsCount,
                        toLeft: barsToLeft,
                        toRight: barsToRight
                    });

                    // Устанавливаем видимый диапазон так, чтобы последняя свеча была в центре
                    // Используем логический диапазон для более точного контроля
                    const fromIndex = Math.max(0, lastVisibleCandleIndex - barsToLeft);
                    const toIndex = lastVisibleCandleIndex + barsToRight;

                    console.log('Setting visible range:', {
                        from: fromIndex,
                        to: toIndex,
                        lastVisibleCandleIndex: lastVisibleCandleIndex
                    });

                    // Устанавливаем видимый диапазон
                    chart.timeScale().setVisibleLogicalRange({
                        from: fromIndex,
                        to: toIndex
                    });

                    // Проверяем результат через небольшую задержку
                    setTimeout(() => {
                        const visibleRange = chart.timeScale().getVisibleLogicalRange();
                        console.log('Visible range after centering:', visibleRange);

                        // Если центрирование не сработало, используем другой метод
                        if (!visibleRange || Math.abs(visibleRange.to - toIndex) > 5) {
                            console.log('Centering failed, using alternative method');

                            // Используем fitContent для начального позиционирования
                            chart.timeScale().fitContent();

                            // Затем прокручиваем к нужной позиции
                            setTimeout(() => {
                                // Используем координатную систему для прокрутки
                                const centerX = Math.floor(containerWidth / 2);
                                chart.timeScale().scrollToPosition(lastVisibleCandleIndex, centerX);

                                console.log('Applied alternative centering method');
                            }, 100);
                        }
                    }, 200);
                }, 300);

                // Применяем автомасштабирование по вертикали
                chart.priceScale('right').applyOptions({
                    autoScale: true,
                    scaleMargins: {
                        top: 0.1,
                        bottom: 0.1,
                    },
                });
            } catch (error) {
                console.error('Error in new centering approach:', error);
            }
        }

        // Функция для полного пересоздания графика - улучшенная версия
        function recreateChart() {
            console.log('Recreating chart from scratch with improved approach');

            try {
                // Сохраняем текущие данные
                const currentCandles = candleSeries ? candleSeries.dataByIndex() : [];

                // Получаем контейнер
                const chartContainer = document.getElementById('chart-container');
                if (!chartContainer) {
                    console.error('Chart container not found');
                    return;
                }

                // Удаляем старый график
                if (chart) {
                    chart.remove();
                }

                // Создаем новый график с оптимальными настройками для свободного перемещения
                chart = LightweightCharts.createChart(chartContainer, {
                    width: chartContainer.clientWidth,
                    height: chartContainer.clientHeight,
                    layout: {
                        backgroundColor: '#131722',
                        textColor: '#d1d4dc',
                        fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
                    },
                    grid: {
                        vertLines: { color: 'rgba(42, 46, 57, 0.5)' },
                        horzLines: { color: 'rgba(42, 46, 57, 0.5)' },
                    },
                    timeScale: {
                        timeVisible: true,
                        secondsVisible: false,
                        barSpacing: 6,
                        minBarSpacing: 4,
                        // КРИТИЧЕСКИ ВАЖНО: отключаем все ограничения прокрутки
                        fixRightEdge: false,
                        fixLeftEdge: false,
                        rightBarStaysOnScroll: false,
                        lockVisibleTimeRangeOnResize: false,
                        shiftVisibleRangeOnNewBar: false,
                        rightOffset: 0, // Убираем отступ справа для лучшего центрирования
                    },
                    rightPriceScale: {
                        borderColor: '#2a2e39',
                        autoScale: true,
                        scaleMargins: {
                            top: 0.1,
                            bottom: 0.1,
                        },
                    },
                    crosshair: {
                        mode: LightweightCharts.CrosshairMode.Normal,
                    },
                    // ВАЖНО: явно включаем перемещение и масштабирование
                    handleScroll: true,
                    handleScale: true,
                });

                // Создаем новую серию свечей
                candleSeries = chart.addCandlestickSeries({
                    upColor: '#26a69a',
                    downColor: '#ef5350',
                    borderVisible: false,
                    wickUpColor: '#26a69a',
                    wickDownColor: '#ef5350',
                });

                // Восстанавливаем данные
                if (currentCandles.length > 0) {
                    candleSeries.setData(currentCandles);
                } else if (allCandles.length > 0) {
                    // Если нет текущих данных, используем все свечи
                    // Добавляем дополнительные свечи справа для прокрутки
                    const extendedCandles = createExtendedCandles();
                    candleSeries.setData(extendedCandles);
                }

                // Добавляем обработчики событий
                setupEventHandlers();

                // Сбрасываем флаг перемещения графика
                userHasMovedChart = false;

                console.log('Chart recreated successfully with improved approach');
                return true;
            } catch (error) {
                console.error('Error recreating chart:', error);
                return false;
            }
        }

        // Функция для создания расширенных данных с дополнительными свечами справа - улучшенная версия
        function createExtendedCandles() {
            // Определяем, какие свечи показывать
            const baseCandles = _areAllCandlesShown ? allCandles : allCandles.slice(0, visibleCandlesCount);

            if (baseCandles.length < 2) {
                console.error('Not enough candles to create extended data');
                return baseCandles;
            }

            // Создаем дополнительные свечи справа для возможности прокрутки
            const extraCandles = [];
            const lastCandle = baseCandles[baseCandles.length - 1];
            const secondLastCandle = baseCandles[baseCandles.length - 2];
            const timeStep = lastCandle.time - secondLastCandle.time;

            // Создаем 300 дополнительных свечей с реалистичными данными
            // Это гарантирует возможность прокрутки далеко вправо
            let prevClose = lastCandle.close;
            for (let i = 1; i <= 300; i++) {
                // Генерируем случайное изменение цены в пределах ±2%
                const changePercent = (Math.random() * 4 - 2) * 1.0;
                const change = prevClose * (changePercent / 100);

                // Создаем реалистичные данные свечи
                const close = prevClose + change;
                const open = prevClose;
                const high = Math.max(open, close) * (1 + Math.random() * 0.01);
                const low = Math.min(open, close) * (1 - Math.random() * 0.01);
                const volume = lastCandle.volume * (0.5 + Math.random());

                extraCandles.push({
                    time: lastCandle.time + timeStep * i,
                    open: open,
                    high: high,
                    low: low,
                    close: close,
                    volume: volume
                });

                prevClose = close;
            }

            console.log(`Created ${extraCandles.length} extra candles for right scrolling`);

            // Объединяем базовые свечи с дополнительными
            const result = [...baseCandles, ...extraCandles];
            console.log(`Total candles after extension: ${result.length}`);

            return result;
        }

        // Настройка обработчиков событий для графика
        function setupEventHandlers() {
            if (!chart) return;

            // Получаем контейнер
            const chartContainer = document.getElementById('chart-container');
            if (!chartContainer) return;

            // Обработчик изменения размера
            const resizeHandler = () => {
                if (!chart) return;

                chart.resize(
                    chartContainer.clientWidth,
                    chartContainer.clientHeight
                );

                // Пересчитываем видимый диапазон при изменении размера
                setTimeout(() => {
                    centerLastCandle();
                }, 100);
            };

            // Удаляем предыдущий обработчик, если он был
            window.removeEventListener('resize', resizeHandler);

            // Добавляем новый обработчик
            window.addEventListener('resize', resizeHandler);

            // Включаем перемещение и масштабирование
            chart.applyOptions({
                handleScroll: true,
                handleScale: true,
            });

            // Отслеживаем взаимодействие пользователя с графиком
            chartContainer.addEventListener('mousedown', () => {
                userHasMovedChart = true;
                chartContainer.style.cursor = 'grabbing';
            });

            chartContainer.addEventListener('mouseup', () => {
                chartContainer.style.cursor = 'grab';
            });

            chartContainer.addEventListener('touchstart', () => {
                userHasMovedChart = true;
            });

            // Проверяем настройки графика после настройки обработчиков
            setTimeout(() => {
                checkChartState();
            }, 200);
        }

        // Центрирование последней свечи - полностью новая реализация
        function centerLastCandle() {
            console.log('Centering last candle with completely new implementation');

            if (!chart || !candleSeries) {
                console.error('Cannot center last candle: chart or series not available');
                return;
            }

            try {
                // Получаем данные свечей
                const candles = candleSeries.dataByIndex();
                if (!candles || candles.length === 0) {
                    console.error('No candles available for centering');
                    return;
                }

                // Получаем размер контейнера
                const container = document.getElementById('chart-container');
                if (!container) {
                    console.error('Chart container not found');
                    return;
                }

                const containerWidth = container.clientWidth;
                console.log('Container width:', containerWidth);

                // Получаем индекс последней видимой свечи (без учета дополнительных)
                const lastVisibleCandleIndex = Math.min(visibleCandlesCount, allCandles.length) - 1;

                console.log('Last visible candle index:', lastVisibleCandleIndex);

                // Устанавливаем фиксированное расстояние между свечами
                const barSpacing = 6;

                // Отключаем все ограничения прокрутки и масштабирования
                chart.applyOptions({
                    timeScale: {
                        barSpacing: barSpacing,
                        fixRightEdge: false,
                        fixLeftEdge: false,
                        rightBarStaysOnScroll: false,
                        lockVisibleTimeRangeOnResize: false,
                        shiftVisibleRangeOnNewBar: false,
                        rightOffset: 0,
                    },
                    handleScroll: true,
                    handleScale: true,
                });

                // Рассчитываем количество видимых свечей
                const visibleBarsCount = Math.floor(containerWidth / barSpacing);

                // Рассчитываем, сколько свечей должно быть слева и справа от центральной
                const barsToLeft = Math.floor(visibleBarsCount * 0.5); // 50% слева
                const barsToRight = Math.floor(visibleBarsCount * 0.5); // 50% справа

                console.log('Centering with bars to left:', barsToLeft, 'and to right:', barsToRight);

                // Устанавливаем видимый диапазон так, чтобы последняя свеча была в центре
                const fromIndex = Math.max(0, lastVisibleCandleIndex - barsToLeft);
                const toIndex = lastVisibleCandleIndex + barsToRight;

                console.log('Setting visible range:', {
                    from: fromIndex,
                    to: toIndex,
                    lastVisibleCandleIndex: lastVisibleCandleIndex
                });

                // Устанавливаем видимый диапазон
                chart.timeScale().setVisibleLogicalRange({
                    from: fromIndex,
                    to: toIndex
                });

                // Показываем индикатор перемещения
                const dragIndicator = document.getElementById('drag-indicator');
                if (dragIndicator) {
                    dragIndicator.style.display = 'flex';
                    dragIndicator.style.opacity = '0.9';

                    // Скрываем через 5 секунд
                    setTimeout(() => {
                        dragIndicator.style.opacity = '0';
                        setTimeout(() => {
                            dragIndicator.style.display = 'none';
                        }, 300);
                    }, 5000);
                }

                // Отправляем сообщение во Flutter
                sendMessageToFlutter('lastCandleCentered', []);

                console.log('Centering last candle - complete');
            } catch (error) {
                console.error('Error centering last candle:', error);
            }
        }

        // Применение зума (устаревшая функция, используем centerLastCandle)
        function applyZoom() {
            centerLastCandle();
        }

        // Установка точки входа - вызывается только после нажатия кнопок UP/DOWN
        function setEntryPoint() {
            try {
                if (allCandles.length < visibleCandlesCount) {
                    console.error('Not enough candles to set entry point');
                    return;
                }

                // Точка входа - последняя видимая свеча (243-я)
                const entryCandle = allCandles[visibleCandlesCount - 1];
                if (!entryCandle) {
                    console.error('Entry point not set, using last visible candle');
                    return;
                }

                entryPointPrice = entryCandle.close;
                entryPointTime = entryCandle.time;

                console.log('Entry point set at candle:', visibleCandlesCount - 1, 'price:', entryPointPrice);

                // Удаляем предыдущие элементы, если они существуют
                if (horizontalLine) {
                    try {
                        candleSeries.removePriceLine(horizontalLine);
                    } catch (e) {
                        console.error('Error removing price line:', e);
                    }
                    horizontalLine = null;
                }

                // Удаляем существующие элементы перекрестия
                const existingVerticalLine = document.getElementById('entry-vertical-line');
                if (existingVerticalLine) {
                    existingVerticalLine.remove();
                }

                // Создаем горизонтальную линию для точки входа (пунктирная белая полупрозрачная)
                horizontalLine = candleSeries.createPriceLine({
                    price: entryPointPrice,
                    color: 'rgba(255, 255, 255, 0.7)', // Белый цвет с прозрачностью 70%
                    lineWidth: 1,
                    lineStyle: LightweightCharts.LineStyle.Dashed,
                    axisLabelVisible: true,
                    title: 'Entry',
                });

                // Создаем вертикальную линию для перекрестия
                const container = document.getElementById('chart-container');

                // Создаем элемент для вертикальной линии
                const verticalLine = document.createElement('div');
                verticalLine.style.position = 'absolute';
                verticalLine.style.width = '1px';
                verticalLine.style.height = '100%';
                verticalLine.style.borderLeft = '1px dashed rgba(255, 255, 255, 0.7)';
                verticalLine.style.zIndex = '1000';
                verticalLine.id = 'entry-vertical-line';

                // Добавляем элемент в контейнер
                container.appendChild(verticalLine);

                // Позиционируем вертикальную линию
                const timeScale = chart.timeScale();
                const x = timeScale.timeToCoordinate(entryCandle.time);
                if (x !== null) {
                    verticalLine.style.left = x + 'px';
                }

                // Обновляем позицию при изменении масштаба
                chart.timeScale().subscribeVisibleTimeRangeChange(() => {
                    const x = timeScale.timeToCoordinate(entryCandle.time);
                    if (x !== null) {
                        verticalLine.style.left = x + 'px';
                    }
                });

                // Сообщаем Flutter, что точка входа установлена
                sendMessageToFlutter('entryPointSet', [entryPointPrice, entryPointTime]);
            } catch (error) {
                console.error('Error setting entry point:', error);
            }
        }

        // Определение результата
        function determineResult() {
            try {
                if (!entryPointPrice) {
                    console.error('Cannot determine result: entryPointPrice is not set');
                    return;
                }

                if (allCandles.length < visibleCandlesCount + 7) {
                    console.error('Cannot determine result: not enough candles');
                    return;
                }

                // Берем 7-ю свечу после точки входа
                const resultCandle = allCandles[visibleCandlesCount + 6];
                if (!resultCandle) {
                    console.error('Cannot determine result: result candle not found');
                    return;
                }

                console.log('Result determined using candle:', visibleCandlesCount + 6, 'price:', resultCandle.close);

                // Сравниваем цену закрытия с ценой входа
                const priceChange = resultCandle.close - entryPointPrice;
                const percentChange = (priceChange / entryPointPrice) * 100;
                const isUp = priceChange > 0;

                // Показываем всплывающее окно с результатом
                showResultPopup(isUp, percentChange);

                // Сообщаем Flutter о результате
                sendMessageToFlutter('tradeResult', [
                    isUp, // isUp
                    percentChange,   // percentChange
                    resultCandle.close // finalPrice
                ]);
            } catch (error) {
                console.error('Error determining result:', error);
            }
        }

        // Показать всплывающее окно с результатом
        function showResultPopup(isSuccess, percentChange) {
            console.log('Showing result popup:', isSuccess, percentChange);

            // Получаем элементы всплывающего окна
            const popup = document.getElementById('result-popup');
            const statusElement = document.getElementById('result-status');
            const profitElement = document.getElementById('result-profit');

            if (!popup || !statusElement || !profitElement) {
                console.error('Result popup elements not found');
                return;
            }

            // Устанавливаем статус (Правильный/Неправильный выбор)
            if (isSuccess) {
                statusElement.innerHTML = '<span class="result-success">Правильный выбор</span>';
                statusElement.className = 'result-success';
            } else {
                statusElement.innerHTML = '<span class="result-failure">Неправильный выбор</span>';
                statusElement.className = 'result-failure';
            }

            // Форматируем процент изменения
            const formattedPercent = Math.abs(percentChange).toFixed(2) + '%';

            // Устанавливаем прибыль/убыток
            if (percentChange >= 0) {
                profitElement.innerHTML = '+' + formattedPercent;
                profitElement.className = 'result-profit profit-positive';
            } else {
                profitElement.innerHTML = '-' + formattedPercent;
                profitElement.className = 'result-profit profit-negative';
            }

            // Показываем всплывающее окно
            popup.style.display = 'block';

            // Позиционируем всплывающее окно в левой половине графика
            const chartContainer = document.getElementById('chart-container');
            if (chartContainer) {
                const containerWidth = chartContainer.offsetWidth;
                const containerHeight = chartContainer.offsetHeight;

                popup.style.left = (containerWidth * 0.25) + 'px';
                popup.style.top = (containerHeight * 0.5) + 'px';
            }

            // Скрываем всплывающее окно через 5 секунд
            setTimeout(() => {
                popup.style.display = 'none';
            }, 5000);

            console.log('Result popup shown');
        }

        // Очистка всех элементов графика
        function clearChartElements() {
            console.log('Clearing all chart elements');

            // Сбрасываем точку входа
            entryPointPrice = null;
            entryPointTime = null;

            // Удаляем горизонтальную линию
            if (horizontalLine) {
                candleSeries.removePriceLine(horizontalLine);
                horizontalLine = null;
            }

            // Удаляем вертикальную линию
            const existingVerticalLine = document.getElementById('entry-vertical-line');
            if (existingVerticalLine) {
                existingVerticalLine.remove();
            }

            // Скрываем всплывающее окно с результатом
            const popup = document.getElementById('result-popup');
            if (popup) {
                popup.style.display = 'none';
            }

            // Удаляем все маркеры
            candleSeries.setMarkers([]);

            // Удаляем все пользовательские элементы
            const customElements = document.querySelectorAll('.custom-chart-element');
            customElements.forEach(element => {
                element.remove();
            });

            console.log('All chart elements cleared');
        }

        // Отправка сообщения в Flutter
        function sendMessageToFlutter(handler, args) {
            try {
                const message = JSON.stringify({
                    handler: handler,
                    args: args
                });

                if (window.flutter_inappwebview) {
                    // Для мобильных платформ
                    window.flutter_inappwebview.postMessage(message);
                } else if (window.parent && window.parent !== window) {
                    // Для веб-платформы (iframe)
                    window.parent.postMessage(message, '*');
                } else {
                    console.log('Flutter interface not available');
                }
            } catch (e) {
                console.error('Error sending message to Flutter:', e);
            }
        }

        // Сброс позиции графика к исходному состоянию - полностью новая версия
        function resetChartPosition() {
            console.log('Resetting chart position with completely new approach');

            if (!chart || !candleSeries || !allCandles || allCandles.length === 0) {
                console.error('Cannot reset chart position: chart, series or candles not available');
                return;
            }

            try {
                // Отключаем все ограничения прокрутки и масштабирования
                chart.applyOptions({
                    timeScale: {
                        barSpacing: 6,
                        fixRightEdge: false,
                        fixLeftEdge: false,
                        rightBarStaysOnScroll: false,
                        lockVisibleTimeRangeOnResize: false,
                        shiftVisibleRangeOnNewBar: false,
                        rightOffset: 0,
                    },
                    handleScroll: true,
                    handleScale: true,
                    rightPriceScale: {
                        autoScale: true,
                        scaleMargins: {
                            top: 0.1,
                            bottom: 0.1,
                        },
                    },
                });

                // Сбрасываем флаг перемещения графика
                userHasMovedChart = false;

                // Очищаем все элементы графика
                clearChartElements();

                // Центрируем последнюю свечу
                setTimeout(() => {
                    centerLastCandle();

                    // Показываем индикатор перемещения
                    const dragIndicator = document.getElementById('drag-indicator');
                    if (dragIndicator) {
                        dragIndicator.style.display = 'flex';
                        dragIndicator.style.opacity = '0.9';

                        // Скрываем через 5 секунд
                        setTimeout(() => {
                            dragIndicator.style.opacity = '0';
                            setTimeout(() => {
                                dragIndicator.style.display = 'none';
                            }, 300);
                        }, 5000);
                    }

                    // Отправляем сообщение во Flutter
                    sendMessageToFlutter('chartPositionReset', []);

                    console.log('Chart position reset complete');
                }, 100);
            } catch (error) {
                console.error('Error resetting chart position:', error);
            }
        }

        // Проверка и исправление состояния графика - улучшенная версия
        function checkChartState() {
            console.log('Checking and fixing chart state');

            try {
                // Проверяем, что график создан и настроен правильно
                if (!chart) {
                    console.error('Chart is not initialized');
                    return;
                }

                // Получаем текущие настройки графика
                const options = chart.options();

                // Проверяем, что перемещение и масштабирование включены
                console.log('Chart draggable status:', {
                    handleScroll: options.handleScroll,
                    handleScale: options.handleScale,
                    timeScale: options.timeScale ? 'configured' : 'not configured'
                });

                // Если перемещение отключено, включаем его с полным набором настроек
                if (!options.handleScroll || !options.handleScale) {
                    console.log('Enabling chart dragging and scaling with full settings');
                    chart.applyOptions({
                        handleScroll: true,
                        handleScale: true,
                        timeScale: {
                            barSpacing: 6,
                            fixRightEdge: false,
                            fixLeftEdge: false,
                            rightBarStaysOnScroll: false,
                            lockVisibleTimeRangeOnResize: false,
                        },
                        rightPriceScale: {
                            autoScale: true,
                            scaleMargins: {
                                top: 0.1,
                                bottom: 0.1,
                            },
                        },
                    });
                }

                // Проверяем настройки timeScale
                const timeScaleOptions = options.timeScale || {};
                if (timeScaleOptions.fixRightEdge === true ||
                    timeScaleOptions.fixLeftEdge === true ||
                    timeScaleOptions.lockVisibleTimeRangeOnResize === true ||
                    timeScaleOptions.shiftVisibleRangeOnNewBar === true ||
                    timeScaleOptions.rightOffset !== 0) {

                    console.log('Fixing restrictive timeScale settings');
                    chart.applyOptions({
                        timeScale: {
                            barSpacing: 6,
                            fixRightEdge: false,
                            fixLeftEdge: false,
                            rightBarStaysOnScroll: false,
                            lockVisibleTimeRangeOnResize: false,
                            shiftVisibleRangeOnNewBar: false,
                            rightOffset: 0,
                        }
                    });

                    // Проверяем, что настройки применились
                    const updatedOptions = chart.options();
                    console.log('Updated timeScale settings:', {
                        fixRightEdge: updatedOptions.timeScale?.fixRightEdge,
                        fixLeftEdge: updatedOptions.timeScale?.fixLeftEdge,
                        rightOffset: updatedOptions.timeScale?.rightOffset
                    });
                }

                // Проверяем, что серия свечей создана и содержит данные
                if (candleSeries) {
                    const candleCount = candleSeries.dataByIndex().length;
                    console.log('Candle series contains', candleCount, 'candles');

                    // Если нет данных, но у нас есть allCandles, пробуем восстановить
                    if (candleCount === 0 && allCandles.length > 0) {
                        console.log('Restoring candles data');
                        // Проверяем, сколько свечей должно быть видно
                        const currentCandleCount = candleSeries.dataByIndex().length;
                        const isShowingAllCandles = currentCandleCount >= allCandles.length;

                        if (isShowingAllCandles) {
                            candleSeries.setData(allCandles);
                        } else {
                            candleSeries.setData(allCandles.slice(0, visibleCandlesCount));
                        }
                    }
                } else {
                    console.error('Candle series is not initialized');
                }

                // Проверяем видимый диапазон
                const visibleRange = chart.timeScale().getVisibleLogicalRange();
                console.log('Visible range:', visibleRange);

                // Если видимый диапазон не установлен или некорректен, исправляем
                if (!visibleRange ||
                    (candleSeries && candleSeries.dataByIndex().length > 0 &&
                     (visibleRange.to < candleSeries.dataByIndex().length - 1))) {

                    console.log('Fixing visible range');
                    // Используем fitContent для начального позиционирования
                    chart.timeScale().fitContent();

                    // Затем центрируем последнюю свечу
                    setTimeout(() => {
                        centerLastCandle();
                    }, 50);
                }

                // Отправляем результаты проверки во Flutter
                sendMessageToFlutter('chartStateChecked', [
                    options.handleScroll,
                    options.handleScale,
                    candleSeries ? candleSeries.dataByIndex().length : 0
                ]);

                console.log('Chart state check and fix complete');
            } catch (e) {
                console.error('Error checking chart state:', e);
            }
        }

        // Инициализация индикатора перемещения
        function initDragIndicator() {
            const dragIndicator = document.getElementById('drag-indicator');
            if (!dragIndicator) return;

            // Показываем индикатор
            dragIndicator.style.display = 'flex';

            // Скрываем индикатор через 5 секунд
            setTimeout(() => {
                dragIndicator.style.opacity = '0';

                // Полностью скрываем после завершения анимации
                setTimeout(() => {
                    dragIndicator.style.display = 'none';
                }, 300);
            }, 5000);

            // Показываем индикатор при взаимодействии с графиком
            const chartContainer = document.getElementById('chart-container');
            if (chartContainer) {
                chartContainer.addEventListener('mousedown', () => {
                    // Показываем индикатор на короткое время при взаимодействии
                    dragIndicator.style.display = 'flex';
                    dragIndicator.style.opacity = '0.7';

                    // Скрываем через 2 секунды
                    setTimeout(() => {
                        dragIndicator.style.opacity = '0';
                        setTimeout(() => {
                            dragIndicator.style.display = 'none';
                        }, 300);
                    }, 2000);
                });
            }
        }

        // Инициализация
        document.addEventListener('DOMContentLoaded', initChart);
    </script>
</body>
</html>