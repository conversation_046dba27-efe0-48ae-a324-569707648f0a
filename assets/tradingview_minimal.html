<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Draggable Chart</title>
    <style>
        body, html {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            background-color: #131722;
            color: #d1d4dc;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        #chart-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            cursor: grab;
        }
        #chart-container:active {
            cursor: grabbing;
        }
    </style>
</head>
<body>
    <div id="chart-container"></div>
    
    <script src="https://unpkg.com/lightweight-charts@3.8.0/dist/lightweight-charts.standalone.production.js"></script>
    <script>
        // Глобальные переменные
        let chart = null;
        let candleSeries = null;
        let allCandles = [];
        let visibleCandlesCount = 243;
        let entryPointPrice = null;
        let entryPointTime = null;
        let horizontalLine = null;
        
        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            initChart();
        });
        
        // Обработчик сообщений от Flutter
        window.addEventListener('message', function(event) {
            try {
                const message = JSON.parse(event.data);
                console.log('Received message:', message);
                
                switch (message.action) {
                    case 'loadCandles':
                        loadCandles(message.data);
                        break;
                    case 'showInitialCandles':
                        showInitialCandles();
                        break;
                    case 'showAllCandles':
                        showAllCandles();
                        break;
                    case 'setEntryPoint':
                        setEntryPoint();
                        break;
                    case 'determineResult':
                        determineResult();
                        break;
                    case 'clearChartElements':
                        clearChartElements();
                        break;
                    case 'centerLastCandle':
                        centerLastCandle();
                        break;
                }
            } catch (e) {
                console.error('Error processing message:', e);
            }
        });
        
        // Инициализация графика
        function initChart() {
            const container = document.getElementById('chart-container');
            
            // Создаем график с минимальными настройками
            chart = LightweightCharts.createChart(container, {
                width: container.clientWidth,
                height: container.clientHeight,
                layout: {
                    backgroundColor: '#131722',
                    textColor: '#d1d4dc',
                },
                grid: {
                    vertLines: { color: 'rgba(42, 46, 57, 0.5)' },
                    horzLines: { color: 'rgba(42, 46, 57, 0.5)' },
                },
                timeScale: {
                    timeVisible: true,
                    secondsVisible: false,
                    barSpacing: 6,
                },
                handleScroll: true,
                handleScale: true,
            });
            
            // Создаем серию свечей
            candleSeries = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                borderVisible: false,
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
            });
            
            // Обработчик изменения размера окна
            window.addEventListener('resize', function() {
                if (chart) {
                    chart.resize(container.clientWidth, container.clientHeight);
                    centerLastCandle();
                }
            });
            
            // Добавляем обработчики событий для проверки перемещения
            container.addEventListener('mousedown', function(e) {
                console.log('Mouse down on chart');
                container.style.cursor = 'grabbing';
            });
            
            container.addEventListener('mouseup', function(e) {
                console.log('Mouse up on chart');
                container.style.cursor = 'grab';
            });
            
            container.addEventListener('touchstart', function(e) {
                console.log('Touch start on chart');
            });
            
            // Сообщаем Flutter, что график инициализирован
            sendMessageToFlutter('chartInitialized', []);
        }
        
        // Загрузка свечей
        function loadCandles(candles) {
            try {
                allCandles = Array.isArray(candles) ? candles : JSON.parse(candles);
                console.log(`Loaded ${allCandles.length} candles`);
                
                // Показываем начальные свечи
                showInitialCandles();
                
                // Сообщаем Flutter, что свечи загружены
                sendMessageToFlutter('candlesLoaded', [allCandles.length]);
            } catch (e) {
                console.error('Error loading candles:', e);
            }
        }
        
        // Показать только первые 243 свечи
        function showInitialCandles() {
            if (!chart || !candleSeries || !allCandles || allCandles.length === 0) {
                console.error('Cannot show initial candles');
                return;
            }
            
            try {
                // Берем только первые 243 свечи
                const initialCandles = allCandles.slice(0, visibleCandlesCount);
                
                // Устанавливаем свечи на график
                candleSeries.setData(initialCandles);
                console.log(`Set ${initialCandles.length} candles to chart`);
                
                // Очищаем элементы графика
                clearChartElements();
                
                // Центрируем последнюю свечу
                centerLastCandle();
            } catch (e) {
                console.error('Error showing initial candles:', e);
            }
        }
        
        // Показать все 250 свечей
        function showAllCandles() {
            if (!chart || !candleSeries || !allCandles || allCandles.length === 0) {
                console.error('Cannot show all candles');
                return;
            }
            
            try {
                // Устанавливаем все свечи на график
                candleSeries.setData(allCandles);
                console.log(`Set all ${allCandles.length} candles to chart`);
                
                // Центрируем последнюю свечу
                centerLastCandle();
                
                // Сообщаем Flutter, что все свечи отображены
                sendMessageToFlutter('allCandlesShown', []);
            } catch (e) {
                console.error('Error showing all candles:', e);
            }
        }
        
        // Центрирование последней свечи
        function centerLastCandle() {
            if (!chart || !candleSeries) {
                console.error('Cannot center last candle');
                return;
            }
            
            try {
                // Получаем данные свечей
                const candles = candleSeries.dataByIndex();
                if (!candles || candles.length === 0) {
                    console.error('No candles available for centering');
                    return;
                }
                
                // Получаем размер контейнера
                const container = document.getElementById('chart-container');
                const containerWidth = container.clientWidth;
                
                // Рассчитываем количество видимых свечей
                const barSpacing = 6;
                const visibleBarsCount = Math.floor(containerWidth / barSpacing);
                
                // Рассчитываем отступ справа для позиционирования последней свечи на 15% правее центра
                const rightOffsetPercent = 0.35; // 35% от видимых свечей
                const rightOffset = Math.floor(visibleBarsCount * rightOffsetPercent);
                
                console.log('Centering with rightOffset:', rightOffset);
                
                // Устанавливаем настройки timeScale
                chart.applyOptions({
                    timeScale: {
                        barSpacing: barSpacing,
                        rightOffset: rightOffset,
                    }
                });
                
                // Сообщаем Flutter, что последняя свеча центрирована
                sendMessageToFlutter('lastCandleCentered', []);
            } catch (e) {
                console.error('Error centering last candle:', e);
            }
        }
        
        // Установка точки входа
        function setEntryPoint() {
            if (!candleSeries || !allCandles || allCandles.length < visibleCandlesCount) {
                console.error('Cannot set entry point');
                return;
            }
            
            try {
                // Точка входа - последняя видимая свеча
                const entryCandle = allCandles[visibleCandlesCount - 1];
                entryPointPrice = entryCandle.close;
                entryPointTime = entryCandle.time;
                
                // Удаляем предыдущую линию, если она есть
                if (horizontalLine) {
                    candleSeries.removePriceLine(horizontalLine);
                }
                
                // Создаем горизонтальную линию для точки входа
                horizontalLine = candleSeries.createPriceLine({
                    price: entryPointPrice,
                    color: 'rgba(255, 255, 255, 0.7)',
                    lineWidth: 1,
                    lineStyle: LightweightCharts.LineStyle.Dashed,
                    axisLabelVisible: true,
                    title: 'Entry',
                });
                
                // Сообщаем Flutter, что точка входа установлена
                sendMessageToFlutter('entryPointSet', [entryPointPrice, entryPointTime]);
            } catch (e) {
                console.error('Error setting entry point:', e);
            }
        }
        
        // Определение результата
        function determineResult() {
            if (!entryPointPrice || allCandles.length < visibleCandlesCount + 7) {
                console.error('Cannot determine result');
                return;
            }
            
            try {
                // Берем 7-ю свечу после точки входа
                const resultCandle = allCandles[visibleCandlesCount + 6];
                
                // Сравниваем цену закрытия с ценой входа
                const priceChange = resultCandle.close - entryPointPrice;
                const percentChange = (priceChange / entryPointPrice) * 100;
                const isUp = priceChange > 0;
                
                // Сообщаем Flutter о результате
                sendMessageToFlutter('tradeResult', [
                    isUp,
                    percentChange,
                    resultCandle.close
                ]);
            } catch (e) {
                console.error('Error determining result:', e);
            }
        }
        
        // Очистка элементов графика
        function clearChartElements() {
            // Сбрасываем точку входа
            entryPointPrice = null;
            entryPointTime = null;
            
            // Удаляем горизонтальную линию
            if (horizontalLine) {
                candleSeries.removePriceLine(horizontalLine);
                horizontalLine = null;
            }
        }
        
        // Отправка сообщения в Flutter
        function sendMessageToFlutter(handler, args) {
            try {
                const message = JSON.stringify({
                    handler: handler,
                    args: args
                });
                
                if (window.flutter_inappwebview) {
                    window.flutter_inappwebview.postMessage(message);
                } else if (window.parent && window.parent !== window) {
                    window.parent.postMessage(message, '*');
                }
            } catch (e) {
                console.error('Error sending message to Flutter:', e);
            }
        }
    </script>
</body>
</html>
