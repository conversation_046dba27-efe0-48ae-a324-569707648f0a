import 'package:flutter/material.dart';
import 'candlestick_chart.dart';

/// Интерактивный график свечей без зума
class InteractiveCandlestickChart extends StatefulWidget {
  final List<List<dynamic>> klines;
  final List<String> timestamps;
  final String timeframe;
  final double? currentPrice;
  final String? symbol;

  const InteractiveCandlestickChart({
    Key? key,
    required this.klines,
    required this.timestamps,
    required this.timeframe,
    this.currentPrice,
    this.symbol,
  }) : super(key: key);

  @override
  State<InteractiveCandlestickChart> createState() => _InteractiveCandlestickChartState();
}

class _InteractiveCandlestickChartState extends State<InteractiveCandlestickChart> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 350,
      margin: const EdgeInsets.only(bottom: 20),
      child: <PERSON>ack(
        children: [
          // Основной график
          Positioned.fill(
            child: Container(
              margin: const EdgeInsets.only(bottom: 30, right: 100),
              child: CustomPaint(
                painter: CandlestickPainter(
                  klines: widget.klines,
                  timestamps: widget.timestamps,
                  timeframe: widget.timeframe,
                  currentPrice: widget.currentPrice,
                  showGrid: true,
                  showLabels: false,
                  upColor: Colors.green,
                  downColor: Colors.red,
                  rightPadding: 100,
                ),
              ),
            ),
          ),

          // Оси координат
          Positioned.fill(
            child: CustomPaint(
              painter: _AxisPainter(
                klines: widget.klines,
                timestamps: widget.timestamps,
                timeframe: widget.timeframe,
                rightPadding: 100,
                currentPrice: widget.currentPrice,
              ),
            ),
          ),
        ],
      ),
    );
  }
}



class _AxisPainter extends CustomPainter {
  final List<List<dynamic>> klines;
  final List<String> timestamps;
  final String timeframe;
  final double rightPadding;
  final double? currentPrice;

  _AxisPainter({
    required this.klines,
    required this.timestamps,
    required this.timeframe,
    this.rightPadding = 100.0,
    this.currentPrice,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (klines.isEmpty) return;

    // Извлекаем данные для расчета диапазона цен
    final List<double> highs = [];
    final List<double> lows = [];

    for (var kline in klines) {
      try {
        highs.add(double.parse(kline[2].toString()));
        lows.add(double.parse(kline[3].toString()));
      } catch (e) {
        continue;
      }
    }

    if (highs.isEmpty || lows.isEmpty) return;

    final double minValue = lows.reduce((a, b) => a < b ? a : b);
    final double maxValue = highs.reduce((a, b) => a > b ? a : b);
    final padding = (maxValue - minValue) * 0.1;
    final adjustedMinValue = minValue - padding;
    final adjustedMaxValue = maxValue + padding;

    _drawPriceAxis(canvas, size, adjustedMinValue, adjustedMaxValue);
    _drawTimeAxis(canvas, size);

    // Рисуем метку текущей цены
    if (currentPrice != null) {
      _drawCurrentPriceLabel(canvas, size, adjustedMinValue, adjustedMaxValue);
    }
  }

  void _drawPriceAxis(Canvas canvas, Size size, double minValue, double maxValue) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Рисуем метки цен справа
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      final price = maxValue - (i / 4) * (maxValue - minValue);

      textPainter.text = TextSpan(
        text: _formatPrice(price),
        style: const TextStyle(color: Colors.white, fontSize: 10),
      );

      textPainter.layout();

      // Рисуем фон для текста
      final rect = Rect.fromLTWH(
        size.width - rightPadding + 2,
        y - textPainter.height / 2 - 2,
        rightPadding - 4,
        textPainter.height + 4,
      );

      canvas.drawRect(
        rect,
        Paint()..color = Colors.black54,
      );

      textPainter.paint(canvas, Offset(size.width - rightPadding + 4, y - textPainter.height / 2));
    }
  }

  void _drawTimeAxis(Canvas canvas, Size size) {
    if (timestamps.isEmpty) return;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    // Определяем количество меток времени
    final labelCount = 6;
    final step = (timestamps.length - 1) / (labelCount - 1);

    for (int i = 0; i < labelCount; i++) {
      final index = (i * step).round();
      if (index >= timestamps.length) continue;

      final x = (size.width - rightPadding) * i / (labelCount - 1);

      textPainter.text = TextSpan(
        text: timestamps[index],
        style: const TextStyle(color: Colors.white, fontSize: 9),
      );

      textPainter.layout();
      // Рисуем метки времени внизу графика
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, size.height - 25));
    }
  }

  void _drawCurrentPriceLabel(Canvas canvas, Size size, double minValue, double maxValue) {
    if (currentPrice == null || klines.isEmpty) return;

    final double priceRange = maxValue - minValue;
    final double y = size.height - (currentPrice! - minValue) / priceRange * size.height;

    // Определяем цвет на основе направления движения цены
    final lastCandle = klines.last;
    final lastClosePrice = double.parse(lastCandle[4].toString());
    final isUp = currentPrice! >= lastClosePrice;
    final labelColor = isUp ? Colors.green : Colors.red;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    textPainter.text = TextSpan(
      text: '\$${_formatPrice(currentPrice!)}',
      style: TextStyle(
        color: Colors.white,
        fontSize: 10,
        fontWeight: FontWeight.bold,
      ),
    );

    textPainter.layout();

    // Рисуем цветной фон для метки текущей цены
    final rect = Rect.fromLTWH(
      size.width - rightPadding + 2,
      y - textPainter.height / 2 - 2,
      rightPadding - 4,
      textPainter.height + 4,
    );

    canvas.drawRect(
      rect,
      Paint()..color = labelColor.withOpacity(0.8),
    );

    textPainter.paint(canvas, Offset(size.width - rightPadding + 4, y - textPainter.height / 2));
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
