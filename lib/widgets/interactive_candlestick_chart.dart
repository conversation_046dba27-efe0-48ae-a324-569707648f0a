import 'package:flutter/material.dart';
import 'candlestick_chart.dart';

/// Интерактивный график свечей без зума
class InteractiveCandlestickChart extends StatefulWidget {
  final List<List<dynamic>> klines;
  final List<String> timestamps;
  final String timeframe;
  final double? currentPrice;
  final String? symbol;

  const InteractiveCandlestickChart({
    Key? key,
    required this.klines,
    required this.timestamps,
    required this.timeframe,
    this.currentPrice,
    this.symbol,
  }) : super(key: key);

  @override
  State<InteractiveCandlestickChart> createState() => _InteractiveCandlestickChartState();
}

class _InteractiveCandlestickChartState extends State<InteractiveCandlestickChart> {
  List<List<dynamic>> _allKlines = [];
  List<String> _allTimestamps = [];
  double _scrollOffset = 0.0;
  bool _isLoadingOlderData = false;

  @override
  void initState() {
    super.initState();
    _allKlines = List.from(widget.klines);
    _allTimestamps = List.from(widget.timestamps);
  }

  @override
  void didUpdateWidget(InteractiveCandlestickChart oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.klines != widget.klines) {
      _allKlines = List.from(widget.klines);
      _allTimestamps = List.from(widget.timestamps);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 350,
      margin: const EdgeInsets.only(bottom: 20),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: GestureDetector(
        onPanUpdate: (details) {
          setState(() {
            // Улучшенная логика прокрутки
            final sensitivity = 1.5; // Чувствительность прокрутки
            _scrollOffset += details.delta.dx * sensitivity;

            // Ограничиваем прокрутку вправо (не можем прокручивать в будущее)
            if (_scrollOffset > 0) {
              _scrollOffset = 0;
            }

            // Ограничиваем прокрутку влево (не уходим слишком далеко)
            final candleWidth = 8.0;
            final chartWidth = MediaQuery.of(context).size.width - 100; // Учитываем rightPadding
            final visibleCandles = (chartWidth / candleWidth).floor();
            final maxScrollLeft = -(_allKlines.length - visibleCandles) * candleWidth;

            if (_scrollOffset < maxScrollLeft) {
              _scrollOffset = maxScrollLeft;
            }

            // Проверяем, нужно ли загрузить более старые данные
            if (_scrollOffset < maxScrollLeft * 0.9 && !_isLoadingOlderData && widget.symbol != null) {
              _loadOlderData();
            }
          });
        },
        child: Stack(
          children: [
            // Основной график
            Positioned.fill(
              child: Container(
                margin: const EdgeInsets.only(bottom: 30, right: 100),
                child: CustomPaint(
                  painter: ScrollableCandlestickPainter(
                    klines: _allKlines,
                    timestamps: _allTimestamps,
                    timeframe: widget.timeframe,
                    currentPrice: widget.currentPrice,
                    showGrid: true,
                    showLabels: false,
                    upColor: Colors.green,
                    downColor: Colors.red,
                    rightPadding: 100,
                    scrollOffset: _scrollOffset,
                  ),
                ),
              ),
            ),

            // Оси координат
            Positioned.fill(
              child: CustomPaint(
                painter: _AxisPainter(
                  klines: _allKlines,
                  timestamps: _allTimestamps,
                  timeframe: widget.timeframe,
                  rightPadding: 100,
                  currentPrice: widget.currentPrice,
                  scrollOffset: _scrollOffset,
                ),
              ),
            ),

            // Индикатор загрузки
            if (_isLoadingOlderData)
              Positioned(
                top: 10,
                left: 10,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.black54,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        width: 12,
                        height: 12,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      ),
                      SizedBox(width: 6),
                      Text(
                        'Загрузка...',
                        style: TextStyle(color: Colors.white, fontSize: 10),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Future<void> _loadOlderData() async {
    if (_isLoadingOlderData || widget.symbol == null || _allKlines.isEmpty) return;

    setState(() {
      _isLoadingOlderData = true;
    });

    try {
      // Генерируем дополнительные моковые данные для демонстрации
      print('📊 Загружаем более старые данные для ${widget.symbol}');

      await Future.delayed(Duration(milliseconds: 500)); // Имитация загрузки

      // Генерируем 50 дополнительных свечей
      final List<List<dynamic>> olderData = [];
      final List<String> newTimestamps = [];

      final oldestTimestamp = int.parse(_allKlines.first[0].toString());
      final oldestDate = DateTime.fromMillisecondsSinceEpoch(oldestTimestamp);

      // Вычисляем правильный интервал времени в зависимости от таймфрейма
      Duration timeInterval;
      switch (widget.timeframe) {
        case '30М':
        case '30m':
          timeInterval = const Duration(minutes: 30);
          break;
        case '1Ч':
        case '1h':
          timeInterval = const Duration(hours: 1);
          break;
        case '4Ч':
        case '4h':
          timeInterval = const Duration(hours: 4);
          break;
        case '1Д':
        case '1d':
          timeInterval = const Duration(days: 1);
          break;
        case '1Н':
        case '1w':
          timeInterval = const Duration(days: 7);
          break;
        default:
          timeInterval = const Duration(hours: 1);
      }

      for (int i = 50; i > 0; i--) {
        final timestamp = oldestDate.subtract(timeInterval * i).millisecondsSinceEpoch;
        final basePrice = 50000.0; // Базовая цена для демонстрации
        final variation = (i % 10 - 5) * 100; // Небольшие вариации

        final open = basePrice + variation;
        final close = open + (i % 3 - 1) * 50;
        final high = [open, close].reduce((a, b) => a > b ? a : b) + 25;
        final low = [open, close].reduce((a, b) => a < b ? a : b) - 25;

        olderData.add([
          timestamp,
          open.toStringAsFixed(2),
          high.toStringAsFixed(2),
          low.toStringAsFixed(2),
          close.toStringAsFixed(2),
          '1000000',
          timestamp + timeInterval.inMilliseconds,
          '0',
          0,
          '0',
          '0',
          '0'
        ]);

        final date = DateTime.fromMillisecondsSinceEpoch(timestamp);

        // Для 4ч и выше показываем дату, для меньших таймфреймов - время
        print('📅 Timeframe: "${widget.timeframe}", Date: $date'); // Отладка
        if (widget.timeframe == '4h' || widget.timeframe == '4Ч' || widget.timeframe == '1d' || widget.timeframe == '1Д' || widget.timeframe == '1w' || widget.timeframe == '1Н') {
          final dateStr = '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}';
          print('📅 Показываем ДАТУ: $dateStr'); // Отладка
          newTimestamps.add(dateStr);
        } else {
          final timeStr = '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
          print('📅 Показываем ВРЕМЯ: $timeStr'); // Отладка
          newTimestamps.add(timeStr);
        }
      }

      if (olderData.isNotEmpty) {
        setState(() {
          // Добавляем старые данные в начало списка
          _allKlines.insertAll(0, olderData);
          _allTimestamps.insertAll(0, newTimestamps);

          // Корректируем смещение прокрутки, чтобы график не прыгал
          _scrollOffset += olderData.length * 8.0;
        });

        print('📊 Загружено ${olderData.length} старых свечей');
      }
    } catch (e) {
      print('❌ Ошибка загрузки старых данных: $e');
    } finally {
      setState(() {
        _isLoadingOlderData = false;
      });
    }
  }
}



/// Прокручиваемый painter для свечного графика
class ScrollableCandlestickPainter extends CustomPainter {
  final List<List<dynamic>> klines;
  final List<String> timestamps;
  final String timeframe;
  final double? currentPrice;
  final bool showGrid;
  final bool showLabels;
  final Color upColor;
  final Color downColor;
  final double rightPadding;
  final double scrollOffset;

  ScrollableCandlestickPainter({
    required this.klines,
    required this.timestamps,
    required this.timeframe,
    this.currentPrice,
    this.showGrid = true,
    this.showLabels = true,
    this.upColor = Colors.green,
    this.downColor = Colors.red,
    this.rightPadding = 100.0,
    this.scrollOffset = 0.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (klines.isEmpty) return;

    final chartWidth = size.width - rightPadding;
    final chartHeight = size.height;

    // Вычисляем видимый диапазон данных с учетом прокрутки
    final candleWidth = 8.0;
    final rightMargin = 40.0; // Отступ справа для видимости последней свечи
    final effectiveChartWidth = chartWidth - rightMargin;
    final visibleCandles = (effectiveChartWidth / candleWidth).floor();
    final totalCandles = klines.length;

    // Определяем начальный индекс с учетом прокрутки
    final scrolledCandles = (-scrollOffset / candleWidth).floor();
    final startIndex = (totalCandles - visibleCandles - scrolledCandles).clamp(0, totalCandles - 1);
    final endIndex = (startIndex + visibleCandles).clamp(0, totalCandles);

    if (startIndex >= endIndex) return;

    // Извлекаем видимые данные
    final visibleKlines = klines.sublist(startIndex, endIndex);

    // Находим min/max для видимых данных
    double minPrice = double.infinity;
    double maxPrice = double.negativeInfinity;

    for (var kline in visibleKlines) {
      final high = double.parse(kline[2].toString());
      final low = double.parse(kline[3].toString());
      minPrice = minPrice < low ? minPrice : low;
      maxPrice = maxPrice > high ? maxPrice : high;
    }

    final priceRange = maxPrice - minPrice;
    if (priceRange == 0) return;

    // Рисуем сетку
    if (showGrid) {
      _drawGrid(canvas, size, chartWidth, chartHeight);
    }

    // Рисуем свечи
    for (int i = 0; i < visibleKlines.length; i++) {
      final kline = visibleKlines[i];
      final x = i * candleWidth + candleWidth / 2;

      final open = double.parse(kline[1].toString());
      final high = double.parse(kline[2].toString());
      final low = double.parse(kline[3].toString());
      final close = double.parse(kline[4].toString());

      final isUp = close >= open;
      final color = isUp ? upColor : downColor;

      // Конвертируем цены в координаты Y
      final highY = chartHeight - (high - minPrice) / priceRange * chartHeight;
      final lowY = chartHeight - (low - minPrice) / priceRange * chartHeight;
      final openY = chartHeight - (open - minPrice) / priceRange * chartHeight;
      final closeY = chartHeight - (close - minPrice) / priceRange * chartHeight;

      // Рисуем тень (high-low линия)
      canvas.drawLine(
        Offset(x, highY),
        Offset(x, lowY),
        Paint()
          ..color = color
          ..strokeWidth = 1,
      );

      // Рисуем тело свечи
      final bodyTop = isUp ? closeY : openY;
      final bodyBottom = isUp ? openY : closeY;
      final bodyHeight = (bodyBottom - bodyTop).abs().clamp(1.0, double.infinity);

      canvas.drawRect(
        Rect.fromLTWH(x - candleWidth / 4, bodyTop, candleWidth / 2, bodyHeight),
        Paint()
          ..color = color
          ..style = isUp ? PaintingStyle.stroke : PaintingStyle.fill
          ..strokeWidth = 1,
      );
    }

    // Рисуем штрих-пунктирную линию текущей цены
    if (currentPrice != null && currentPrice! >= minPrice && currentPrice! <= maxPrice && visibleKlines.isNotEmpty) {
      final currentPriceY = chartHeight - (currentPrice! - minPrice) / priceRange * chartHeight;

      // Определяем цвет на основе цвета последней свечи
      final lastCandle = visibleKlines.last;
      final openPrice = double.parse(lastCandle[1].toString());
      final closePrice = double.parse(lastCandle[4].toString());
      final isGreenCandle = closePrice >= openPrice;
      final lineColor = isGreenCandle ? Colors.green : Colors.red;

      _drawDashedLine(canvas, Offset(0, currentPriceY), Offset(chartWidth, currentPriceY), lineColor);
    }
  }

  void _drawGrid(Canvas canvas, Size size, double chartWidth, double chartHeight) {
    final gridPaint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..strokeWidth = 0.5;

    // Горизонтальные линии
    for (int i = 0; i <= 4; i++) {
      final y = chartHeight * i / 4;
      canvas.drawLine(
        Offset(0, y),
        Offset(chartWidth, y),
        gridPaint,
      );
    }

    // Вертикальные линии
    for (int i = 0; i <= 6; i++) {
      final x = chartWidth * i / 6;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, chartHeight),
        gridPaint,
      );
    }
  }

  void _drawDashedLine(Canvas canvas, Offset start, Offset end, Color color) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1.5;

    const dashWidth = 5.0;
    const dashSpace = 3.0;

    final distance = (end - start).distance;
    final dashCount = (distance / (dashWidth + dashSpace)).floor();

    for (int i = 0; i < dashCount; i++) {
      final startOffset = start + (end - start) * (i * (dashWidth + dashSpace) / distance);
      final endOffset = start + (end - start) * ((i * (dashWidth + dashSpace) + dashWidth) / distance);

      canvas.drawLine(startOffset, endOffset, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class _AxisPainter extends CustomPainter {
  final List<List<dynamic>> klines;
  final List<String> timestamps;
  final String timeframe;
  final double rightPadding;
  final double? currentPrice;
  final double scrollOffset;

  _AxisPainter({
    required this.klines,
    required this.timestamps,
    required this.timeframe,
    this.rightPadding = 100.0,
    this.currentPrice,
    this.scrollOffset = 0.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (klines.isEmpty) return;

    final chartWidth = size.width - rightPadding;

    // Вычисляем видимый диапазон данных с учетом прокрутки
    final candleWidth = 8.0;
    final rightMargin = 40.0; // Отступ справа для видимости последней свечи
    final effectiveChartWidth = chartWidth - rightMargin;
    final visibleCandles = (effectiveChartWidth / candleWidth).floor();
    final totalCandles = klines.length;

    // Определяем начальный индекс с учетом прокрутки
    final scrolledCandles = (-scrollOffset / candleWidth).floor();
    final startIndex = (totalCandles - visibleCandles - scrolledCandles).clamp(0, totalCandles - 1);
    final endIndex = (startIndex + visibleCandles).clamp(0, totalCandles);

    if (startIndex >= endIndex) return;

    // Извлекаем видимые данные
    final visibleKlines = klines.sublist(startIndex, endIndex);
    final visibleTimestamps = timestamps.length > endIndex
        ? timestamps.sublist(startIndex, endIndex)
        : timestamps;

    // Извлекаем данные для расчета диапазона цен (только видимые)
    final List<double> highs = [];
    final List<double> lows = [];

    for (var kline in visibleKlines) {
      try {
        highs.add(double.parse(kline[2].toString()));
        lows.add(double.parse(kline[3].toString()));
      } catch (e) {
        continue;
      }
    }

    if (highs.isEmpty || lows.isEmpty) return;

    final double minValue = lows.reduce((a, b) => a < b ? a : b);
    final double maxValue = highs.reduce((a, b) => a > b ? a : b);
    final padding = (maxValue - minValue) * 0.1;
    final adjustedMinValue = minValue - padding;
    final adjustedMaxValue = maxValue + padding;

    _drawPriceAxis(canvas, size, adjustedMinValue, adjustedMaxValue);
    _drawTimeAxis(canvas, size, visibleTimestamps);

    // Рисуем метку текущей цены
    if (currentPrice != null) {
      _drawCurrentPriceLabel(canvas, size, adjustedMinValue, adjustedMaxValue, visibleKlines);
    }
  }

  void _drawPriceAxis(Canvas canvas, Size size, double minValue, double maxValue) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Рисуем метки цен справа
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      final price = maxValue - (i / 4) * (maxValue - minValue);

      textPainter.text = TextSpan(
        text: _formatPrice(price),
        style: const TextStyle(color: Colors.white, fontSize: 10),
      );

      textPainter.layout();

      // Рисуем фон для текста
      final rect = Rect.fromLTWH(
        size.width - rightPadding + 2,
        y - textPainter.height / 2 - 2,
        rightPadding - 4,
        textPainter.height + 4,
      );

      canvas.drawRect(
        rect,
        Paint()..color = Colors.black54,
      );

      textPainter.paint(canvas, Offset(size.width - rightPadding + 4, y - textPainter.height / 2));
    }
  }

  void _drawTimeAxis(Canvas canvas, Size size, List<String> visibleTimestamps) {
    if (visibleTimestamps.isEmpty) return;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    final chartWidth = size.width - rightPadding;

    // Определяем количество меток времени (меньше, чтобы не перекрывались)
    final labelCount = 4;
    final step = (visibleTimestamps.length - 1) / (labelCount - 1);

    for (int i = 0; i < labelCount; i++) {
      final index = (i * step).round();
      if (index >= visibleTimestamps.length) continue;

      final x = chartWidth * i / (labelCount - 1);

      textPainter.text = TextSpan(
        text: visibleTimestamps[index],
        style: const TextStyle(color: Colors.white70, fontSize: 10),
      );

      textPainter.layout();
      // Рисуем метки времени внизу графика
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, size.height - 20));
    }
  }

  void _drawCurrentPriceLabel(Canvas canvas, Size size, double minValue, double maxValue, List<List<dynamic>> visibleKlines) {
    if (currentPrice == null || visibleKlines.isEmpty) return;

    final double priceRange = maxValue - minValue;
    final double y = size.height - (currentPrice! - minValue) / priceRange * size.height;

    // Определяем цвет на основе направления движения цены
    final lastCandle = visibleKlines.last;
    final lastClosePrice = double.parse(lastCandle[4].toString());
    final isUp = currentPrice! >= lastClosePrice;
    final labelColor = isUp ? Colors.green : Colors.red;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    textPainter.text = TextSpan(
      text: '\$${_formatPrice(currentPrice!)}',
      style: TextStyle(
        color: Colors.white,
        fontSize: 11,
        fontWeight: FontWeight.bold,
      ),
    );

    textPainter.layout();

    // Рисуем цветной фон для метки текущей цены
    final rect = Rect.fromLTWH(
      size.width - rightPadding + 2,
      y - textPainter.height / 2 - 3,
      rightPadding - 4,
      textPainter.height + 6,
    );

    canvas.drawRect(
      rect,
      Paint()..color = labelColor.withOpacity(0.9),
    );

    // Рисуем рамку
    canvas.drawRect(
      rect,
      Paint()
        ..color = Colors.white
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1,
    );

    textPainter.paint(canvas, Offset(size.width - rightPadding + 4, y - textPainter.height / 2));
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
