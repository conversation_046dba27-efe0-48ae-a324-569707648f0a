import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'candlestick_chart.dart';

class InteractiveCandlestick<PERSON>hart extends StatefulWidget {
  final List<List<dynamic>> klines;
  final List<String> timestamps;
  final String timeframe;
  final double? currentPrice;

  const InteractiveCandlestickChart({
    Key? key,
    required this.klines,
    required this.timestamps,
    required this.timeframe,
    this.currentPrice,
  }) : super(key: key);

  @override
  State<InteractiveCandlestickChart> createState() => _InteractiveCandlestickChartState();
}

class _InteractiveCandlestickChartState extends State<InteractiveCandlestickChart> {
  double _scale = 1.0;
  Offset _offset = Offset.zero;
  Offset? _lastPanPosition;
  
  // Ограничения для зума и панорамирования
  static const double _minScale = 0.5;
  static const double _maxScale = 5.0;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 350, // Увеличиваем высоту для осей
      margin: const EdgeInsets.only(bottom: 20),
      child: Listener(
        onPointerSignal: (pointerSignal) {
          if (pointerSignal is PointerScrollEvent) {
            setState(() {
              // Зум с помощью колесика мыши
              final delta = pointerSignal.scrollDelta.dy;
              final zoomFactor = delta > 0 ? 0.9 : 1.1;
              _scale = (_scale * zoomFactor).clamp(_minScale, _maxScale);
            });
          }
        },
        child: GestureDetector(
          onScaleStart: (details) {
            _lastPanPosition = details.focalPoint;
          },
          onScaleUpdate: (details) {
            setState(() {
              // Обновляем масштаб
              if (details.scale != 1.0) {
                _scale = (_scale * details.scale).clamp(_minScale, _maxScale);
              }

              // Обновляем смещение для панорамирования
              if (_lastPanPosition != null) {
                final delta = details.focalPoint - _lastPanPosition!;
                _offset = Offset(
                  (_offset.dx + delta.dx).clamp(-1000.0, 1000.0),
                  (_offset.dy + delta.dy).clamp(-200.0, 200.0),
                );
                _lastPanPosition = details.focalPoint;
              }
            });
          },
          onScaleEnd: (details) {
            _lastPanPosition = null;
          },
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.8),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.withOpacity(0.3)),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  // Основной график без осей
                  Positioned.fill(
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 30, right: 100), // Увеличиваем место для осей
                      child: CustomPaint(
                        painter: CandlestickPainter(
                          klines: widget.klines,
                          timestamps: widget.timestamps,
                          timeframe: widget.timeframe,
                          currentPrice: widget.currentPrice,
                          showGrid: true,
                          showLabels: false, // Отключаем встроенные метки
                          upColor: Colors.green,
                          downColor: Colors.red,
                          rightPadding: 100,
                          scale: _scale,
                          offset: _offset,
                        ),
                      ),
                    ),
                  ),
                  // Фиксированные оси поверх графика
                  Positioned.fill(
                    child: CustomPaint(
                      painter: _AxisPainter(
                        klines: widget.klines,
                        timestamps: widget.timestamps,
                        timeframe: widget.timeframe,
                        rightPadding: 100,
                        currentPrice: widget.currentPrice,
                      ),
                    ),
                  ),
                  // Индикатор зума
                  Positioned(
                    top: 10,
                    left: 10,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black54,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'Zoom: ${_scale.toStringAsFixed(1)}x',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ),

                  // Кнопки управления зумом
                  Positioned(
                    top: 10,
                    right: 10,
                    child: Column(
                      children: [
                        _buildZoomButton(Icons.add, () {
                          setState(() {
                            _scale = (_scale * 1.2).clamp(_minScale, _maxScale);
                          });
                        }),
                        const SizedBox(height: 4),
                        _buildZoomButton(Icons.remove, () {
                          setState(() {
                            _scale = (_scale / 1.2).clamp(_minScale, _maxScale);
                          });
                        }),
                        const SizedBox(height: 4),
                        _buildZoomButton(Icons.refresh, () {
                          setState(() {
                            _scale = 1.0;
                            _offset = Offset.zero;
                          });
                        }),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildZoomButton(IconData icon, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 28,
        height: 28,
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: Colors.white24, width: 1),
        ),
        child: Icon(icon, size: 16, color: Colors.white),
      ),
    );
  }
}

class _AxisPainter extends CustomPainter {
  final List<List<dynamic>> klines;
  final List<String> timestamps;
  final String timeframe;
  final double rightPadding;
  final double? currentPrice;

  _AxisPainter({
    required this.klines,
    required this.timestamps,
    required this.timeframe,
    this.rightPadding = 100.0,
    this.currentPrice,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (klines.isEmpty) return;

    // Извлекаем данные для расчета диапазона цен
    final List<double> highs = [];
    final List<double> lows = [];

    for (var kline in klines) {
      try {
        highs.add(double.parse(kline[2].toString()));
        lows.add(double.parse(kline[3].toString()));
      } catch (e) {
        continue;
      }
    }

    if (highs.isEmpty || lows.isEmpty) return;

    final double minValue = lows.reduce((a, b) => a < b ? a : b);
    final double maxValue = highs.reduce((a, b) => a > b ? a : b);
    final padding = (maxValue - minValue) * 0.1;
    final adjustedMinValue = minValue - padding;
    final adjustedMaxValue = maxValue + padding;

    _drawPriceAxis(canvas, size, adjustedMinValue, adjustedMaxValue);
    _drawTimeAxis(canvas, size);

    // Рисуем метку текущей цены
    if (currentPrice != null) {
      _drawCurrentPriceLabel(canvas, size, adjustedMinValue, adjustedMaxValue);
    }
  }

  void _drawPriceAxis(Canvas canvas, Size size, double minValue, double maxValue) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Рисуем метки цен справа
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      final price = maxValue - (i / 4) * (maxValue - minValue);

      textPainter.text = TextSpan(
        text: _formatPrice(price),
        style: const TextStyle(color: Colors.white, fontSize: 10),
      );

      textPainter.layout();

      // Рисуем фон для текста
      final rect = Rect.fromLTWH(
        size.width - rightPadding + 2,
        y - textPainter.height / 2 - 2,
        rightPadding - 4,
        textPainter.height + 4,
      );

      canvas.drawRect(
        rect,
        Paint()..color = Colors.black54,
      );

      textPainter.paint(canvas, Offset(size.width - rightPadding + 4, y - textPainter.height / 2));
    }
  }

  void _drawTimeAxis(Canvas canvas, Size size) {
    if (timestamps.isEmpty) return;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    // Определяем количество меток времени
    final labelCount = 6;
    final step = (timestamps.length - 1) / (labelCount - 1);

    for (int i = 0; i < labelCount; i++) {
      final index = (i * step).round();
      if (index >= timestamps.length) continue;

      final x = (size.width - rightPadding) * i / (labelCount - 1);

      textPainter.text = TextSpan(
        text: timestamps[index],
        style: const TextStyle(color: Colors.white, fontSize: 9),
      );

      textPainter.layout();
      // Рисуем метки времени внизу графика
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, size.height - 25));
    }
  }

  void _drawCurrentPriceLabel(Canvas canvas, Size size, double minValue, double maxValue) {
    if (currentPrice == null || klines.isEmpty) return;

    final double priceRange = maxValue - minValue;
    final double y = size.height - (currentPrice! - minValue) / priceRange * size.height;

    // Определяем цвет на основе направления движения цены
    final lastCandle = klines.last;
    final lastClosePrice = double.parse(lastCandle[4].toString());
    final isUp = currentPrice! >= lastClosePrice;
    final labelColor = isUp ? Colors.green : Colors.red;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    textPainter.text = TextSpan(
      text: '\$${_formatPrice(currentPrice!)}',
      style: TextStyle(
        color: Colors.white,
        fontSize: 10,
        fontWeight: FontWeight.bold,
      ),
    );

    textPainter.layout();

    // Рисуем цветной фон для метки текущей цены
    final rect = Rect.fromLTWH(
      size.width - rightPadding + 2,
      y - textPainter.height / 2 - 2,
      rightPadding - 4,
      textPainter.height + 4,
    );

    canvas.drawRect(
      rect,
      Paint()..color = labelColor.withOpacity(0.8),
    );

    textPainter.paint(canvas, Offset(size.width - rightPadding + 4, y - textPainter.height / 2));
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
