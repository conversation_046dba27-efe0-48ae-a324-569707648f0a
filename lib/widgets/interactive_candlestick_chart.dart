import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'candlestick_chart.dart';

class InteractiveCandlestick<PERSON>hart extends StatefulWidget {
  final List<List<dynamic>> klines;
  final List<String> timestamps;
  final String timeframe;
  final double? currentPrice;

  const InteractiveCandlestickChart({
    Key? key,
    required this.klines,
    required this.timestamps,
    required this.timeframe,
    this.currentPrice,
  }) : super(key: key);

  @override
  State<InteractiveCandlestickChart> createState() => _InteractiveCandlestickChartState();
}

class _InteractiveCandlestickChartState extends State<InteractiveCandlestickChart> {
  double _scale = 1.0;
  Offset _offset = Offset.zero;
  Offset? _lastPanPosition;
  
  // Ограничения для зума и панорамирования
  static const double _minScale = 0.5;
  static const double _maxScale = 5.0;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 350, // Увеличиваем высоту для осей
      margin: const EdgeInsets.only(bottom: 20),
      child: Listener(
        onPointerSignal: (pointerSignal) {
          if (pointerSignal is PointerScrollEvent) {
            setState(() {
              // Зум с помощью колесика мыши
              final delta = pointerSignal.scrollDelta.dy;
              final zoomFactor = delta > 0 ? 0.9 : 1.1;
              _scale = (_scale * zoomFactor).clamp(_minScale, _maxScale);
            });
          }
        },
        child: GestureDetector(
          onScaleStart: (details) {
            _lastPanPosition = details.focalPoint;
          },
          onScaleUpdate: (details) {
            setState(() {
              // Обновляем масштаб
              final newScale = (_scale * details.scale).clamp(_minScale, _maxScale);
              
              // Обновляем смещение для панорамирования
              if (_lastPanPosition != null) {
                final delta = details.focalPoint - _lastPanPosition!;
                _offset = Offset(
                  (_offset.dx + delta.dx).clamp(-500.0, 500.0), // Ограничиваем панорамирование
                  _offset.dy,
                );
                _lastPanPosition = details.focalPoint;
              }
              
              _scale = newScale;
            });
          },
          onScaleEnd: (details) {
            _lastPanPosition = null;
          },
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.8),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.withOpacity(0.3)),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  // Основной график
                  Positioned.fill(
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 30, right: 60), // Место для осей
                      child: CustomPaint(
                        painter: CandlestickPainter(
                          klines: widget.klines,
                          timestamps: widget.timestamps,
                          timeframe: widget.timeframe,
                          currentPrice: widget.currentPrice,
                          showGrid: true,
                          showLabels: false, // Отключаем встроенные метки
                          upColor: Colors.green,
                          downColor: Colors.red,
                          rightPadding: 0, // Убираем внутренний отступ
                          scale: _scale,
                          offset: _offset,
                        ),
                      ),
                    ),
                  ),
                  // Индикатор зума
                  Positioned(
                    top: 10,
                    left: 10,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: Colors.black54,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'Zoom: ${_scale.toStringAsFixed(1)}x',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                      ),
                    ),
                  ),
                  // Кнопки управления зумом
                  Positioned(
                    top: 10,
                    right: 70,
                    child: Column(
                      children: [
                        _buildZoomButton(Icons.add, () {
                          setState(() {
                            _scale = (_scale * 1.2).clamp(_minScale, _maxScale);
                          });
                        }),
                        const SizedBox(height: 4),
                        _buildZoomButton(Icons.remove, () {
                          setState(() {
                            _scale = (_scale / 1.2).clamp(_minScale, _maxScale);
                          });
                        }),
                        const SizedBox(height: 4),
                        _buildZoomButton(Icons.refresh, () {
                          setState(() {
                            _scale = 1.0;
                            _offset = Offset.zero;
                          });
                        }),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildZoomButton(IconData icon, VoidCallback onPressed) {
    return Container(
      width: 24,
      height: 24,
      decoration: BoxDecoration(
        color: Colors.black54,
        borderRadius: BorderRadius.circular(4),
      ),
      child: IconButton(
        icon: Icon(icon, size: 12, color: Colors.white),
        onPressed: onPressed,
        padding: EdgeInsets.zero,
      ),
    );
  }
}
