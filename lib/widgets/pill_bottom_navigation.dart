import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../utils/device_type.dart';

class PillBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final bool showLabels;
  final double height;
  final Color backgroundColor;
  final Color activeColor;
  final Color inactiveColor;
  final Color pillColor;

  const PillBottomNavigation({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    this.showLabels = true,
    this.height = 60.0,
    this.backgroundColor = Colors.black,
    this.activeColor = Colors.white,
    this.inactiveColor = Colors.grey,
    this.pillColor = Colors.white,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isDesktop = DeviceUtils.isDesktop(context);
    
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            spreadRadius: 0,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: isDesktop
          ? _buildDesktopNavigation(context)
          : _buildMobileNavigation(context),
    );
  }

  Widget _buildMobileNavigation(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildNavItem(context, 0, Icons.article_outlined, 'News'),
        _buildNavItem(context, 1, Icons.show_chart, 'Charts'),
        _buildNavItem(context, 2, Icons.waves, 'Sinusoid'),
        _buildNavItem(context, 3, Icons.school_outlined, 'Learn'),
        _buildNavItem(context, 4, Icons.person_outline, 'Profile'),
      ],
    );
  }

  Widget _buildDesktopNavigation(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        const Spacer(),
        _buildNavItem(context, 0, Icons.article_outlined, 'News'),
        const SizedBox(width: 16),
        _buildNavItem(context, 1, Icons.show_chart, 'Charts'),
        const SizedBox(width: 16),
        _buildNavItem(context, 2, Icons.waves, 'Sinusoid'),
        const SizedBox(width: 16),
        _buildNavItem(context, 3, Icons.school_outlined, 'Learn'),
        const SizedBox(width: 16),
        _buildNavItem(context, 4, Icons.person_outline, 'Profile'),
        const Spacer(),
      ],
    );
  }

  Widget _buildNavItem(BuildContext context, int index, IconData icon, String label) {
    final bool isActive = currentIndex == index;
    
    // For active item, show a pill with icon and text
    if (isActive) {
      return GestureDetector(
        onTap: () => onTap(index),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: pillColor,
            borderRadius: BorderRadius.circular(20),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                color: Colors.black,
                size: 20,
              ),
              if (showLabels) ...[
                const SizedBox(width: 8),
                Text(
                  label,
                  style: const TextStyle(
                    color: Colors.black,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ],
            ],
          ),
        ).animate(onPlay: (controller) => controller.repeat())
         .shimmer(duration: 2000.ms, delay: 1000.ms)
         .then()
         .scale(
           begin: const Offset(1.0, 1.0),
           end: const Offset(1.05, 1.05),
           duration: 600.ms,
         )
         .then()
         .scale(
           begin: const Offset(1.05, 1.05),
           end: const Offset(1.0, 1.0),
           duration: 600.ms,
         ),
      );
    }
    
    // For inactive items, just show the icon
    return GestureDetector(
      onTap: () => onTap(index),
      child: Container(
        padding: const EdgeInsets.all(8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: inactiveColor,
              size: 20,
            ),
            if (showLabels) ...[
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: inactiveColor,
                  fontSize: 12,
                ),
              ),
            ],
          ],
        ),
      ).animate()
       .scale(
         begin: const Offset(1.0, 1.0),
         end: const Offset(1.1, 1.1),
         curve: Curves.easeInOut,
         duration: 200.ms,
       )
       .then()
       .scale(
         begin: const Offset(1.1, 1.1),
         end: const Offset(1.0, 1.0),
         curve: Curves.easeInOut,
         duration: 200.ms,
       ),
    );
  }
}
