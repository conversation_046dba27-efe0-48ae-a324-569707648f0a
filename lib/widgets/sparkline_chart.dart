import 'package:flutter/material.dart';

class SparklineChart extends StatelessWidget {
  final List<double> data;
  final Color color;
  final double? width;
  final double? height;
  final double strokeWidth;
  final bool showArea;

  const SparklineChart({
    super.key,
    required this.data,
    required this.color,
    this.width,
    this.height,
    this.strokeWidth = 2.0,
    this.showArea = true,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width,
      height: height,
      child: CustomPaint(
        size: Size.infinite,
        painter: _SparklinePainter(
          data: data,
          color: color,
          strokeWidth: strokeWidth,
          showArea: showArea,
        ),
      ),
    );
  }
}

class _SparklinePainter extends CustomPainter {
  final List<double> data;
  final Color color;
  final double strokeWidth;
  final bool showArea;

  _SparklinePainter({
    required this.data,
    required this.color,
    required this.strokeWidth,
    required this.showArea,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (data.isEmpty) return;

    final double width = size.width;
    final double height = size.height;

    // Find min and max values
    double minValue = data.reduce((a, b) => a < b ? a : b);
    double maxValue = data.reduce((a, b) => a > b ? a : b);
    
    // Ensure there's a range to work with
    if (maxValue - minValue < 0.000001) {
      maxValue = minValue + 1;
    }

    // Calculate scaling factors
    final double xScale = width / (data.length - 1);
    final double yScale = height / (maxValue - minValue);

    // Create path for the line
    final Path linePath = Path();
    
    // Move to the first point
    linePath.moveTo(0, height - (data[0] - minValue) * yScale);

    // Add points to the path
    for (int i = 1; i < data.length; i++) {
      final double x = i * xScale;
      final double y = height - (data[i] - minValue) * yScale;
      linePath.lineTo(x, y);
    }

    // Create paint for the line
    final Paint linePaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    // Draw the line
    canvas.drawPath(linePath, linePaint);

    // Create and draw the area if needed
    if (showArea) {
      final Path areaPath = Path.from(linePath);
      
      // Complete the path to form a closed shape
      areaPath.lineTo(width, height);
      areaPath.lineTo(0, height);
      areaPath.close();

      // Create paint for the area
      final Paint areaPaint = Paint()
        ..color = color.withOpacity(0.2)
        ..style = PaintingStyle.fill;

      // Draw the area
      canvas.drawPath(areaPath, areaPaint);
    }
  }

  @override
  bool shouldRepaint(_SparklinePainter oldDelegate) {
    return oldDelegate.data != data ||
        oldDelegate.color != color ||
        oldDelegate.strokeWidth != strokeWidth ||
        oldDelegate.showArea != showArea;
  }
}
