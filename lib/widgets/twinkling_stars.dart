import 'dart:math' as math;
import 'package:flutter/material.dart';

/// Класс для представления мерцающей звезды
class Star {
  /// Позиция звезды
  final Offset position;

  /// Размер звезды
  final double size;

  /// Яркость звезды (от 0.0 до 1.0)
  double brightness;

  /// Скорость изменения яркости
  final double twinkleSpeed;

  /// Направление изменения яркости (1 - увеличение, -1 - уменьшение)
  int twinkleDirection;

  /// Цвет звезды
  final Color color;

  Star({
    required this.position,
    required this.size,
    required this.brightness,
    required this.twinkleSpeed,
    required this.twinkleDirection,
    required this.color,
  });

  /// Обновление яркости звезды
  void update(double deltaTime) {
    // Изменяем яркость в зависимости от направления и скорости
    brightness += twinkleDirection * twinkleSpeed * deltaTime;

    // Если достигли предельных значений, меняем направление
    if (brightness >= 0.7) {
      brightness = 0.7;
      twinkleDirection = -1;
    } else if (brightness <= 0.1) {
      brightness = 0.1;
      twinkleDirection = 1;
    }
  }
}

/// Виджет для отображения мерцающих звезд
class TwinklingStars extends StatefulWidget {
  /// Количество звезд
  final int count;

  /// Минимальный размер звезды
  final double minSize;

  /// Максимальный размер звезды
  final double maxSize;

  /// Минимальная скорость мерцания
  final double minTwinkleSpeed;

  /// Максимальная скорость мерцания
  final double maxTwinkleSpeed;

  /// Цвета звезд
  final List<Color> colors;

  const TwinklingStars({
    super.key,
    this.count = 100,
    this.minSize = 1.0,
    this.maxSize = 3.0,
    this.minTwinkleSpeed = 0.2,
    this.maxTwinkleSpeed = 0.5,
    this.colors = const [
      Colors.white,
      Color(0xFFE6E6FA), // Очень светлый лавандовый
      Color(0xFFF0F8FF), // Светло-голубой
      Color(0xFFFFFAF0), // Светло-желтый
    ],
  });

  @override
  State<TwinklingStars> createState() => _TwinklingStarsState();
}

class _TwinklingStarsState extends State<TwinklingStars> with SingleTickerProviderStateMixin {
  /// Список звезд
  late List<Star> _stars;

  /// Контроллер анимации
  late AnimationController _controller;

  /// Время последнего обновления
  int _lastUpdateTime = 0;

  @override
  void initState() {
    super.initState();

    // Инициализируем контроллер анимации
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 16), // ~60 FPS
    )..repeat();

    // Инициализируем звезды
    _initStars();

    // Инициализируем время
    _lastUpdateTime = DateTime.now().millisecondsSinceEpoch;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  /// Инициализация звезд
  void _initStars() {
    final random = math.Random();
    _stars = List.generate(widget.count, (_) {
      // Случайная позиция
      final position = Offset(
        random.nextDouble() * 1.0, // Нормализованная позиция по X (0.0 - 1.0)
        random.nextDouble() * 1.0, // Нормализованная позиция по Y (0.0 - 1.0)
      );

      // Случайный размер
      final size = widget.minSize + random.nextDouble() * (widget.maxSize - widget.minSize);

      // Случайная начальная яркость с более широким диапазоном
      final brightness = 0.1 + random.nextDouble() * 0.6;

      // Случайная скорость мерцания
      final twinkleSpeed = widget.minTwinkleSpeed + random.nextDouble() * (widget.maxTwinkleSpeed - widget.minTwinkleSpeed);

      // Случайное направление мерцания
      final twinkleDirection = random.nextBool() ? 1 : -1;

      // Случайный цвет из списка
      final color = widget.colors[random.nextInt(widget.colors.length)];

      return Star(
        position: position,
        size: size,
        brightness: brightness,
        twinkleSpeed: twinkleSpeed,
        twinkleDirection: twinkleDirection,
        color: color,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        final size = MediaQuery.of(context).size;
        final currentTime = DateTime.now().millisecondsSinceEpoch;
        final deltaTime = (currentTime - _lastUpdateTime) / 1000.0; // в секундах

        // Обновляем звезды
        for (var star in _stars) {
          star.update(deltaTime);
        }

        // Обновляем время последнего обновления
        _lastUpdateTime = currentTime;

        return CustomPaint(
          size: size,
          painter: StarsPainter(_stars, size),
        );
      },
    );
  }
}

/// Painter для отрисовки звезд
class StarsPainter extends CustomPainter {
  final List<Star> stars;
  final Size screenSize;

  StarsPainter(this.stars, this.screenSize);

  @override
  void paint(Canvas canvas, Size size) {
    for (var star in stars) {
      // Преобразуем нормализованные координаты в реальные
      final realPosition = Offset(
        star.position.dx * screenSize.width,
        star.position.dy * screenSize.height,
      );

      // Создаем paint для звезды
      final paint = Paint()
        ..color = star.color.withOpacity(star.brightness)
        ..style = PaintingStyle.fill;

      // Рисуем звезду
      canvas.drawCircle(
        realPosition,
        star.size,
        paint,
      );

      // Добавляем свечение для всех звезд с разной интенсивностью
      // Для крупных звезд делаем более заметное свечение
      if (star.size > 2.0) {
        // Внешнее свечение (большой радиус, низкая непрозрачность)
        final outerGlowPaint = Paint()
          ..color = star.color.withOpacity(star.brightness * 0.15)
          ..style = PaintingStyle.fill
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, star.size * 3.0);

        canvas.drawCircle(
          realPosition,
          star.size * 4.0,
          outerGlowPaint,
        );

        // Среднее свечение
        final middleGlowPaint = Paint()
          ..color = star.color.withOpacity(star.brightness * 0.3)
          ..style = PaintingStyle.fill
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, star.size * 1.5);

        canvas.drawCircle(
          realPosition,
          star.size * 2.0,
          middleGlowPaint,
        );

        // Внутреннее свечение (яркое)
        final innerGlowPaint = Paint()
          ..color = Colors.white.withOpacity(star.brightness * 0.7)
          ..style = PaintingStyle.fill
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, star.size * 0.5);

        canvas.drawCircle(
          realPosition,
          star.size * 1.2,
          innerGlowPaint,
        );
      }
      // Для средних звезд
      else if (star.size > 1.2) {
        // Внешнее свечение
        final outerGlowPaint = Paint()
          ..color = star.color.withOpacity(star.brightness * 0.2)
          ..style = PaintingStyle.fill
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, star.size * 1.5);

        canvas.drawCircle(
          realPosition,
          star.size * 2.0,
          outerGlowPaint,
        );

        // Внутреннее свечение
        final innerGlowPaint = Paint()
          ..color = star.color.withOpacity(star.brightness * 0.4)
          ..style = PaintingStyle.fill
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, star.size * 0.8);

        canvas.drawCircle(
          realPosition,
          star.size * 1.2,
          innerGlowPaint,
        );
      }
      // Для мелких звезд - только легкое свечение
      else {
        final glowPaint = Paint()
          ..color = star.color.withOpacity(star.brightness * 0.2)
          ..style = PaintingStyle.fill
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, star.size * 1.0);

        canvas.drawCircle(
          realPosition,
          star.size * 1.5,
          glowPaint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant StarsPainter oldDelegate) => true;
}
