import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';

class CandlestickChart extends StatefulWidget {
  final List<List<dynamic>> klines;
  final List<String> timestamps;
  final Color upColor;
  final Color downColor;
  final bool showGrid;
  final bool showLabels;
  final double? height;
  final double? width;
  final String timeframe; // Добавляем параметр для таймфрейма
  final double? currentPrice; // Добавляем параметр для текущей цены

  const CandlestickChart({
    super.key,
    required this.klines,
    required this.timestamps,
    this.upColor = Colors.green,
    this.downColor = Colors.red,
    this.showGrid = true,
    this.showLabels = true,
    this.height,
    this.width,
    this.timeframe = '1Ч', // По умолчанию часовой таймфрейм
    this.currentPrice, // Текущая цена (опционально)
  });

  @override
  State<CandlestickChart> createState() => _CandlestickChartState();
}

class _CandlestickChartState extends State<CandlestickChart> {
  double _scale = 1.0;
  double _previousScale = 1.0;
  Offset _offset = Offset.zero;
  Offset _previousOffset = Offset.zero;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      height: widget.height,
      child: Stack(
        children: [
          // Основная область графика с интерактивностью
          Positioned.fill(
            child: Listener(
              onPointerSignal: (pointerSignal) {
                if (pointerSignal is PointerScrollEvent) {
                  setState(() {
                    // Зум с помощью колесика мыши
                    final delta = pointerSignal.scrollDelta.dy;
                    final zoomFactor = delta > 0 ? 0.9 : 1.1;
                    _scale = (_scale * zoomFactor).clamp(0.5, 5.0);
                  });
                }
              },
              child: GestureDetector(
                onPanStart: (details) {
                  _previousOffset = _offset;
                },
                onPanUpdate: (details) {
                  setState(() {
                    // Ограничиваем движение только по X (время) и немного по Y
                    _offset = Offset(
                      (_offset.dx + details.delta.dx).clamp(-500.0, 500.0),
                      (_offset.dy + details.delta.dy * 0.3).clamp(-100.0, 100.0),
                    );
                  });
                },
                child: CustomPaint(
                  size: Size.infinite,
                  painter: _CandlestickPainter(
                    klines: widget.klines,
                    upColor: widget.upColor,
                    downColor: widget.downColor,
                    showGrid: widget.showGrid,
                    showLabels: false, // Отключаем метки в основном графике
                    timeframe: widget.timeframe,
                    currentPrice: widget.currentPrice,
                    rightPadding: 80.0,
                    scale: _scale,
                    offset: _offset,
                    timestamps: widget.timestamps,
                  ),
                ),
              ),
            ),
          ),
          // Фиксированные оси и метки поверх графика
          if (widget.showLabels)
            Positioned.fill(
              child: CustomPaint(
                painter: _CandlestickAxisPainter(
                  klines: widget.klines,
                  rightPadding: 80.0,
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class _CandlestickPainter extends CustomPainter {
  final List<List<dynamic>> klines;
  final Color upColor;
  final Color downColor;
  final bool showGrid;
  final bool showLabels;
  final String timeframe; // Добавляем параметр для таймфрейма
  final double? currentPrice; // Добавляем параметр для текущей цены
  final double rightPadding; // Добавляем отступ справа
  final double scale;
  final Offset offset;
  final List<String> timestamps; // Добавляем timestamps

  _CandlestickPainter({
    required this.klines,
    required this.upColor,
    required this.downColor,
    required this.showGrid,
    required this.showLabels,
    required this.timeframe, // Требуем параметр таймфрейма
    this.currentPrice, // Текущая цена (опционально)
    this.rightPadding = 80.0, // Отступ справа по умолчанию
    this.scale = 1.0,
    this.offset = Offset.zero,
    this.timestamps = const [], // Добавляем timestamps
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (klines.isEmpty) return;

    final double width = size.width - rightPadding; // Учитываем отступ справа
    final double height = size.height;

    // Извлекаем данные из klines
    final List<double> opens = [];
    final List<double> highs = [];
    final List<double> lows = [];
    final List<double> closes = [];
    final List<String> timestamps = [];

    for (var kline in klines) {
      try {
        opens.add(double.parse(kline[1].toString()));
        highs.add(double.parse(kline[2].toString()));
        lows.add(double.parse(kline[3].toString()));
        closes.add(double.parse(kline[4].toString()));

        // Преобразуем timestamp в дату
        final timestamp = DateTime.fromMillisecondsSinceEpoch(kline[0] as int);
        // Форматируем метку времени в зависимости от таймфрейма
        timestamps.add(formatTimestamp(timestamp, timeframe));
      } catch (e) {
        // Пропускаем некорректные данные
        continue;
      }
    }

    if (opens.isEmpty) return;

    // Находим минимальное и максимальное значения
    final double minValue = lows.reduce((a, b) => a < b ? a : b);
    final double maxValue = highs.reduce((a, b) => a > b ? a : b);

    // Добавляем отступ к минимальному и максимальному значениям
    final padding = (maxValue - minValue) * 0.1;
    final adjustedMinValue = minValue - padding;
    final adjustedMaxValue = maxValue + padding;

    // Убеждаемся, что есть диапазон для работы
    final double valueRange = adjustedMaxValue - adjustedMinValue > 0.000001
        ? adjustedMaxValue - adjustedMinValue
        : 1.0;

    // Рассчитываем масштабирующие факторы
    final double candleWidth = width / klines.length * 0.8;
    final double spacing = width / klines.length * 0.2;
    final double yScale = height / valueRange;

    // Рисуем сетку, если нужно
    if (showGrid) {
      _drawGrid(canvas, size, adjustedMinValue, adjustedMaxValue);
    }

    // Рисуем свечи
    for (int i = 0; i < opens.length; i++) {
      final double open = opens[i];
      final double high = highs[i];
      final double low = lows[i];
      final double close = closes[i];

      final bool isUp = close >= open;
      final Color color = isUp ? upColor : downColor;

      final double x = i * (candleWidth + spacing) + spacing / 2;
      final double candleTop = height - (Math.max(open, close) - adjustedMinValue) * yScale;
      final double candleBottom = height - (Math.min(open, close) - adjustedMinValue) * yScale;
      final double candleHeight = candleBottom - candleTop;

      final double highY = height - (high - adjustedMinValue) * yScale;
      final double lowY = height - (low - adjustedMinValue) * yScale;

      // Рисуем фитиль (вертикальную линию)
      final Paint wickPaint = Paint()
        ..color = color
        ..strokeWidth = 1.0
        ..style = PaintingStyle.stroke;

      canvas.drawLine(
        Offset(x + candleWidth / 2, highY),
        Offset(x + candleWidth / 2, lowY),
        wickPaint,
      );

      // Рисуем тело свечи
      final Paint candlePaint = Paint()
        ..color = color
        ..style = isUp ? PaintingStyle.stroke : PaintingStyle.fill;

      if (isUp) {
        candlePaint.strokeWidth = 1.0;
      }

      canvas.drawRect(
        Rect.fromLTWH(x, candleTop, candleWidth, candleHeight),
        candlePaint,
      );
    }

    // Рисуем метки, если нужно
    if (showLabels) {
      _drawLabels(canvas, size, adjustedMinValue, adjustedMaxValue, timestamps);
    }

    // Рисуем оси
    _drawPriceAxis(canvas, size, adjustedMinValue, adjustedMaxValue);
    _drawTimeAxis(canvas, size);

    // Рисуем линию текущей цены
    if (currentPrice != null) {
      _drawCurrentPriceLine(canvas, size, adjustedMinValue, adjustedMaxValue);
    }
  }

  void _drawGrid(Canvas canvas, Size size, double minValue, double maxValue) {
    final gridPaint = Paint()
      ..color = Colors.grey.withAlpha(51)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Рисуем горизонтальные линии сетки
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
    }

    // Рисуем вертикальные линии сетки
    for (int i = 0; i <= 6; i++) {
      final x = size.width * i / 6;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), gridPaint);
    }
  }

  void _drawLabels(Canvas canvas, Size size, double minValue, double maxValue, List<String> timestamps) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Рисуем метки цен
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      final price = maxValue - (i / 4) * (maxValue - minValue);

      textPainter.text = TextSpan(
        text: _formatPrice(price),
        style: const TextStyle(color: Colors.white, fontSize: 10),
      );

      textPainter.layout();
      textPainter.paint(canvas, Offset(size.width - textPainter.width - 4, y - textPainter.height / 2));
    }

    // Отображаем текущую цену, если она предоставлена
    if (currentPrice != null) {
      // Проверяем, находится ли текущая цена в пределах видимого диапазона
      if (currentPrice! >= minValue && currentPrice! <= maxValue) {
        // Вычисляем позицию Y для текущей цены
        final y = size.height - ((currentPrice! - minValue) / (maxValue - minValue) * size.height);

        // Определяем цвет линии (зеленый, если последняя свеча растущая, иначе красный)
        final isLastCandleUp = klines.isNotEmpty &&
            double.parse(klines.last[4].toString()) >= double.parse(klines.last[1].toString());
        final lineColor = isLastCandleUp ? upColor : downColor;

        // Рисуем горизонтальную линию текущей цены
        final linePaint = Paint()
          ..color = lineColor
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke;

        // Рисуем пунктирную линию
        final dashWidth = 5.0;
        final dashSpace = 3.0;
        double startX = 0;

        while (startX < size.width) {
          canvas.drawLine(
            Offset(startX, y),
            Offset(startX + dashWidth, y),
            linePaint,
          );
          startX += dashWidth + dashSpace;
        }

        // Рисуем метку текущей цены
        final bgPaint = Paint()
          ..color = Colors.black.withOpacity(0.7)
          ..style = PaintingStyle.fill;

        textPainter.text = TextSpan(
          text: _formatPrice(currentPrice!),
          style: TextStyle(color: lineColor, fontSize: 10, fontWeight: FontWeight.bold),
        );

        textPainter.layout();

        // Рисуем фон для текста
        final textBgRect = Rect.fromLTWH(
          size.width - textPainter.width - 8,
          y - textPainter.height / 2 - 2,
          textPainter.width + 8,
          textPainter.height + 4,
        );

        canvas.drawRect(textBgRect, bgPaint);

        // Рисуем текст
        textPainter.paint(canvas, Offset(size.width - textPainter.width - 4, y - textPainter.height / 2));
      }
    }

    // Рисуем метки дат
    if (timestamps.isNotEmpty) {
      // Определяем количество меток в зависимости от таймфрейма
      int labelCount;
      switch (timeframe) {
        case '30М': // 30 минут
          labelCount = 6; // Показываем 6 меток для 30-минутного графика
          break;
        case '1Ч': // 1 час
          labelCount = 6; // Показываем 6 меток для часового графика
          break;
        case '4Ч': // 4 часа
          labelCount = 7; // Показываем 7 меток для 4-часового графика
          break;
        case '1Д': // 1 день
          labelCount = 8; // Показываем 8 меток для дневного графика
          break;
        case '1Н': // 1 неделя
          labelCount = 6; // Показываем 6 меток для недельного графика
          break;
        default:
          labelCount = 6;
      }

      // Выбираем равномерно распределенные метки
      final visibleTimestamps = timestamps.length > labelCount ?
          _selectEvenlyDistributedLabels(timestamps, labelCount) :
          timestamps;

      // Вычисляем индексы для выбранных меток
      final List<int> selectedIndices = [];
      if (timestamps.length > labelCount) {
        final step = (timestamps.length - 1) / (labelCount - 1);
        for (int i = 0; i < labelCount; i++) {
          final index = (i * step).round();
          if (index < timestamps.length) {
            selectedIndices.add(index);
          }
        }
      } else {
        for (int i = 0; i < timestamps.length; i++) {
          selectedIndices.add(i);
        }
      }

      // Рисуем метки времени под соответствующими свечами
      for (int i = 0; i < selectedIndices.length; i++) {
        final index = selectedIndices[i];
        // Вычисляем позицию X для метки времени
        final x = size.width * index / (timestamps.length - 1);

        textPainter.text = TextSpan(
          text: visibleTimestamps[i],
          style: const TextStyle(color: Colors.white, fontSize: 10),
        );

        textPainter.layout();
        textPainter.paint(canvas, Offset(x - textPainter.width / 2, size.height + 4));
      }
    }

  }

  void _drawPriceAxis(Canvas canvas, Size size, double minValue, double maxValue) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Рисуем метки цен справа
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      final price = maxValue - (i / 4) * (maxValue - minValue);

      textPainter.text = TextSpan(
        text: _formatPrice(price),
        style: const TextStyle(color: Colors.white, fontSize: 10),
      );

      textPainter.layout();

      // Рисуем фон для текста
      final rect = Rect.fromLTWH(
        size.width - rightPadding + 2,
        y - textPainter.height / 2 - 2,
        rightPadding - 4,
        textPainter.height + 4,
      );

      canvas.drawRect(
        rect,
        Paint()..color = Colors.black54,
      );

      textPainter.paint(canvas, Offset(size.width - rightPadding + 4, y - textPainter.height / 2));
    }
  }

  void _drawTimeAxis(Canvas canvas, Size size) {
    if (timestamps.isEmpty) return;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    // Определяем количество меток времени
    final labelCount = 6;
    final step = (timestamps.length - 1) / (labelCount - 1);

    for (int i = 0; i < labelCount; i++) {
      final index = (i * step).round();
      if (index >= timestamps.length) continue;

      final x = (size.width - rightPadding) * i / (labelCount - 1);

      textPainter.text = TextSpan(
        text: timestamps[index],
        style: const TextStyle(color: Colors.white, fontSize: 9),
      );

      textPainter.layout();
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, size.height + 4));
    }
  }

  void _drawCurrentPriceLine(Canvas canvas, Size size, double minValue, double maxValue) {
    if (currentPrice == null) return;

    final double priceRange = maxValue - minValue;
    final double y = size.height - (currentPrice! - minValue) / priceRange * size.height;

    // Рисуем горизонтальную линию
    final Paint linePaint = Paint()
      ..color = Colors.orange
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    canvas.drawLine(
      Offset(0, y),
      Offset(size.width - rightPadding, y),
      linePaint,
    );

    // Рисуем метку цены
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    textPainter.text = TextSpan(
      text: '\$${_formatPrice(currentPrice!)}',
      style: const TextStyle(
        color: Colors.orange,
        fontSize: 11,
        fontWeight: FontWeight.bold,
      ),
    );

    textPainter.layout();

    // Рисуем фон для метки
    final rect = Rect.fromLTWH(
      size.width - rightPadding + 2,
      y - textPainter.height / 2 - 2,
      rightPadding - 4,
      textPainter.height + 4,
    );

    canvas.drawRect(
      rect,
      Paint()..color = Colors.orange.withOpacity(0.8),
    );

    textPainter.paint(canvas, Offset(size.width - rightPadding + 4, y - textPainter.height / 2));
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  // Метод для форматирования меток времени в зависимости от таймфрейма
  String formatTimestamp(DateTime date, String timeframe) {
    switch (timeframe) {
      case '30М': // 30 минут
        // Для 30-минутного графика показываем часы и минуты
        return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
      case '1Ч': // 1 час
        // Для часового графика показываем день и час
        return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')} ${date.hour}:00';
      case '4Ч': // 4 часа
        // Для 4-часового графика показываем день, месяц и час
        return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')} ${date.hour}:00';
      case '1Д': // 1 день
        // Для дневного графика показываем день и месяц
        return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}';
      case '1Н': // 1 неделя
        // Для недельного графика показываем месяц и год
        final months = ['Янв', 'Фев', 'Мар', 'Апр', 'Май', 'Июн', 'Июл', 'Авг', 'Сен', 'Окт', 'Ноя', 'Дек'];
        return '${months[date.month - 1]} ${date.year}';
      default:
        return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}';
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;

  // Вспомогательный метод для выбора равномерно распределенных меток
  List<String> _selectEvenlyDistributedLabels(List<String> allLabels, int count) {
    if (allLabels.length <= count) return allLabels;

    List<String> result = [];
    // Всегда добавляем первую метку
    result.add(allLabels.first);

    // Вычисляем шаг для равномерного распределения
    final step = (allLabels.length - 1) / (count - 1);

    // Добавляем промежуточные метки
    for (int i = 1; i < count - 1; i++) {
      final index = (i * step).round();
      if (index < allLabels.length) {
        result.add(allLabels[index]);
      }
    }

    // Всегда добавляем последнюю метку
    result.add(allLabels.last);

    return result;
  }
}

// Класс для рисования фиксированных осей свечного графика
class _CandlestickAxisPainter extends CustomPainter {
  final List<List<dynamic>> klines;
  final double rightPadding;

  _CandlestickAxisPainter({
    required this.klines,
    required this.rightPadding,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (klines.isEmpty) return;

    // Извлекаем данные из klines для расчета диапазона цен
    final List<double> highs = [];
    final List<double> lows = [];

    for (var kline in klines) {
      try {
        highs.add(double.parse(kline[2].toString()));
        lows.add(double.parse(kline[3].toString()));
      } catch (e) {
        continue;
      }
    }

    if (highs.isEmpty || lows.isEmpty) return;

    final double maxValue = highs.reduce((a, b) => a > b ? a : b);
    final double minValue = lows.reduce((a, b) => a < b ? a : b);
    final double valueRange = maxValue - minValue;

    if (valueRange == 0) return;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Рисуем метки цен справа (фиксированные)
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      final price = maxValue - (i / 4) * valueRange;

      textPainter.text = TextSpan(
        text: _formatPrice(price),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          backgroundColor: Colors.black54,
        ),
      );

      textPainter.layout();

      // Рисуем фон для текста
      final rect = Rect.fromLTWH(
        size.width - textPainter.width - 8,
        y - textPainter.height / 2 - 2,
        textPainter.width + 4,
        textPainter.height + 4,
      );

      canvas.drawRect(
        rect,
        Paint()..color = Colors.black54,
      );

      textPainter.paint(
        canvas,
        Offset(size.width - textPainter.width - 6, y - textPainter.height / 2)
      );
    }
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  @override
  bool shouldRepaint(_CandlestickAxisPainter oldDelegate) {
    return oldDelegate.klines != klines;
  }
}

// Публичный класс для использования в других виджетах
class CandlestickPainter extends _CandlestickPainter {
  CandlestickPainter({
    required List<List<dynamic>> klines,
    required List<String> timestamps,
    required String timeframe,
    double? currentPrice,
    bool showGrid = true,
    bool showLabels = true,
    Color upColor = Colors.green,
    Color downColor = Colors.red,
    double rightPadding = 80.0,
    double scale = 1.0,
    Offset offset = Offset.zero,
  }) : super(
          klines: klines,
          upColor: upColor,
          downColor: downColor,
          showGrid: showGrid,
          showLabels: showLabels,
          timeframe: timeframe,
          currentPrice: currentPrice,
          rightPadding: rightPadding,
          scale: scale,
          offset: offset,
          timestamps: timestamps,
        );
}

// Вспомогательный класс для математических операций
class Math {
  static double max(double a, double b) => a > b ? a : b;
  static double min(double a, double b) => a < b ? a : b;
}
