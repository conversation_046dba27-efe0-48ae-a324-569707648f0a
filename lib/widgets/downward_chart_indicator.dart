import 'package:flutter/material.dart';

/// Индикатор графика падения, точно как на изображении
class DownwardChartIndicator extends StatelessWidget {
  final double width;
  final double height;
  
  const DownwardChartIndicator({
    Key? key,
    this.width = 30.0,
    this.height = 30.0,
  }) : super(key: key);
  
  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.black,
        borderRadius: BorderRadius.circular(4.0),
        border: Border.all(
          color: const Color(0xFF3A0000), // Темно-красная граница
          width: 1.0,
        ),
      ),
      child: CustomPaint(
        painter: DownwardChartPainter(),
        size: Size(width, height),
      ),
    );
  }
}

/// Кастомный painter для рисования графика падения, точно как на изображении
class DownwardChartPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = const Color(0xFFFF4D4F) // Красный цвет, как на изображении
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round;
    
    final path = Path();
    final width = size.width;
    final height = size.height;
    
    // Точки для графика падения, точно как на изображении
    final points = [
      Offset(width * 0.15, height * 0.3),      // Начальная точка
      Offset(width * 0.3, height * 0.3),       // Горизонтальная линия
      Offset(width * 0.5, height * 0.5),       // Начало падения
      Offset(width * 0.7, height * 0.8),       // Продолжение падения
      Offset(width * 0.85, height * 0.7),      // Небольшой отскок в конце
    ];
    
    // Рисуем линию
    path.moveTo(points[0].dx, points[0].dy);
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }
    
    canvas.drawPath(path, paint);
  }
  
  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return false;
  }
}
