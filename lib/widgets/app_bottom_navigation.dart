import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:ui';
import '../utils/device_type.dart';

class AppBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final BoxDecoration? backgroundDecoration;

  const AppBottomNavigation({
    super.key,
    required this.currentIndex,
    required this.onTap,
    this.backgroundDecoration,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop = DeviceUtils.isDesktop(context);
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Container(
      decoration: backgroundDecoration ?? BoxDecoration(
        color: isDarkMode
            ? Colors.black.withAlpha(204) // 0.8 opacity
            : Colors.white.withAlpha(204), // 0.8 opacity
        border: Border(
          top: BorderSide(
            color: isDarkMode
                ? Colors.grey.shade800.withAlpha(77) // 0.3 opacity
                : Colors.grey.shade300,
            width: 0.5,
          ),
        ),
      ),
      child: ClipRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: isDesktop
              ? _buildDesktopNavigation(context)
              : _buildMobileNavigation(context),
        ),
      ),
    );
  }

  Widget _buildMobileNavigation(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final selectedColor = isDarkMode
        ? CupertinoColors.activeBlue
        : CupertinoColors.systemBlue;
    final unselectedColor = isDarkMode
        ? Colors.grey.shade500
        : Colors.grey.shade600;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0, top: 8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          _buildNavItem(
            context,
            0,
            CupertinoIcons.news,
            'News',
            selectedColor,
            unselectedColor
          ),
          _buildNavItem(
            context,
            1,
            CupertinoIcons.graph_circle,
            'Charts',
            selectedColor,
            unselectedColor
          ),
          _buildSinusoidNavItem(
            context,
            2,
            selectedColor,
            unselectedColor
          ),
          _buildNavItem(
            context,
            3,
            CupertinoIcons.book,
            'Learn',
            selectedColor,
            unselectedColor
          ),
          _buildNavItem(
            context,
            4,
            CupertinoIcons.person,
            'Profile',
            selectedColor,
            unselectedColor
          ),
        ],
      ),
    );
  }

  Widget _buildNavItem(
    BuildContext context,
    int index,
    IconData icon,
    String label,
    Color selectedColor,
    Color unselectedColor
  ) {
    final isSelected = currentIndex == index;

    return GestureDetector(
      onTap: () => onTap(index),
      behavior: HitTestBehavior.opaque,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 60,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? selectedColor : unselectedColor,
              size: 26,
            ),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? selectedColor : unselectedColor,
                fontSize: 10,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSinusoidNavItem(
    BuildContext context,
    int index,
    Color selectedColor,
    Color unselectedColor
  ) {
    final isSelected = currentIndex == index;

    return GestureDetector(
      onTap: () => onTap(index),
      behavior: HitTestBehavior.opaque,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: 60,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              'logo/bottom_navigation_bar/Sinusoid.png',
              width: 26,
              height: 26,
              color: isSelected ? selectedColor : unselectedColor,
            ),
            const SizedBox(height: 4),
            Text(
              'Sinusoid',
              style: TextStyle(
                color: isSelected ? selectedColor : unselectedColor,
                fontSize: 10,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopNavigation(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final selectedColor = isDarkMode
        ? CupertinoColors.activeBlue
        : CupertinoColors.systemBlue;
    final unselectedColor = isDarkMode
        ? Colors.grey.shade500
        : Colors.grey.shade600;
    final backgroundColor = isDarkMode
        ? Colors.black.withAlpha(204) // 0.8 opacity
        : Colors.white.withAlpha(204); // 0.8 opacity

    return Container(
      height: 60,
      padding: const EdgeInsets.symmetric(horizontal: 20),
      color: backgroundColor,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Logo or app name
          Text(
            'Finance AI',
            style: TextStyle(
              color: isDarkMode ? Colors.white : Colors.black,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),

          // Navigation items
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDesktopNavItem(context, 0, CupertinoIcons.news, 'News', selectedColor, unselectedColor),
              _buildDesktopNavItem(context, 1, CupertinoIcons.graph_circle, 'Charts', selectedColor, unselectedColor),
              _buildDesktopSinusoidNavItem(context, 2, selectedColor, unselectedColor),
              _buildDesktopNavItem(context, 3, CupertinoIcons.book, 'Learn', selectedColor, unselectedColor),
              _buildDesktopNavItem(context, 4, CupertinoIcons.person, 'Profile', selectedColor, unselectedColor),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopSinusoidNavItem(BuildContext context, int index, Color selectedColor, Color unselectedColor) {
    final isSelected = currentIndex == index;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () => onTap(index),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? (isDarkMode
                  ? selectedColor.withAlpha(51) // 0.2 opacity
                  : selectedColor.withAlpha(26)) // 0.1 opacity
              : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Image.asset(
              'logo/bottom_navigation_bar/Sinusoid.png',
              width: 20,
              height: 20,
              color: isSelected ? selectedColor : unselectedColor,
            ),
            const SizedBox(width: 8),
            Text(
              'Sinusoid',
              style: TextStyle(
                color: isSelected ? selectedColor : unselectedColor,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDesktopNavItem(BuildContext context, int index, IconData icon, String label, Color selectedColor, Color unselectedColor) {
    final isSelected = currentIndex == index;
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return GestureDetector(
      onTap: () => onTap(index),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        margin: const EdgeInsets.symmetric(horizontal: 4),
        decoration: BoxDecoration(
          color: isSelected
              ? (isDarkMode
                  ? selectedColor.withAlpha(51) // 0.2 opacity
                  : selectedColor.withAlpha(26)) // 0.1 opacity
              : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              color: isSelected ? selectedColor : unselectedColor,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: isSelected ? selectedColor : unselectedColor,
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
