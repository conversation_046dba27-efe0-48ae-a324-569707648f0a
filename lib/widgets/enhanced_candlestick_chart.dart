import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/gestures.dart';
import '../services/binance_service.dart';
import 'candlestick_chart.dart';

/// Улучшенный график свечей с поддержкой загрузки исторических данных
/// и бесконечной прокрутки как в TradingView
class EnhancedCandlestickChart extends StatefulWidget {
  final List<List<dynamic>> initialKlines;
  final List<String> timestamps;
  final String timeframe;
  final double? currentPrice;
  final String symbol;
  final Color upColor;
  final Color downColor;
  final bool showGrid;
  final bool showLabels;
  final double? height;
  final double? width;

  const EnhancedCandlestickChart({
    Key? key,
    required this.initialKlines,
    required this.timestamps,
    required this.timeframe,
    required this.symbol,
    this.currentPrice,
    this.upColor = Colors.green,
    this.downColor = Colors.red,
    this.showGrid = true,
    this.showLabels = true,
    this.height,
    this.width,
  }) : super(key: key);

  @override
  State<EnhancedCandlestickChart> createState() => _EnhancedCandlestickChartState();
}

class _EnhancedCandlestickChartState extends State<EnhancedCandlestickChart> {
  late List<List<dynamic>> _allKlines;
  late List<String> _allTimestamps;
  double _scale = 1.0;
  Offset _offset = Offset.zero;
  Offset? _lastPanPosition;
  
  // Параметры для загрузки данных
  final BinanceService _binanceService = BinanceService();
  bool _isLoadingHistorical = false;
  bool _hasMoreData = true;
  
  // Ограничения для зума и панорамирования
  static const double _minScale = 0.1;
  static const double _maxScale = 10.0;
  
  // Параметры для определения когда загружать больше данных
  static const double _loadMoreThreshold = 0.8; // 80% от начала
  
  @override
  void initState() {
    super.initState();
    _allKlines = List.from(widget.initialKlines);
    _allTimestamps = List.from(widget.timestamps);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height ?? 350,
      width: widget.width,
      margin: const EdgeInsets.only(bottom: 20),
      child: Listener(
        onPointerSignal: (pointerSignal) {
          if (pointerSignal is PointerScrollEvent) {
            setState(() {
              // Зум с помощью колесика мыши
              final delta = pointerSignal.scrollDelta.dy;
              final zoomFactor = delta > 0 ? 0.9 : 1.1;
              _scale = (_scale * zoomFactor).clamp(_minScale, _maxScale);
              print('🔍 Зум изменен: ${_scale.toStringAsFixed(2)}x');
            });
          }
        },
        child: GestureDetector(
          onScaleStart: (details) {
            _lastPanPosition = details.focalPoint;
          },
          onScaleUpdate: (details) {
            setState(() {
              // Обновляем масштаб
              if (details.scale != 1.0) {
                _scale = (_scale * details.scale).clamp(_minScale, _maxScale);
              }

              // Обновляем смещение для панорамирования
              if (_lastPanPosition != null) {
                final delta = details.focalPoint - _lastPanPosition!;
                _offset = Offset(
                  _offset.dx + delta.dx,
                  (_offset.dy + delta.dy).clamp(-200.0, 200.0),
                );
                _lastPanPosition = details.focalPoint;
                
                // Проверяем, нужно ли загрузить больше данных
                _checkForMoreData();
              }
            });
          },
          onScaleEnd: (details) {
            _lastPanPosition = null;
          },
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withOpacity(0.8),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.withOpacity(0.3)),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Stack(
                children: [
                  // Основной график
                  Positioned.fill(
                    child: Padding(
                      padding: const EdgeInsets.only(bottom: 30, right: 100),
                      child: CustomPaint(
                        painter: EnhancedCandlestickPainter(
                          klines: _allKlines,
                          timestamps: _allTimestamps,
                          timeframe: widget.timeframe,
                          currentPrice: widget.currentPrice,
                          showGrid: widget.showGrid,
                          showLabels: false,
                          upColor: widget.upColor,
                          downColor: widget.downColor,
                          rightPadding: 100,
                          scale: _scale,
                          offset: _offset,
                        ),
                      ),
                    ),
                  ),
                  
                  // Фиксированные оси поверх графика
                  Positioned.fill(
                    child: CustomPaint(
                      painter: _AxisPainter(
                        klines: _allKlines,
                        timestamps: _allTimestamps,
                        timeframe: widget.timeframe,
                        rightPadding: 100,
                        currentPrice: widget.currentPrice,
                      ),
                    ),
                  ),
                  
                  // Индикатор зума
                  if (_scale != 1.0)
                    Positioned(
                      top: 10,
                      left: 10,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.black54,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          'Zoom: ${_scale.toStringAsFixed(1)}x',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ),

                  // Индикатор загрузки исторических данных
                  if (_isLoadingHistorical)
                    Positioned(
                      top: 10,
                      right: 10,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.blue.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: const [
                            SizedBox(
                              width: 12,
                              height: 12,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            ),
                            SizedBox(width: 4),
                            Text(
                              'Загрузка...',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 10,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Кнопки управления зумом
                  Positioned(
                    bottom: 40,
                    right: 10,
                    child: Column(
                      children: [
                        _buildZoomButton(Icons.add, () {
                          setState(() {
                            _scale = (_scale * 1.2).clamp(_minScale, _maxScale);
                          });
                        }),
                        const SizedBox(height: 4),
                        _buildZoomButton(Icons.remove, () {
                          setState(() {
                            _scale = (_scale / 1.2).clamp(_minScale, _maxScale);
                          });
                        }),
                        const SizedBox(height: 4),
                        _buildZoomButton(Icons.refresh, () {
                          setState(() {
                            _scale = 1.0;
                            _offset = Offset.zero;
                          });
                        }),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildZoomButton(IconData icon, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        width: 28,
        height: 28,
        decoration: BoxDecoration(
          color: Colors.black54,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(color: Colors.white24, width: 1),
        ),
        child: Icon(icon, size: 16, color: Colors.white),
      ),
    );
  }

  /// Проверяет, нужно ли загрузить больше исторических данных
  void _checkForMoreData() {
    if (_isLoadingHistorical || !_hasMoreData) return;
    
    // Если пользователь прокрутил к началу графика, загружаем больше данных
    final normalizedOffset = _offset.dx / _scale;
    if (normalizedOffset > 0) {
      _loadMoreHistoricalData();
    }
  }

  /// Загружает больше исторических данных
  Future<void> _loadMoreHistoricalData() async {
    if (_isLoadingHistorical || !_hasMoreData || _allKlines.isEmpty) return;
    
    setState(() {
      _isLoadingHistorical = true;
    });

    try {
      // Получаем время первой свечи для загрузки предыдущих данных
      final firstKline = _allKlines.first;
      final firstTimestamp = firstKline[0] as int;
      final endTime = DateTime.fromMillisecondsSinceEpoch(firstTimestamp);
      
      // Определяем интервал для API
      String interval;
      switch (widget.timeframe) {
        case '30М':
          interval = '30m';
          break;
        case '1Ч':
          interval = '1h';
          break;
        case '4Ч':
          interval = '4h';
          break;
        case '1Д':
          interval = '1d';
          break;
        case '1Н':
          interval = '1w';
          break;
        default:
          interval = '1h';
      }

      // Загружаем предыдущие данные
      final historicalKlines = await _binanceService.getKlines(
        symbol: widget.symbol,
        interval: interval,
        limit: 100,
      );

      if (historicalKlines.isNotEmpty && mounted) {
        // Фильтруем данные, чтобы избежать дублирования
        final filteredKlines = historicalKlines.where((kline) {
          final timestamp = kline[0] as int;
          return timestamp < firstTimestamp;
        }).toList();

        if (filteredKlines.isNotEmpty) {
          setState(() {
            // Добавляем новые данные в начало списка
            _allKlines.insertAll(0, filteredKlines);
            
            // Генерируем новые временные метки
            final newTimestamps = _generateTimestamps(filteredKlines);
            _allTimestamps.insertAll(0, newTimestamps);
            
            // Корректируем смещение, чтобы график не "прыгал"
            final addedDataWidth = filteredKlines.length * (widget.width ?? 400) / _allKlines.length;
            _offset = Offset(_offset.dx - addedDataWidth, _offset.dy);
          });
        } else {
          _hasMoreData = false;
        }
      }
    } catch (e) {
      print('Ошибка загрузки исторических данных: $e');
      _hasMoreData = false;
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingHistorical = false;
        });
      }
    }
  }

  /// Генерирует временные метки для свечей
  List<String> _generateTimestamps(List<List<dynamic>> klines) {
    return klines.map((kline) {
      final timestamp = kline[0] as int;
      final date = DateTime.fromMillisecondsSinceEpoch(timestamp);
      return _formatTimestamp(date, widget.timeframe);
    }).toList();
  }

  /// Форматирует временную метку в зависимости от таймфрейма
  String _formatTimestamp(DateTime date, String timeframe) {
    switch (timeframe) {
      case '30М':
        return '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
      case '1Ч':
        return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')} ${date.hour}:00';
      case '4Ч':
        return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')} ${date.hour}:00';
      case '1Д':
        return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}';
      case '1Н':
        final months = ['Янв', 'Фев', 'Мар', 'Апр', 'Май', 'Июн', 'Июл', 'Авг', 'Сен', 'Окт', 'Ноя', 'Дек'];
        return '${months[date.month - 1]} ${date.year}';
      default:
        return '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}';
    }
  }
}

// Используем существующий CandlestickPainter как EnhancedCandlestickPainter
typedef EnhancedCandlestickPainter = CandlestickPainter;

// Используем существующий _AxisPainter из interactive_candlestick_chart.dart
class _AxisPainter extends CustomPainter {
  final List<List<dynamic>> klines;
  final List<String> timestamps;
  final String timeframe;
  final double rightPadding;
  final double? currentPrice;

  _AxisPainter({
    required this.klines,
    required this.timestamps,
    required this.timeframe,
    this.rightPadding = 100.0,
    this.currentPrice,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (klines.isEmpty) return;

    // Извлекаем данные для осей
    final highs = klines.map((k) => double.parse(k[2].toString())).toList();
    final lows = klines.map((k) => double.parse(k[3].toString())).toList();

    if (highs.isEmpty || lows.isEmpty) return;

    final double minValue = lows.reduce((a, b) => a < b ? a : b);
    final double maxValue = highs.reduce((a, b) => a > b ? a : b);
    final padding = (maxValue - minValue) * 0.1;
    final adjustedMinValue = minValue - padding;
    final adjustedMaxValue = maxValue + padding;

    _drawPriceAxis(canvas, size, adjustedMinValue, adjustedMaxValue);
    _drawTimeAxis(canvas, size);

    // Рисуем метку текущей цены
    if (currentPrice != null) {
      _drawCurrentPriceLabel(canvas, size, adjustedMinValue, adjustedMaxValue);
    }
  }

  void _drawPriceAxis(Canvas canvas, Size size, double minValue, double maxValue) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Рисуем метки цен справа
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      final price = maxValue - (i / 4) * (maxValue - minValue);

      textPainter.text = TextSpan(
        text: _formatPrice(price),
        style: const TextStyle(color: Colors.white, fontSize: 10),
      );

      textPainter.layout();

      // Рисуем фон для текста
      final rect = Rect.fromLTWH(
        size.width - rightPadding + 2,
        y - textPainter.height / 2 - 2,
        rightPadding - 4,
        textPainter.height + 4,
      );

      canvas.drawRect(
        rect,
        Paint()..color = Colors.black54,
      );

      textPainter.paint(canvas, Offset(size.width - rightPadding + 4, y - textPainter.height / 2));
    }
  }

  void _drawTimeAxis(Canvas canvas, Size size) {
    if (timestamps.isEmpty) return;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    // Определяем количество меток времени
    final labelCount = 6;
    final step = (timestamps.length - 1) / (labelCount - 1);

    for (int i = 0; i < labelCount; i++) {
      final index = (i * step).round();
      if (index >= timestamps.length) continue;

      final x = (size.width - rightPadding) * i / (labelCount - 1);

      textPainter.text = TextSpan(
        text: timestamps[index],
        style: const TextStyle(color: Colors.white, fontSize: 9),
      );

      textPainter.layout();
      textPainter.paint(canvas, Offset(x - textPainter.width / 2, size.height + 4));
    }
  }

  void _drawCurrentPriceLabel(Canvas canvas, Size size, double minValue, double maxValue) {
    if (currentPrice == null) return;

    final y = size.height - (currentPrice! - minValue) / (maxValue - minValue) * size.height;
    
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    textPainter.text = TextSpan(
      text: _formatPrice(currentPrice!),
      style: const TextStyle(color: Colors.orange, fontSize: 10, fontWeight: FontWeight.bold),
    );

    textPainter.layout();

    // Рисуем фон для текста
    final rect = Rect.fromLTWH(
      size.width - rightPadding + 2,
      y - textPainter.height / 2 - 2,
      rightPadding - 4,
      textPainter.height + 4,
    );

    canvas.drawRect(rect, Paint()..color = Colors.orange.withOpacity(0.2));
    textPainter.paint(canvas, Offset(size.width - rightPadding + 4, y - textPainter.height / 2));
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
