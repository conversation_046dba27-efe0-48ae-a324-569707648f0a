import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'dart:math' as math;

class InteractiveChart extends StatefulWidget {
  final List<double> prices;
  final List<DateTime>? dates;
  final Color color;
  final double? maxPrice;
  final double? minPrice;
  final bool showGrid;
  final bool showLabels;
  final double rightPadding;

  const InteractiveChart({
    Key? key,
    required this.prices,
    this.dates,
    required this.color,
    this.maxPrice,
    this.minPrice,
    this.showGrid = true,
    this.showLabels = true,
    this.rightPadding = 50.0, // Отступ справа для последней свечи
  }) : super(key: key);

  @override
  State<InteractiveChart> createState() => _InteractiveChartState();
}

class _InteractiveChartState extends State<InteractiveChart> {
  double _scale = 1.0;
  double _previousScale = 1.0;
  Offset _offset = Offset.zero;
  Offset _previousOffset = Offset.zero;

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerSignal: (pointerSignal) {
        if (pointerSignal is PointerScrollEvent) {
          setState(() {
            // Зум с помощью колесика мыши
            final delta = pointerSignal.scrollDelta.dy;
            final zoomFactor = delta > 0 ? 0.9 : 1.1;
            _scale = (_scale * zoomFactor).clamp(0.5, 5.0);
          });
        }
      },
      child: GestureDetector(
        onScaleStart: (details) {
          _previousScale = _scale;
          _previousOffset = _offset;
        },
        onScaleUpdate: (details) {
          setState(() {
            // Обновляем масштаб
            _scale = (_previousScale * details.scale).clamp(0.5, 5.0);

            // Обновляем смещение для drag
            _offset = _previousOffset + details.focalPointDelta;

            // Ограничиваем смещение, чтобы график не уходил слишком далеко
            final maxOffset = 300.0;
            _offset = Offset(
              _offset.dx.clamp(-maxOffset, maxOffset),
              _offset.dy.clamp(-maxOffset, maxOffset),
            );
          });
        },

        child: Transform(
          transform: Matrix4.identity()
            ..translate(_offset.dx, _offset.dy)
            ..scale(_scale),
          child: CustomPaint(
            size: Size.infinite,
            painter: InteractiveChartPainter(
              prices: widget.prices,
              dates: widget.dates,
              color: widget.color,
              maxPrice: widget.maxPrice,
              minPrice: widget.minPrice,
              showGrid: widget.showGrid,
              showLabels: widget.showLabels,
              rightPadding: widget.rightPadding,
            ),
          ),
        ),
      ),
    );
  }
}

class InteractiveChartPainter extends CustomPainter {
  final List<double> prices;
  final List<DateTime>? dates;
  final Color color;
  final double? maxPrice;
  final double? minPrice;
  final bool showGrid;
  final bool showLabels;
  final double rightPadding;

  InteractiveChartPainter({
    required this.prices,
    this.dates,
    required this.color,
    this.maxPrice,
    this.minPrice,
    required this.showGrid,
    required this.showLabels,
    required this.rightPadding,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (prices.isEmpty) return;

    // Вычисляем эффективную ширину с учетом отступа справа
    final effectiveWidth = size.width - rightPadding;
    
    final calculatedMaxPrice = maxPrice ?? prices.reduce(math.max);
    final calculatedMinPrice = minPrice ?? prices.reduce(math.min);
    final priceRange = calculatedMaxPrice - calculatedMinPrice;
    
    if (priceRange == 0) return;

    // Рисуем сетку, если включена
    if (showGrid) {
      _drawGrid(canvas, size, calculatedMaxPrice, calculatedMinPrice, priceRange);
    }

    // Рисуем график
    _drawChart(canvas, size, effectiveWidth, calculatedMaxPrice, calculatedMinPrice, priceRange);

    // Рисуем метки, если включены
    if (showLabels) {
      _drawLabels(canvas, size, calculatedMaxPrice, calculatedMinPrice, priceRange);
    }
  }

  void _drawGrid(Canvas canvas, Size size, double maxPrice, double minPrice, double priceRange) {
    final gridPaint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Горизонтальные линии
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
    }

    // Вертикальные линии
    for (int i = 0; i <= 6; i++) {
      final x = size.width * i / 6;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), gridPaint);
    }
  }

  void _drawChart(Canvas canvas, Size size, double effectiveWidth, double maxPrice, double minPrice, double priceRange) {
    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final fillPaint = Paint()
      ..color = color.withOpacity(0.2)
      ..style = PaintingStyle.fill;

    final path = Path();
    final fillPath = Path();

    // Вычисляем шаг по X с учетом эффективной ширины
    final xStep = effectiveWidth / (prices.length - 1);

    // Начинаем с первой точки
    final firstPoint = Offset(
      0,
      size.height - ((prices[0] - minPrice) / priceRange * size.height),
    );
    path.moveTo(firstPoint.dx, firstPoint.dy);
    fillPath.moveTo(firstPoint.dx, size.height);
    fillPath.lineTo(firstPoint.dx, firstPoint.dy);

    // Добавляем остальные точки
    for (int i = 1; i < prices.length; i++) {
      final x = xStep * i;
      final y = size.height - ((prices[i] - minPrice) / priceRange * size.height);
      path.lineTo(x, y);
      fillPath.lineTo(x, y);
    }

    // Закрываем путь для заполнения
    fillPath.lineTo(effectiveWidth, size.height);
    fillPath.close();

    // Рисуем заполнение
    canvas.drawPath(fillPath, fillPaint);

    // Рисуем линию
    canvas.drawPath(path, paint);

    // Рисуем точки на линии для лучшей интерактивности
    final pointPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    for (int i = 0; i < prices.length; i++) {
      final x = xStep * i;
      final y = size.height - ((prices[i] - minPrice) / priceRange * size.height);
      canvas.drawCircle(Offset(x, y), 2.0, pointPaint);
    }
  }

  void _drawLabels(Canvas canvas, Size size, double maxPrice, double minPrice, double priceRange) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Рисуем метки цен справа
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      final price = maxPrice - (i / 4) * priceRange;
      
      textPainter.text = TextSpan(
        text: _formatPrice(price),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      );
      textPainter.layout();
      
      // Размещаем метки в области отступа справа
      textPainter.paint(
        canvas, 
        Offset(size.width - rightPadding + 8, y - textPainter.height / 2)
      );
    }

    // Рисуем метки времени внизу (если есть даты)
    if (dates != null && dates!.isNotEmpty) {
      final effectiveWidth = size.width - rightPadding;
      final timeStep = effectiveWidth / 4; // 5 меток времени
      
      for (int i = 0; i <= 4; i++) {
        final x = timeStep * i;
        final dateIndex = ((dates!.length - 1) * i / 4).round();
        
        if (dateIndex < dates!.length) {
          final date = dates![dateIndex];
          textPainter.text = TextSpan(
            text: '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 9,
            ),
          );
          textPainter.layout();
          textPainter.paint(
            canvas, 
            Offset(x - textPainter.width / 2, size.height - 15)
          );
        }
      }
    }
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  @override
  bool shouldRepaint(InteractiveChartPainter oldDelegate) {
    return oldDelegate.prices != prices ||
        oldDelegate.maxPrice != maxPrice ||
        oldDelegate.minPrice != minPrice ||
        oldDelegate.color != color ||
        oldDelegate.dates != dates;
  }
}
