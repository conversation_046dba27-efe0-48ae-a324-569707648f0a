import 'package:flutter/material.dart';
import '../models/anti_fomo_simulator_models.dart';

/// A widget that displays a scrolling news ticker
class NewsTickerWidget extends StatefulWidget {
  final List<TickerNewsItem> newsItems;
  final double height;
  final double scrollSpeed;

  const NewsTickerWidget({
    super.key,
    required this.newsItems,
    this.height = 40,
    this.scrollSpeed = 50.0, // pixels per second
  });

  @override
  State<NewsTickerWidget> createState() => _NewsTickerWidgetState();
}

class _NewsTickerWidgetState extends State<NewsTickerWidget> with SingleTickerProviderStateMixin {
  late ScrollController _scrollController;
  late AnimationController _animationController;
  late Animation<double> _animation;
  double _contentWidth = 0;
  double _containerWidth = 0;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 1), // Will be updated in post-frame callback
    );

    _animation = Tween<double>(
      begin: 0,
      end: 1,
    ).animate(_animationController);

    _animation.addListener(() {
      if (_containerWidth > 0 && _contentWidth > 0) {
        _scrollController.jumpTo(_animation.value * _contentWidth);
      }
    });

    // Wait for layout to complete to get actual widths
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _startScrolling();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(NewsTickerWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.newsItems != oldWidget.newsItems) {
      // Reset animation if news items change
      _startScrolling();
    }
  }

  void _startScrolling() {
    if (!mounted) return;

    // Get the width of the container and content
    final RenderBox? containerBox = context.findRenderObject() as RenderBox?;
    if (containerBox == null) return;
    
    _containerWidth = containerBox.size.width;
    
    // Estimate content width based on text length
    _contentWidth = 0;
    for (final item in widget.newsItems) {
      // Rough estimate: each character is about 8 pixels wide
      _contentWidth += (item.text.length * 8) + 40; // Add some padding between items
    }
    
    // Calculate animation duration based on content width and scroll speed
    final duration = (_contentWidth / widget.scrollSpeed).round();
    
    // Update animation
    _animationController.duration = Duration(seconds: duration);
    _animationController.reset();
    _animationController.repeat();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.newsItems.isEmpty) {
      return Container(
        height: widget.height,
        color: Colors.grey[900],
        child: const Center(
          child: Text(
            'No news available',
            style: TextStyle(color: Colors.white),
          ),
        ),
      );
    }

    return Container(
      height: widget.height,
      decoration: BoxDecoration(
        color: Colors.grey[900],
        border: Border(
          top: BorderSide(color: Colors.grey[800]!),
          bottom: BorderSide(color: Colors.grey[800]!),
        ),
      ),
      child: ListView.builder(
        controller: _scrollController,
        scrollDirection: Axis.horizontal,
        physics: const NeverScrollableScrollPhysics(),
        itemCount: widget.newsItems.length * 2, // Duplicate items for continuous scrolling
        itemBuilder: (context, index) {
          final itemIndex = index % widget.newsItems.length;
          final item = widget.newsItems[itemIndex];
          return _buildNewsItem(item);
        },
      ),
    );
  }

  Widget _buildNewsItem(TickerNewsItem item) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      alignment: Alignment.center,
      child: Row(
        children: [
          // Bullet or special indicator
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: item.isSpecial ? Colors.red : Colors.blue,
            ),
          ),
          const SizedBox(width: 8),
          
          // News text
          Text(
            item.text,
            style: TextStyle(
              color: item.isSpecial ? Colors.red : Colors.white,
              fontWeight: item.isSpecial ? FontWeight.bold : FontWeight.normal,
              fontSize: 13,
            ),
          ),
          
          // Separator
          const SizedBox(width: 16),
          const Text(
            '•',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 17,
            ),
          ),
          const SizedBox(width: 16),
        ],
      ),
    );
  }
}
