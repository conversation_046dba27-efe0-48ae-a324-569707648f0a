import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import '../models/candle.dart';

// Импорт для мобильных платформ
import 'improved_tradingview_chart_widget.dart';
// Для веб-платформы используем отдельную реализацию
import 'web_improved_tradingview_chart_widget.dart' if (dart.library.io) 'improved_tradingview_chart_widget.dart';

/// Фабрика для создания правильной реализации TradingView в зависимости от платформы
class PlatformImprovedTradingViewChartWidget extends StatefulWidget {
  final List<Candle> allCandles; // Все свечи
  final Function(double price, int time)? onEntryPointSet;
  final Function(bool isUp, double percentChange, double finalPrice)? onTradeResult;

  const PlatformImprovedTradingViewChartWidget({
    super.key,
    required this.allCandles,
    this.onEntryPointSet,
    this.onTradeResult,
  });

  // Статический метод для доступа к методу showAllCandles через GlobalKey
  static void showAllCandles(GlobalKey key) {
    final state = key.currentState as _PlatformImprovedTradingViewChartWidgetState?;
    state?.showAllCandles();
  }

  // Статический метод для доступа к методу setEntryPoint через GlobalKey
  static void setEntryPoint(GlobalKey key) {
    final state = key.currentState as _PlatformImprovedTradingViewChartWidgetState?;
    state?.setEntryPoint();
  }

  // Статический метод для доступа к методу saveChartState через GlobalKey
  static void saveChartState(GlobalKey key) {
    final state = key.currentState as _PlatformImprovedTradingViewChartWidgetState?;
    state?.saveChartState();
  }

  // Статический метод для доступа к методу restoreChartState через GlobalKey
  static void restoreChartState(GlobalKey key) {
    final state = key.currentState as _PlatformImprovedTradingViewChartWidgetState?;
    state?.restoreChartState();
  }

  // Статический метод для доступа к методу clearChartElements через GlobalKey
  static void clearChartElements(GlobalKey key) {
    final state = key.currentState as _PlatformImprovedTradingViewChartWidgetState?;
    state?.clearChartElements();
  }

  @override
  State<PlatformImprovedTradingViewChartWidget> createState() => _PlatformImprovedTradingViewChartWidgetState();
}

class _PlatformImprovedTradingViewChartWidgetState extends State<PlatformImprovedTradingViewChartWidget> {
  // Ключи для доступа к методам виджетов
  final GlobalKey _chartKey = GlobalKey();

  // Метод для отображения всех свечей
  void showAllCandles() {
    if (kIsWeb) {
      (_chartKey.currentState as WebImprovedTradingViewChartWidgetState?)?.showAllCandles();
    } else {
      (_chartKey.currentState as ImprovedTradingViewChartWidgetState?)?.showAllCandles();
    }
  }

  // Метод для отображения только начальных свечей
  void showInitialCandles() {
    if (kIsWeb) {
      (_chartKey.currentState as WebImprovedTradingViewChartWidgetState?)?.showInitialCandles();
    } else {
      (_chartKey.currentState as ImprovedTradingViewChartWidgetState?)?.showInitialCandles();
    }
  }

  // Метод для установки точки входа
  void setEntryPoint() {
    if (kIsWeb) {
      (_chartKey.currentState as WebImprovedTradingViewChartWidgetState?)?.setEntryPoint();
    } else {
      (_chartKey.currentState as ImprovedTradingViewChartWidgetState?)?.setEntryPoint();
    }
  }

  // Метод для сохранения состояния графика
  void saveChartState() {
    if (kIsWeb) {
      (_chartKey.currentState as WebImprovedTradingViewChartWidgetState?)?.saveChartState();
    } else {
      (_chartKey.currentState as ImprovedTradingViewChartWidgetState?)?.saveChartState();
    }
  }

  // Метод для восстановления состояния графика
  void restoreChartState() {
    if (kIsWeb) {
      (_chartKey.currentState as WebImprovedTradingViewChartWidgetState?)?.restoreChartState();
    } else {
      (_chartKey.currentState as ImprovedTradingViewChartWidgetState?)?.restoreChartState();
    }
  }

  // Метод для очистки элементов графика
  void clearChartElements() {
    if (kIsWeb) {
      (_chartKey.currentState as WebImprovedTradingViewChartWidgetState?)?.clearChartElements();
    } else {
      (_chartKey.currentState as ImprovedTradingViewChartWidgetState?)?.clearChartElements();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (kIsWeb) {
      // Для веб-платформы
      return WebImprovedTradingViewChartWidget(
        key: _chartKey,
        allCandles: widget.allCandles,
        onEntryPointSet: widget.onEntryPointSet,
        onTradeResult: widget.onTradeResult,
      );
    } else {
      // Для мобильных платформ
      return ImprovedTradingViewChartWidget(
        key: _chartKey,
        allCandles: widget.allCandles,
        onEntryPointSet: widget.onEntryPointSet,
        onTradeResult: widget.onTradeResult,
      );
    }
  }
}
