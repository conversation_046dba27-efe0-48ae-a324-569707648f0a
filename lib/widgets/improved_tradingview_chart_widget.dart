import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'dart:convert';
import 'dart:async';
import '../models/candle.dart';

/// Улучшенный виджет для отображения графика TradingView
/// с поддержкой всех требований из памяти пользователя
class ImprovedTradingViewChartWidget extends StatefulWidget {
  final List<Candle> allCandles; // Все свечи
  final Function(double price, int time)? onEntryPointSet;
  final Function(bool isUp, double percentChange, double finalPrice)? onTradeResult;

  const ImprovedTradingViewChartWidget({
    super.key,
    required this.allCandles,
    this.onEntryPointSet,
    this.onTradeResult,
  });

  @override
  State<ImprovedTradingViewChartWidget> createState() => ImprovedTradingViewChartWidgetState();
}

class ImprovedTradingViewChartWidgetState extends State<ImprovedTradingViewChartWidget> {
  late WebViewController _controller;
  bool _isWebViewLoaded = false;
  bool _areAllCandlesShown = false;
  bool _isEntryPointSet = false;

  @override
  void initState() {
    super.initState();
    _initWebView();
  }

  @override
  void didUpdateWidget(ImprovedTradingViewChartWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Если изменились свечи, загружаем их заново
    if (widget.allCandles != oldWidget.allCandles) {
      _loadCandles();
    }
  }

  // Инициализация WebView
  void _initWebView() {
    _controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0xFF131722))
      ..setNavigationDelegate(
        NavigationDelegate(
          onPageFinished: (String url) {
            debugPrint('WebView page loaded');
            setState(() {
              _isWebViewLoaded = true;
            });
            _loadCandles();
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint('WebView error: ${error.description}');
          },
        ),
      )
      ..addJavaScriptChannel(
        'FlutterChannel',
        onMessageReceived: _handleJavaScriptMessage,
      )
      ..loadFlutterAsset('assets/improved_tradingview_chart.html');
  }

  // Обработка сообщений от JavaScript
  void _handleJavaScriptMessage(JavaScriptMessage message) {
    try {
      final Map<String, dynamic> data = jsonDecode(message.message);
      final String type = data['type'];
      final Map<String, dynamic> payload = data['data'] ?? {};

      debugPrint('Received message from JavaScript: $type');

      switch (type) {
        case 'chartInitialized':
          _loadCandles();
          break;
        case 'initialCandlesShown':
          setState(() {
            _areAllCandlesShown = false;
            _isEntryPointSet = false;
          });
          break;
        case 'entryPointSet':
          setState(() {
            _isEntryPointSet = true;
          });
          if (widget.onEntryPointSet != null) {
            widget.onEntryPointSet!(
              payload['price'],
              payload['time'],
            );
          }
          break;
        case 'allCandlesShown':
          setState(() {
            _areAllCandlesShown = true;
          });
          break;
        case 'tradeResult':
          if (widget.onTradeResult != null) {
            widget.onTradeResult!(
              payload['isUp'],
              payload['percentChange'],
              payload['finalPrice'],
            );
          }
          break;
      }
    } catch (e) {
      debugPrint('Error handling JavaScript message: $e');
    }
  }

  // Загрузка свечей
  Future<void> _loadCandles() async {
    if (!_isWebViewLoaded || widget.allCandles.isEmpty) {
      return;
    }

    try {
      // Преобразуем свечи в формат для TradingView
      final List<Map<String, dynamic>> candlesData = widget.allCandles.map((candle) => {
        'time': candle.time,
        'open': candle.open,
        'high': candle.high,
        'low': candle.low,
        'close': candle.close,
        'volume': candle.volume,
      }).toList();

      // Создаем сообщение для отправки в JavaScript
      final String message = jsonEncode({
        'action': 'loadCandles',
        'data': candlesData,
      });

      // Отправляем данные в WebView
      await _controller.runJavaScript(
        'window.postMessage(${jsonEncode(message)}, "*")',
      );

      debugPrint('Candles data sent to WebView');
    } catch (e) {
      debugPrint('Error loading candles: $e');
    }
  }

  // Показать все свечи, включая будущие
  Future<void> showAllCandles() async {
    if (!_isWebViewLoaded) {
      return;
    }

    try {
      await _controller.runJavaScript(
        'window.postMessage(${jsonEncode({"action": "showAllCandles"})}, "*")',
      );
      debugPrint('Show all candles command sent');
    } catch (e) {
      debugPrint('Error showing all candles: $e');
    }
  }

  // Показать только начальные свечи
  Future<void> showInitialCandles() async {
    if (!_isWebViewLoaded) {
      return;
    }

    try {
      await _controller.runJavaScript(
        'window.postMessage(${jsonEncode({"action": "showInitialCandles"})}, "*")',
      );
      debugPrint('Show initial candles command sent');
    } catch (e) {
      debugPrint('Error showing initial candles: $e');
    }
  }

  // Установить точку входа
  Future<void> setEntryPoint() async {
    if (!_isWebViewLoaded) {
      return;
    }

    try {
      await _controller.runJavaScript(
        'window.postMessage(${jsonEncode({"action": "setEntryPoint"})}, "*")',
      );
      debugPrint('Set entry point command sent');
    } catch (e) {
      debugPrint('Error setting entry point: $e');
    }
  }

  // Сохранить состояние графика
  Future<void> saveChartState() async {
    if (!_isWebViewLoaded) {
      return;
    }

    try {
      await _controller.runJavaScript(
        'window.postMessage(${jsonEncode({"action": "saveChartState"})}, "*")',
      );
      debugPrint('Save chart state command sent');
    } catch (e) {
      debugPrint('Error saving chart state: $e');
    }
  }

  // Восстановить состояние графика
  Future<void> restoreChartState() async {
    if (!_isWebViewLoaded) {
      return;
    }

    try {
      await _controller.runJavaScript(
        'window.postMessage(${jsonEncode({"action": "restoreChartState"})}, "*")',
      );
      debugPrint('Restore chart state command sent');
    } catch (e) {
      debugPrint('Error restoring chart state: $e');
    }
  }

  // Очистить элементы графика
  Future<void> clearChartElements() async {
    if (!_isWebViewLoaded) {
      return;
    }

    try {
      await _controller.runJavaScript(
        'window.postMessage(${jsonEncode({"action": "clearChartElements"})}, "*")',
      );
      debugPrint('Clear chart elements command sent');
    } catch (e) {
      debugPrint('Error clearing chart elements: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return _isWebViewLoaded
        ? WebViewWidget(controller: _controller)
        : const Center(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(color: Colors.white),
                SizedBox(height: 16),
                Text('Loading chart...',
                  style: TextStyle(color: Colors.white70),
                ),
              ],
            ),
          );
  }
}
