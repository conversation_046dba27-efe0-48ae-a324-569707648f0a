import 'package:flutter/material.dart';

class TmmPngLogo extends StatelessWidget {
  final double size;

  const TmmPngLogo({
    super.key,
    this.size = 100,
  });

  @override
  Widget build(BuildContext context) {
    return Transform.translate(
      offset: const Offset(-0.5, 0.0), // Смещаем логотип влево на 0.5 пикселя
      child: Image.asset(
        'logo/TMM/TMM.png',
        width: size,
        height: size,
        fit: BoxFit.contain,
        errorBuilder: (context, error, stackTrace) {
          // Возвращаем заглушку вместо сообщения об ошибке
          return Container(
            width: size,
            height: size,
            decoration: BoxDecoration(
              color: Colors.transparent,
              borderRadius: BorderRadius.circular(size / 2),
            ),
            child: Center(
              child: Icon(
                Icons.rocket_launch,
                size: size * 0.6,
                color: Colors.white,
              ),
            ),
          );
        },
      ),
    );
  }
}
