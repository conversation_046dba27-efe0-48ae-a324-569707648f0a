import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'dart:math' as math;

class SimpleInteractiveChart extends StatefulWidget {
  final List<double> prices;
  final List<DateTime>? dates;
  final Color color;
  final double? maxPrice;
  final double? minPrice;
  final bool showGrid;
  final bool showLabels;
  final double rightPadding;

  const SimpleInteractiveChart({
    Key? key,
    required this.prices,
    this.dates,
    required this.color,
    this.maxPrice,
    this.minPrice,
    this.showGrid = true,
    this.showLabels = true,
    this.rightPadding = 80.0,
  }) : super(key: key);

  @override
  State<SimpleInteractiveChart> createState() => _SimpleInteractiveChartState();
}

class _SimpleInteractiveChartState extends State<SimpleInteractiveChart> {
  double _scale = 1.0;
  Offset _offset = Offset.zero;
  Offset? _lastPanPosition;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Основная область графика с интерактивностью
        Positioned.fill(
          child: Listener(
            onPointerSignal: (pointerSignal) {
              if (pointerSignal is PointerScrollEvent) {
                setState(() {
                  // Зум с помощью колесика мыши
                  final delta = pointerSignal.scrollDelta.dy;
                  final zoomFactor = delta > 0 ? 0.9 : 1.1;
                  _scale = (_scale * zoomFactor).clamp(0.5, 5.0);
                });
              }
            },
            child: GestureDetector(
              onPanStart: (details) {
                _lastPanPosition = details.localPosition;
              },
              onPanUpdate: (details) {
                if (_lastPanPosition != null) {
                  setState(() {
                    final delta = details.localPosition - _lastPanPosition!;
                    // Ограничиваем движение только по X (время) и немного по Y
                    _offset = Offset(
                      (_offset.dx + delta.dx).clamp(-500.0, 500.0),
                      (_offset.dy + delta.dy * 0.3).clamp(-100.0, 100.0),
                    );

                    _lastPanPosition = details.localPosition;
                  });
                }
              },
              onPanEnd: (details) {
                _lastPanPosition = null;
              },
              child: CustomPaint(
                size: Size.infinite,
                painter: SimpleChartPainter(
                  prices: widget.prices,
                  dates: widget.dates,
                  color: widget.color,
                  maxPrice: widget.maxPrice,
                  minPrice: widget.minPrice,
                  showGrid: widget.showGrid,
                  showLabels: false, // Отключаем метки в основном графике
                  rightPadding: widget.rightPadding,
                  scale: _scale,
                  offset: _offset,
                ),
              ),
            ),
          ),
        ),
        // Фиксированные оси и метки поверх графика
        if (widget.showLabels)
          Positioned.fill(
            child: CustomPaint(
              painter: AxisPainter(
                prices: widget.prices,
                maxPrice: widget.maxPrice,
                minPrice: widget.minPrice,
                rightPadding: widget.rightPadding,
              ),
            ),
          ),
      ],
    );
  }
}

class SimpleChartPainter extends CustomPainter {
  final List<double> prices;
  final List<DateTime>? dates;
  final Color color;
  final double? maxPrice;
  final double? minPrice;
  final bool showGrid;
  final bool showLabels;
  final double rightPadding;
  final double scale;
  final Offset offset;

  SimpleChartPainter({
    required this.prices,
    this.dates,
    required this.color,
    this.maxPrice,
    this.minPrice,
    required this.showGrid,
    required this.showLabels,
    required this.rightPadding,
    this.scale = 1.0,
    this.offset = Offset.zero,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (prices.isEmpty) return;

    // Вычисляем эффективную ширину с учетом отступа справа
    final effectiveWidth = size.width - rightPadding;
    
    final calculatedMaxPrice = maxPrice ?? prices.reduce(math.max);
    final calculatedMinPrice = minPrice ?? prices.reduce(math.min);
    final priceRange = calculatedMaxPrice - calculatedMinPrice;
    
    if (priceRange == 0) return;

    // Рисуем сетку, если включена
    if (showGrid) {
      _drawGrid(canvas, size);
    }

    // Рисуем график
    _drawChart(canvas, size, effectiveWidth, calculatedMaxPrice, calculatedMinPrice, priceRange);

    // Рисуем метки, если включены
    if (showLabels) {
      _drawLabels(canvas, size, calculatedMaxPrice, calculatedMinPrice, priceRange);
    }
  }

  void _drawGrid(Canvas canvas, Size size) {
    final gridPaint = Paint()
      ..color = Colors.grey.withOpacity(0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Горизонтальные линии
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
    }

    // Вертикальные линии
    for (int i = 0; i <= 6; i++) {
      final x = size.width * i / 6;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), gridPaint);
    }
  }

  void _drawChart(Canvas canvas, Size size, double effectiveWidth, double maxPrice, double minPrice, double priceRange) {
    // Сохраняем состояние canvas
    canvas.save();

    // Применяем трансформации
    canvas.translate(offset.dx, offset.dy);
    canvas.scale(scale, 1.0); // Масштабируем только по X

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0 / scale // Компенсируем масштаб для толщины линии
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round;

    final fillPaint = Paint()
      ..color = color.withOpacity(0.2)
      ..style = PaintingStyle.fill;

    final path = Path();
    final fillPath = Path();

    // Вычисляем шаг по X с учетом эффективной ширины
    final xStep = effectiveWidth / (prices.length - 1);

    // Начинаем с первой точки
    final firstPoint = Offset(
      0,
      size.height - ((prices[0] - minPrice) / priceRange * size.height),
    );
    path.moveTo(firstPoint.dx, firstPoint.dy);
    fillPath.moveTo(firstPoint.dx, size.height);
    fillPath.lineTo(firstPoint.dx, firstPoint.dy);

    // Добавляем остальные точки
    for (int i = 1; i < prices.length; i++) {
      final x = xStep * i;
      final y = size.height - ((prices[i] - minPrice) / priceRange * size.height);
      path.lineTo(x, y);
      fillPath.lineTo(x, y);
    }

    // Закрываем путь для заполнения
    fillPath.lineTo(effectiveWidth, size.height);
    fillPath.close();

    // Рисуем заполнение
    canvas.drawPath(fillPath, fillPaint);

    // Рисуем линию
    canvas.drawPath(path, paint);

    // Восстанавливаем состояние canvas
    canvas.restore();
  }

  void _drawLabels(Canvas canvas, Size size, double maxPrice, double minPrice, double priceRange) {
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Рисуем метки цен справа
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      final price = maxPrice - (i / 4) * priceRange;

      textPainter.text = TextSpan(
        text: _formatPrice(price),
        style: const TextStyle(color: Colors.white, fontSize: 10),
      );

      textPainter.layout();
      textPainter.paint(
        canvas, 
        Offset(size.width - textPainter.width - 4, y - textPainter.height / 2)
      );
    }
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  @override
  bool shouldRepaint(SimpleChartPainter oldDelegate) {
    return oldDelegate.prices != prices ||
        oldDelegate.maxPrice != maxPrice ||
        oldDelegate.minPrice != minPrice ||
        oldDelegate.color != color ||
        oldDelegate.dates != dates ||
        oldDelegate.scale != scale ||
        oldDelegate.offset != offset;
  }
}

// Класс для рисования фиксированных осей
class AxisPainter extends CustomPainter {
  final List<double> prices;
  final double? maxPrice;
  final double? minPrice;
  final double rightPadding;

  AxisPainter({
    required this.prices,
    this.maxPrice,
    this.minPrice,
    required this.rightPadding,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (prices.isEmpty) return;

    final calculatedMaxPrice = maxPrice ?? prices.reduce((a, b) => a > b ? a : b);
    final calculatedMinPrice = minPrice ?? prices.reduce((a, b) => a < b ? a : b);
    final priceRange = calculatedMaxPrice - calculatedMinPrice;

    if (priceRange == 0) return;

    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Рисуем метки цен справа (фиксированные)
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      final price = calculatedMaxPrice - (i / 4) * priceRange;

      textPainter.text = TextSpan(
        text: _formatPrice(price),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 10,
          backgroundColor: Colors.black54,
        ),
      );

      textPainter.layout();

      // Рисуем фон для текста
      final rect = Rect.fromLTWH(
        size.width - textPainter.width - 8,
        y - textPainter.height / 2 - 2,
        textPainter.width + 4,
        textPainter.height + 4,
      );

      canvas.drawRect(
        rect,
        Paint()..color = Colors.black54,
      );

      textPainter.paint(
        canvas,
        Offset(size.width - textPainter.width - 6, y - textPainter.height / 2)
      );
    }
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  @override
  bool shouldRepaint(AxisPainter oldDelegate) {
    return oldDelegate.prices != prices ||
        oldDelegate.maxPrice != maxPrice ||
        oldDelegate.minPrice != minPrice;
  }
}
