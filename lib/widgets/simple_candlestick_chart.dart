import 'package:finance_ai/models/trading_simulator_models.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

/// Простой и надёжный виджет для отображения свечного графика
class SimpleCandlestickChart extends StatelessWidget {
  final List<CandleData> candles;
  final double height;
  final Color upColor;
  final Color downColor;
  final Color gridColor;
  final Color textColor;
  final Color backgroundColor;
  final double? entryPrice;
  final int? entryIndex;
  final int emptyBars;
  final double zoomFactor;

  const SimpleCandlestickChart({
    super.key,
    required this.candles,
    this.height = 400,
    this.upColor = const Color(0xFF4ADE80),  // зелёный
    this.downColor = const Color(0xFFF87171), // красный
    this.gridColor = const Color(0xFF333333),
    this.textColor = const Color(0xFFCCCCCC),
    this.backgroundColor = const Color(0xFF121212),
    this.entryPrice,
    this.entryIndex,
    this.emptyBars = 0,
    this.zoomFactor = 1.0,
  });

  @override
  Widget build(BuildContext context) {
    // Если свечей нет, показываем заглушку
    if (candles.isEmpty) {
      return SizedBox(
        height: height,
        child: Center(
          child: Text(
            'Loading chart data...',
            style: TextStyle(color: textColor),
          ),
        ),
      );
    }

    print("РИСУЕМ ГРАФИК: ${candles.length} свечей, $emptyBars пустых баров, zoom: $zoomFactor");

    // Определяем min и max для оси Y
    double minY = double.infinity;
    double maxY = double.negativeInfinity;

    for (int i = 0; i < candles.length; i++) {
      final candle = candles[i];
      // Обновляем min и max для оси Y
      if (candle.low < minY) minY = candle.low;
      if (candle.high > maxY) maxY = candle.high;
    }

    // Добавляем отступ к min и max для лучшего отображения
    final padding = (maxY - minY) * 0.1;
    minY = minY - padding;
    maxY = maxY + padding;

    return Container(
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      child: CustomPaint(
        size: Size.infinite,
        painter: CandleSticksPainter(
          candles: candles,
          minY: minY,
          maxY: maxY,
          upColor: upColor,
          downColor: downColor,
          gridColor: gridColor,
          textColor: textColor,
          entryPrice: entryPrice,
          entryIndex: entryIndex,
          emptyBars: emptyBars,
          zoomFactor: zoomFactor,
        ),
      ),
    );
  }
}

/// Кастомный painter для отрисовки свечей
class CandleSticksPainter extends CustomPainter {
  final List<CandleData> candles;
  final double minY;
  final double maxY;
  final Color upColor;
  final Color downColor;
  final Color gridColor;
  final Color textColor;
  final double? entryPrice;
  final int? entryIndex;
  final int emptyBars;
  final double zoomFactor;

  CandleSticksPainter({
    required this.candles,
    required this.minY,
    required this.maxY,
    required this.upColor,
    required this.downColor,
    required this.gridColor,
    required this.textColor,
    this.entryPrice,
    this.entryIndex,
    this.emptyBars = 0,
    this.zoomFactor = 1.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final width = size.width;
    final height = size.height;
    
    // Рисуем сетку
    _drawGrid(canvas, size);
    
    // Рисуем ценовые метки на левой стороне
    _drawPriceLabels(canvas, size);
    
    // Рисуем горизонтальную линию для entryPrice, если он задан
    if (entryPrice != null) {
      _drawEntryLine(canvas, size);
    }
    
    // Рассчитываем ширину свечи и промежуток, учитывая зум
    final totalBars = candles.length + emptyBars;
    final effectiveWidth = zoomFactor > 1 ? width * zoomFactor : width;
    final visibleWidth = width;
    
    // Определяем смещение для горизонтального зума (концентрируем на последней свече)
    double offsetX = 0;
    if (zoomFactor > 1) {
      // Рассчитываем позицию последней свечи
      final lastCandlePosition = (candles.length - 1) / totalBars * effectiveWidth;
      // Устанавливаем смещение так, чтобы последняя свеча была ближе к центру
      offsetX = lastCandlePosition - (visibleWidth * 0.7);
      if (offsetX < 0) offsetX = 0;
      if (offsetX > effectiveWidth - visibleWidth) offsetX = effectiveWidth - visibleWidth;
    }
    
    final candleWidth = effectiveWidth / totalBars * 0.8;
    final spacing = effectiveWidth / totalBars * 0.2;
    
    // Рисуем свечи с учетом зума
    if (zoomFactor > 1) {
      canvas.save(); // Сохраняем состояние canvas перед изменениями
      canvas.clipRect(Rect.fromLTWH(0, 0, visibleWidth, height));
      
      // Применяем зум
      canvas.translate(-offsetX, 0);
    }
    
    // Рисуем свечи
    for (int i = 0; i < candles.length; i++) {
      final candle = candles[i];
      final x = i * (candleWidth + spacing) + spacing / 2;
      
      // Пересчитываем Y-координаты из цен
      final highY = _getYForPrice(candle.high, height);
      final lowY = _getYForPrice(candle.low, height);
      final openY = _getYForPrice(candle.open, height);
      final closeY = _getYForPrice(candle.close, height);
      
      // Определяем цвет свечи
      final bool isUp = candle.close >= candle.open;
      final color = isUp ? upColor : downColor;
      
      // Рисуем фитиль (вертикальную линию от high до low)
      final wickPaint = Paint()
        ..color = color
        ..strokeWidth = 1.0;
      
      canvas.drawLine(
        Offset(x + candleWidth / 2, highY),
        Offset(x + candleWidth / 2, lowY),
        wickPaint,
      );
      
      // Рисуем тело свечи (прямоугольник от open до close)
      final bodyTop = isUp ? closeY : openY;
      final bodyBottom = isUp ? openY : closeY;
      final bodyHeight = (bodyBottom - bodyTop).abs();
      
      final bodyPaint = Paint()
        ..color = color
        ..style = isUp ? PaintingStyle.stroke : PaintingStyle.fill;
      
      if (isUp) {
        bodyPaint.strokeWidth = 1.0;
      }
      
      canvas.drawRect(
        Rect.fromLTWH(x, bodyTop, candleWidth, bodyHeight),
        bodyPaint,
      );
      
      // Если это свеча входа, отмечаем стрелкой
      if (entryIndex != null && i == entryIndex) {
        _drawEntryArrow(canvas, x, candleWidth, lowY + 15, color);
      }
    }
    
    // Восстанавливаем состояние canvas после зума
    if (zoomFactor > 1) {
      canvas.restore(); // Обязательно вызываем restore() для каждого save()
    }
  }
  
  /// Преобразует цену в Y-координату
  double _getYForPrice(double price, double height) {
    return height - ((price - minY) / (maxY - minY) * height);
  }
  
  /// Рисует сетку
  void _drawGrid(Canvas canvas, Size size) {
    final gridPaint = Paint()
      ..color = gridColor
      ..strokeWidth = 0.5;
      
    // Горизонтальные линии
    for (int i = 0; i <= 5; i++) {
      final y = size.height * i / 5;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        gridPaint,
      );
    }
    
    // Вертикальные линии
    for (int i = 0; i <= 4; i++) {
      final x = size.width * i / 4;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        gridPaint,
      );
    }
  }
  
  /// Рисует ценовые метки на левой стороне
  void _drawPriceLabels(Canvas canvas, Size size) {
    final textStyle = TextStyle(
      color: textColor,
      fontSize: 10,
    );
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );
    
    for (int i = 0; i <= 5; i++) {
      final y = size.height * i / 5;
      final price = maxY - ((maxY - minY) * i / 5);
      
      textPainter.text = TextSpan(
        text: price.toStringAsFixed(2),
        style: textStyle,
      );
      
      textPainter.layout();
      
      textPainter.paint(
        canvas,
        Offset(5, y - textPainter.height / 2),
      );
    }
  }
  
  /// Рисует горизонтальную линию для цены входа
  void _drawEntryLine(Canvas canvas, Size size) {
    final entryY = _getYForPrice(entryPrice!, size.height);
    
    final linePaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 1.0
      ..strokeCap = StrokeCap.round;
      
    // Рисуем пунктирную линию
    final dashWidth = 5.0;
    final dashSpace = 3.0;
    double startX = 0;
    
    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, entryY),
        Offset(startX + dashWidth, entryY),
        linePaint,
      );
      startX += dashWidth + dashSpace;
    }
    
    // Рисуем метку цены
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );
    
    textPainter.text = TextSpan(
      text: 'Entry: ${entryPrice!.toStringAsFixed(2)}',
      style: const TextStyle(
        color: Colors.white,
        fontSize: 10,
        fontWeight: FontWeight.bold,
      ),
    );
    
    textPainter.layout();
    
    final labelBackground = Paint()
      ..color = Colors.black54
      ..style = PaintingStyle.fill;
      
    canvas.drawRect(
      Rect.fromLTWH(
        size.width - textPainter.width - 10,
        entryY - textPainter.height - 5,
        textPainter.width + 8,
        textPainter.height + 4,
      ),
      labelBackground,
    );
    
    textPainter.paint(
      canvas,
      Offset(size.width - textPainter.width - 6, entryY - textPainter.height - 3),
    );
  }

  /// Рисует стрелку для обозначения точки входа
  void _drawEntryArrow(Canvas canvas, double x, double candleWidth, double y, Color color) {
    final arrowPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();
    final arrowSize = 10.0;
    final centerX = x + candleWidth / 2;

    // Рисуем стрелку, направленную вверх
    path.moveTo(centerX, y - arrowSize);
    path.lineTo(centerX - arrowSize, y);
    path.lineTo(centerX + arrowSize, y);
    path.close();

    canvas.drawPath(path, arrowPaint);
    
    // Добавляем надпись "ENTRY"
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
    );
    
    textPainter.text = TextSpan(
      text: 'ENTRY',
      style: TextStyle(
        color: color,
        fontSize: 10,
        fontWeight: FontWeight.bold,
      ),
    );
    
    textPainter.layout();
    
    final labelBackground = Paint()
      ..color = Colors.black54
      ..style = PaintingStyle.fill;
      
    canvas.drawRect(
      Rect.fromLTWH(
        centerX - textPainter.width / 2 - 4,
        y + 5,
        textPainter.width + 8,
        textPainter.height + 4,
      ),
      labelBackground,
    );
    
    textPainter.paint(
      canvas,
      Offset(centerX - textPainter.width / 2, y + 7),
    );
  }

  @override
  bool shouldRepaint(covariant CandleSticksPainter oldDelegate) {
    return candles != oldDelegate.candles ||
           minY != oldDelegate.minY ||
           maxY != oldDelegate.maxY ||
           entryPrice != oldDelegate.entryPrice ||
           entryIndex != oldDelegate.entryIndex ||
           emptyBars != oldDelegate.emptyBars ||
           zoomFactor != oldDelegate.zoomFactor;
  }
} 