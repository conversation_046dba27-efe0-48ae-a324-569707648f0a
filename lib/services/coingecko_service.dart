import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import '../models/crypto_currency.dart';
import 'package:shared_preferences/shared_preferences.dart';

class CoinGeckoService {
  static const String _baseUrl = 'https://api.coingecko.com/api/v3';
  static const String _apiKey = ''; // Вставьте ваш API ключ CoinGecko здесь

  // Кэш для хранения данных о криптовалютах
  final Map<String, dynamic> _cache = {};

  // Ключи для SharedPreferences
  static const String _trendingCacheKey = 'trending_coins_cache';
  static const String _topCoinsCacheKey = 'top_coins_cache';
  static const String _cacheDateKey = 'cache_date';

  // Время жизни кэша (в часах)
  static const int _cacheLifetime = 1;

  // Конструктор, который загружает кэш из SharedPreferences
  CoinGeckoService() {
    _loadCache();
  }

  // Загрузка кэша из SharedPreferences
  Future<void> _loadCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Проверяем дату последнего обновления кэша
      final cacheDate = prefs.getString(_cacheDateKey);
      if (cacheDate != null) {
        final lastUpdate = DateTime.parse(cacheDate);
        final now = DateTime.now();
        final difference = now.difference(lastUpdate).inHours;

        // Если кэш устарел, не загружаем его
        if (difference >= _cacheLifetime) {
          return;
        }
      }

      // Загружаем кэш трендовых монет
      final trendingCache = prefs.getString(_trendingCacheKey);
      if (trendingCache != null) {
        _cache['trending'] = jsonDecode(trendingCache);
        debugPrint('Loaded trending coins from cache');
      }

      // Загружаем кэш топовых монет
      final topCoinsCache = prefs.getString(_topCoinsCacheKey);
      if (topCoinsCache != null) {
        _cache['top'] = jsonDecode(topCoinsCache);
        debugPrint('Loaded top coins from cache');
      }
    } catch (e) {
      debugPrint('Error loading cache: $e');
    }
  }

  // Сохранение кэша в SharedPreferences
  Future<void> _saveCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Сохраняем дату обновления кэша
      await prefs.setString(_cacheDateKey, DateTime.now().toIso8601String());

      // Сохраняем кэш трендовых монет
      if (_cache.containsKey('trending')) {
        await prefs.setString(_trendingCacheKey, jsonEncode(_cache['trending']));
        debugPrint('Saved trending coins to cache');
      }

      // Сохраняем кэш топовых монет
      if (_cache.containsKey('top')) {
        await prefs.setString(_topCoinsCacheKey, jsonEncode(_cache['top']));
        debugPrint('Saved top coins to cache');
      }
    } catch (e) {
      debugPrint('Error saving cache: $e');
    }
  }

  // Получение трендовых криптовалют
  Future<List<CryptoCurrency>> getTrendingCoins() async {
    try {
      // Проверяем кэш
      if (_cache.containsKey('trending')) {
        final cachedData = _cache['trending'];
        return _convertToCryptoCurrencyList(cachedData);
      }

      // Формируем URL для запроса
      final url = Uri.parse('$_baseUrl/search/trending');
      final headers = {
        'Accept-Encoding': 'gzip, deflate',
        'User-Agent': 'FinanceAI/1.0',
        if (_apiKey.isNotEmpty) 'x-cg-pro-api-key': _apiKey,
      };

      // Выполняем запрос
      final response = await http.get(url, headers: headers);

      // Проверяем статус ответа
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Сохраняем данные в кэш
        _cache['trending'] = data;
        _saveCache();

        return _convertToCryptoCurrencyList(data);
      } else {
        throw Exception('Failed to load trending coins: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching trending coins: $e');
      // В случае ошибки возвращаем пустой список
      return [];
    }
  }

  // Получение категорий криптовалют
  Future<Map<String, List<String>>> getCoinCategories() async {
    try {
      // Проверяем кэш
      if (_cache.containsKey('categories')) {
        return Map<String, List<String>>.from(_cache['categories']);
      }

      // Определяем категории вручную, так как API CoinGecko не предоставляет прямого доступа к ним
      final Map<String, List<String>> categories = {
        'AI Agents': [
          'fetch-ai', 'singularitynet', 'ocean-protocol', 'the-graph', 'render-token',
          'oasis-network', 'akash-network', 'cortex', 'numeraire', 'golem',
          'bittensor', 'artificial-liquid-intelligence-token', 'agoras-tokens', 'matrix-ai-network',
          'deepbrain-chain', 'singularitydao', 'nunet', 'phala-network', 'covalent',
          'injective', 'chainlink', 'worldcoin', 'oraichain-token', 'aleph-zero',
          'gala', 'theta-token', 'arweave', 'filecoin', 'stacks'
        ],
        'Memes': [
          'dogecoin', 'shiba-inu', 'pepe', 'floki', 'dogelon-mars',
          'bonk', 'wojak', 'mog-coin', 'cat-in-a-dogs-world', 'brett',
          'turbo', 'book-of-meme', 'meme-coin', 'popcat', 'milady-meme-coin',
          'dogwifhat', 'moo-deng', 'toshi', 'coq-inu', 'doge-killer',
          'pepe-unchained', 'brett', 'mog-coin', 'tamadoge', 'catecoin',
          'samoyedcoin', 'hoge-finance', 'banano', 'vita-inu', 'dejitaru-tsuka'
        ]
      };

      // Сохраняем данные в кэш
      _cache['categories'] = categories;
      _saveCache();

      return categories;
    } catch (e) {
      debugPrint('Error fetching coin categories: $e');
      // В случае ошибки возвращаем пустой словарь
      return {};
    }
  }

  // Получение криптовалют по категории
  Future<List<CryptoCurrency>> getCoinsByCategory(String category) async {
    try {
      // Проверяем кэш
      if (_cache.containsKey(category)) {
        final cachedData = _cache[category];
        return _convertToTopCoinsList(cachedData, categories: [category]);
      }

      // Получаем список ID криптовалют для указанной категории
      final categories = await getCoinCategories();
      if (!categories.containsKey(category)) {
        return [];
      }

      final coinIds = categories[category]!;
      if (coinIds.isEmpty) {
        return [];
      }

      // Разбиваем список ID на части по 25 элементов, чтобы не превышать лимиты API
      final List<List<String>> idBatches = [];
      for (int i = 0; i < coinIds.length; i += 25) {
        final end = (i + 25 < coinIds.length) ? i + 25 : coinIds.length;
        idBatches.add(coinIds.sublist(i, end));
      }

      // Результирующий список криптовалют
      final List<CryptoCurrency> result = [];

      // Обрабатываем каждую партию ID
      for (final batch in idBatches) {
        final idsParam = batch.join(',');
        final url = Uri.parse(
          '$_baseUrl/coins/markets?vs_currency=usd&ids=$idsParam&order=market_cap_desc&per_page=50&page=1&sparkline=true'
        );
        final headers = {
          'Accept-Encoding': 'gzip, deflate',
          'User-Agent': 'FinanceAI/1.0',
          if (_apiKey.isNotEmpty) 'x-cg-pro-api-key': _apiKey,
        };

        // Выполняем запрос
        final response = await http.get(url, headers: headers);

        // Проверяем статус ответа
        if (response.statusCode == 200) {
          final data = jsonDecode(response.body);

          // Добавляем результаты в общий список
          result.addAll(_convertToTopCoinsList(data, categories: [category]));
        } else {
          debugPrint('Failed to load batch of $category coins: ${response.statusCode}');
        }

        // Добавляем небольшую задержку между запросами, чтобы не превышать лимиты API
        await Future.delayed(const Duration(milliseconds: 500));
      }

      // Сохраняем данные в кэш
      _cache[category] = result.map((crypto) => {
        'id': crypto.id,
        'name': crypto.name,
        'symbol': crypto.symbol,
        'current_price': crypto.price,
        'price_change_percentage_24h': crypto.priceChangePercentage24h,
        'market_cap': crypto.marketCap,
        'total_volume': crypto.volume24h,
        'image': crypto.imageUrl,
      }).toList();
      _saveCache();

      return result;
    } catch (e) {
      debugPrint('Error fetching $category coins: $e');
      // В случае ошибки возвращаем пустой список
      return [];
    }
  }

  // Получение топовых криптовалют по рыночной капитализации
  Future<List<CryptoCurrency>> getTopCoins({int limit = 30}) async {
    try {
      // Проверяем кэш
      if (_cache.containsKey('top')) {
        final cachedData = _cache['top'];
        return _convertToTopCoinsList(cachedData, categories: ['Top']);
      }

      // Формируем URL для запроса
      final url = Uri.parse(
        '$_baseUrl/coins/markets?vs_currency=usd&order=market_cap_desc&per_page=$limit&page=1&sparkline=true&price_change_percentage=24h'
      );
      final headers = {
        'Accept-Encoding': 'gzip, deflate',
        'User-Agent': 'FinanceAI/1.0',
        if (_apiKey.isNotEmpty) 'x-cg-pro-api-key': _apiKey,
      };

      // Выполняем запрос
      final response = await http.get(url, headers: headers);

      // Проверяем статус ответа
      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);

        // Сохраняем данные в кэш
        _cache['top'] = data;
        _saveCache();

        return _convertToTopCoinsList(data, categories: ['Top']);
      } else {
        throw Exception('Failed to load top coins: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching top coins: $e');
      // В случае ошибки возвращаем пустой список
      return [];
    }
  }

  // Преобразование данных о трендовых монетах в список CryptoCurrency
  List<CryptoCurrency> _convertToCryptoCurrencyList(Map<String, dynamic> data) {
    final List<CryptoCurrency> result = [];

    if (data.containsKey('coins')) {
      final List<dynamic> coins = data['coins'];

      for (var coin in coins) {
        final item = coin['item'];
        final String id = item['id'] ?? '';
        final String symbol = item['symbol']?.toUpperCase() ?? '';

        // Создаем объект CryptoCurrency
        final crypto = CryptoCurrency(
          id: id,
          name: item['name'] ?? '',
          symbol: symbol,
          price: item['price_btc'] != null ? (item['price_btc'] as num).toDouble() * 60000 : 0.0, // Примерная конвертация из BTC в USD
          priceChangePercentage24h: 0.0, // Данные о изменении цены отсутствуют в этом API
          marketCap: 0.0, // Данные о рыночной капитализации отсутствуют в этом API
          volume24h: 0.0, // Данные об объеме торгов отсутствуют в этом API
          imageUrl: item['large'] ?? _getBackupImageUrl(symbol, id),
          priceHistory: [], // Данные для графика отсутствуют в этом API
          categories: ['Trending'], // Добавляем категорию "Trending"
        );

        result.add(crypto);
      }
    }

    return result;
  }

  // Получение запасного URL изображения
  String _getBackupImageUrl(String symbol, String id) {
    final urls = [
      'https://assets.coingecko.com/coins/images/1/large/$id.png',
      'https://cryptologos.cc/logos/$id-${symbol.toLowerCase()}-logo.png',
      'https://s2.coinmarketcap.com/static/img/coins/64x64/$id.png',
      'https://raw.githubusercontent.com/spothq/cryptocurrency-icons/master/128/color/${symbol.toLowerCase()}.png',
      'https://via.placeholder.com/50?text=$symbol',
    ];

    return urls[0];
  }

  // Преобразование данных о топовых монетах в список CryptoCurrency
  List<CryptoCurrency> _convertToTopCoinsList(List<dynamic> data, {List<String> categories = const []}) {
    final List<CryptoCurrency> result = [];

    for (var item in data) {
      final String id = item['id'] ?? '';
      final String symbol = item['symbol']?.toUpperCase() ?? '';

      // Создаем объект CryptoCurrency
      final crypto = CryptoCurrency(
        id: id,
        name: item['name'] ?? '',
        symbol: symbol,
        price: item['current_price'] != null ? (item['current_price'] as num).toDouble() : 0.0,
        priceChangePercentage24h: item['price_change_percentage_24h'] != null ? (item['price_change_percentage_24h'] as num).toDouble() : 0.0,
        marketCap: item['market_cap'] != null ? (item['market_cap'] as num).toDouble() : 0.0,
        volume24h: item['total_volume'] != null ? (item['total_volume'] as num).toDouble() : 0.0,
        imageUrl: item['image'] ?? _getBackupImageUrl(symbol, id),
        priceHistory: _convertSparklineToHistory(item['sparkline_in_7d']?['price']),
        categories: categories,
      );

      result.add(crypto);
    }

    return result;
  }

  // Преобразование данных спарклайна в историю цен
  List<PricePoint> _convertSparklineToHistory(List<dynamic>? sparklineData) {
    if (sparklineData == null) {
      return [];
    }

    final List<PricePoint> result = [];
    final now = DateTime.now();

    for (int i = 0; i < sparklineData.length; i++) {
      final price = (sparklineData[i] as num).toDouble();
      // Создаем временную метку с равными интервалами
      final time = now.subtract(Duration(hours: sparklineData.length - i));
      result.add(PricePoint(time, price));
    }

    return result;
  }

  // Получение OHLC данных для графиков
  Future<List<List<dynamic>>> getOHLCData({
    required String coinId,
    required String interval,
    int? limit,
  }) async {
    try {
      // Определяем количество дней для запроса в зависимости от интервала
      int days;
      switch (interval) {
        case '30m':
          days = 2; // 2 дня для 30-минутных свечей
          break;
        case '1h':
          days = 1; // 1 день для часовых свечей
          break;
        case '4h':
          days = 7; // 7 дней для 4-часовых свечей
          break;
        case '1d':
          days = 30; // 30 дней для дневных свечей
          break;
        case '1w':
          days = 365; // 1 год для недельных свечей
          break;
        default:
          days = 1;
      }

      // Формируем URL для запроса OHLC данных
      final url = Uri.parse('$_baseUrl/coins/$coinId/ohlc?vs_currency=usd&days=$days');
      final headers = {
        'Accept-Encoding': 'gzip, deflate',
        'User-Agent': 'FinanceAI/1.0',
        if (_apiKey.isNotEmpty) 'x-cg-pro-api-key': _apiKey,
      };

      // Выполняем запрос
      final response = await http.get(url, headers: headers);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);

        // Преобразуем данные в формат Binance (для совместимости)
        final List<List<dynamic>> klines = [];

        for (var ohlc in data) {
          if (ohlc is List && ohlc.length >= 5) {
            // CoinGecko OHLC формат: [timestamp, open, high, low, close]
            // Binance формат: [timestamp, open, high, low, close, volume, ...]
            klines.add([
              ohlc[0], // timestamp
              ohlc[1].toString(), // open
              ohlc[2].toString(), // high
              ohlc[3].toString(), // low
              ohlc[4].toString(), // close
              '0', // volume (не предоставляется CoinGecko в OHLC)
              0, // close time
              0, // quote asset volume
              0, // number of trades
              0, // taker buy base asset volume
              0, // taker buy quote asset volume
              0, // ignore
            ]);
          }
        }

        // Ограничиваем количество свечей если указан лимит
        if (limit != null && klines.length > limit) {
          return klines.sublist(klines.length - limit);
        }

        print('📊 Получено ${klines.length} OHLC свечей для $coinId');
        return klines;
      } else {
        throw Exception('Failed to load OHLC data: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching OHLC data: $e');
      // В случае ошибки возвращаем пустой список
      return [];
    }
  }
}
