import 'dart:async';
import 'dart:convert';
import 'dart:math';

import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import '../models/sentiment_history_model.dart';

/// A completely redesigned service for fetching and managing market sentiment data
/// with optimized performance, robust caching, and parallel API requests
class OptimizedSentimentService {
  // API URLs
  static const String _fearGreedUrl = 'https://api.alternative.me/fng/';
  static const String _binanceUrl = 'https://api.binance.com/api/v3';
  static const String _coinGeckoUrl = 'https://api.coingecko.com/api/v3';
  static const String _cryptoPanicUrl = 'https://cryptopanic.com/api/v1/posts/';
  static const String _cryptoPanicToken = '38f4c3b9e89ed5c0fda9211409cd20a05a19b079';

  // Cache keys
  static const String _cachePrefix = 'sentiment_cache_';
  static const String _lastUpdateKey = '${_cachePrefix}last_update';
  static const String _indicatorKey = '${_cachePrefix}indicator';
  static const String _metricsKey = '${_cachePrefix}metrics';

  // Cache duration (1 minute for testing)
  static const Duration _cacheDuration = Duration(minutes: 1);

  // HTTP client with timeout
  final _client = http.Client();
  final Duration _timeout = const Duration(seconds: 10);

  // Singleton instance
  static final OptimizedSentimentService _instance = OptimizedSentimentService._internal();

  // Factory constructor
  factory OptimizedSentimentService() => _instance;

  // Private constructor
  OptimizedSentimentService._internal();

  /// Get the current market sentiment with optimized loading
  Future<Map<String, dynamic>> getMarketSentiment() async {
    debugPrint('Getting market sentiment data...');

    try {
      // Check if we have valid cached data
      if (await _isCacheValid()) {
        debugPrint('Using cached sentiment data');
        return await _getCachedData();
      }

      debugPrint('Cache invalid or not found, fetching fresh data...');

      // Start all API requests in parallel
      final futures = await Future.wait([
        _fetchFearGreedIndex(),
        _fetchNewsSentiment(),
        _fetchHoldersScore(),
        _fetchVolumeScore(),
        _fetchSocialEngagement(),
        _fetchPriceVolatility(),
        _fetchBitcoinDominance(),
        _fetchAltcoinShare(),
        _fetchStablecoinLiquidity(),
        _fetchPutCallRatio(),
        _fetchOrderBookDepth(),
        _fetchVolumeChangeVelocity(),
        _fetchVolatilityIndex(),
      ]);

      // Extract results
      final fearGreedIndex = futures[0];
      final newsSentiment = futures[1];
      final holdersScore = futures[2];
      final volumeScore = futures[3];
      final socialEngagement = futures[4];
      final priceVolatility = futures[5];
      final bitcoinDominance = futures[6];
      final altcoinShare = futures[7];
      final stablecoinLiquidity = futures[8];
      final putCallRatio = futures[9];
      final orderBookDepth = futures[10];
      final volumeChangeVelocity = futures[11];
      final volatilityIndex = futures[12];

      // Normalize values
      final normalizedNewsSentiment = (newsSentiment + 1) * 50;
      final normalizedVolumeScore = volumeScore.clamp(0.0, 1.0) * 100;

      // Create metrics map
      final metrics = <String, double>{
        'Fear & Greed Index': fearGreedIndex,
        'News Sentiment': normalizedNewsSentiment,
        'Holders Score': holdersScore,
        'Volume Score': normalizedVolumeScore,
        'Social Engagement': socialEngagement,
        'Price Volatility': priceVolatility,
        'Bitcoin Dominance': bitcoinDominance,
        'Altcoin Share': altcoinShare,
        'Stablecoin Liquidity': stablecoinLiquidity,
        'Put/Call Ratio': putCallRatio,
        'Order Book Depth': orderBookDepth,
        'Volume Change Velocity': volumeChangeVelocity,
        'Volatility Index': volatilityIndex,
      };

      // Calculate indicator value
      final indicator = _calculateIndicator(metrics);

      // Create result map
      final result = {
        'indicator': indicator,
        'metrics': metrics,
      };

      // Cache the data
      await _cacheData(result);

      // Save to history
      await _saveToHistory(indicator, metrics);

      return result;
    } catch (e) {
      debugPrint('Error getting market sentiment: $e');

      // Try to use cached data even if it's expired
      try {
        final cachedData = await _getCachedData(ignoreExpiration: true);
        debugPrint('Using expired cached data as fallback');
        return cachedData;
      } catch (cacheError) {
        debugPrint('No cached data available: $cacheError');

        // Return default values as last resort
        return {
          'indicator': 50.0,
          'metrics': {
            'Fear & Greed Index': 50.0,
            'News Sentiment': 50.0,
            'Holders Score': 50.0,
            'Volume Score': 50.0,
            'Social Engagement': 50.0,
            'Price Volatility': 50.0,
            'Bitcoin Dominance': 50.0,
          },
        };
      }
    }
  }

  /// Check if the cache is valid
  Future<bool> _isCacheValid() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastUpdateStr = prefs.getString(_lastUpdateKey);

      if (lastUpdateStr == null) {
        return false;
      }

      final lastUpdate = DateTime.parse(lastUpdateStr);
      final now = DateTime.now();

      return now.difference(lastUpdate) < _cacheDuration;
    } catch (e) {
      debugPrint('Error checking cache validity: $e');
      return false;
    }
  }

  /// Get cached data
  Future<Map<String, dynamic>> _getCachedData({bool ignoreExpiration = false}) async {
    final prefs = await SharedPreferences.getInstance();

    // Check if cache exists
    if (!prefs.containsKey(_indicatorKey) || !prefs.containsKey(_metricsKey)) {
      throw Exception('Cache not found');
    }

    // Check expiration if needed
    if (!ignoreExpiration) {
      final lastUpdateStr = prefs.getString(_lastUpdateKey);
      if (lastUpdateStr == null) {
        throw Exception('Cache timestamp not found');
      }

      final lastUpdate = DateTime.parse(lastUpdateStr);
      final now = DateTime.now();

      if (now.difference(lastUpdate) >= _cacheDuration) {
        throw Exception('Cache expired');
      }
    }

    // Get cached data
    final indicator = prefs.getDouble(_indicatorKey) ?? 50.0;
    final metricsJson = prefs.getString(_metricsKey);

    if (metricsJson == null) {
      throw Exception('Metrics cache not found');
    }

    final metrics = Map<String, double>.from(
      (json.decode(metricsJson) as Map).map(
        (key, value) => MapEntry(key, value.toDouble()),
      ),
    );

    return {
      'indicator': indicator,
      'metrics': metrics,
    };
  }

  /// Cache data
  Future<void> _cacheData(Map<String, dynamic> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save indicator
      await prefs.setDouble(_indicatorKey, data['indicator']);

      // Save metrics
      await prefs.setString(_metricsKey, json.encode(data['metrics']));

      // Update timestamp
      await prefs.setString(_lastUpdateKey, DateTime.now().toIso8601String());

      debugPrint('Data cached successfully');
    } catch (e) {
      debugPrint('Error caching data: $e');
    }
  }

  /// Save to history
  Future<void> _saveToHistory(double indicator, Map<String, double> metrics) async {
    try {
      // Create history entry
      final entry = SentimentHistoryEntry(
        date: DateTime.now(),
        value: indicator,
        metrics: metrics,
      );

      // Get current history
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString('sentiment_history');

      // Parse history or create new
      final history = historyJson != null
          ? SentimentHistory.fromJson(historyJson)
          : SentimentHistory(entries: []);

      // Add entry
      history.addEntry(entry);

      // Save updated history
      await prefs.setString('sentiment_history', history.toJson());

      debugPrint('Saved to history: ${entry.date} - ${entry.value}');
    } catch (e) {
      debugPrint('Error saving to history: $e');
    }
  }

  /// Calculate the indicator value from metrics
  double _calculateIndicator(Map<String, double> metrics) {
    final fearGreedIndex = metrics['Fear & Greed Index'] ?? 50.0;
    final newsSentiment = metrics['News Sentiment'] ?? 50.0;
    final holdersScore = metrics['Holders Score'] ?? 50.0;
    final volumeScore = metrics['Volume Score'] ?? 50.0;
    final socialEngagement = metrics['Social Engagement'] ?? 50.0;
    final priceVolatility = metrics['Price Volatility'] ?? 50.0;
    final bitcoinDominance = metrics['Bitcoin Dominance'] ?? 50.0;
    final altcoinShare = metrics['Altcoin Share'] ?? 50.0;
    final stablecoinLiquidity = metrics['Stablecoin Liquidity'] ?? 50.0;
    final putCallRatio = metrics['Put/Call Ratio'] ?? 50.0;
    final orderBookDepth = metrics['Order Book Depth'] ?? 50.0;
    final volumeChangeVelocity = metrics['Volume Change Velocity'] ?? 50.0;
    final volatilityIndex = metrics['Volatility Index'] ?? 50.0;

    // Calculate weighted average with updated weights
    final weightedAverage =
        (fearGreedIndex * 0.20) +
        (newsSentiment * 0.15) +
        (holdersScore * 0.15) +
        (volumeScore * 0.15) +
        (socialEngagement * 0.10) +
        (priceVolatility * 0.10) +
        (bitcoinDominance * 0.15) +
        (altcoinShare * 0.15) +
        (stablecoinLiquidity * 0.15) +
        (putCallRatio * 0.10) +
        (orderBookDepth * 0.10) +
        (volumeChangeVelocity * 0.10) +
        (volatilityIndex * 0.10);

    // Normalize the weighted average to account for the additional metrics
    final normalizedScore = (weightedAverage / 1.7).clamp(0.0, 100.0);

    // Log the contribution of each metric for debugging
    debugPrint('=== METRIC CONTRIBUTIONS ===');
    debugPrint('Fear & Greed Index (20%): ${fearGreedIndex * 0.20}');
    debugPrint('News Sentiment (15%): ${newsSentiment * 0.15}');
    debugPrint('Holders Score (15%): ${holdersScore * 0.15}');
    debugPrint('Volume Score (15%): ${volumeScore * 0.15}');
    debugPrint('Social Engagement (10%): ${socialEngagement * 0.10}');
    debugPrint('Price Volatility (10%): ${priceVolatility * 0.10}');
    debugPrint('Bitcoin Dominance (15%): ${bitcoinDominance * 0.15}');
    debugPrint('Altcoin Share (15%): ${altcoinShare * 0.15}');
    debugPrint('Stablecoin Liquidity (15%): ${stablecoinLiquidity * 0.15}');
    debugPrint('Put/Call Ratio (10%): ${putCallRatio * 0.10}');
    debugPrint('Order Book Depth (10%): ${orderBookDepth * 0.10}');
    debugPrint('Volume Change Velocity (10%): ${volumeChangeVelocity * 0.10}');
    debugPrint('Volatility Index (10%): ${volatilityIndex * 0.10}');
    debugPrint('Raw weighted score: $weightedAverage');
    debugPrint('Normalized final score: $normalizedScore');

    return normalizedScore;
  }

  /// Get historical data
  Future<SentimentHistory> getHistoricalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString('sentiment_history');

      if (historyJson != null) {
        return SentimentHistory.fromJson(historyJson);
      }
    } catch (e) {
      debugPrint('Error getting historical data: $e');
    }

    return SentimentHistory(entries: []);
  }

  /// Clear all cached data (for testing)
  Future<void> clearAllData() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Clear cache
      await prefs.remove(_indicatorKey);
      await prefs.remove(_metricsKey);
      await prefs.remove(_lastUpdateKey);

      // Clear history
      await prefs.remove('sentiment_history');

      debugPrint('All data cleared');
    } catch (e) {
      debugPrint('Error clearing data: $e');
    }
  }

  // API Methods with robust error handling and timeouts

  /// Fetch Fear & Greed Index
  Future<double> _fetchFearGreedIndex() async {
    try {
      final response = await _client
          .get(Uri.parse('${_fearGreedUrl}?limit=1'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final value = double.parse(data['data'][0]['value']);
        debugPrint('Fear & Greed Index: $value');
        return value;
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching Fear & Greed Index: $e');
      return 50.0; // Default value
    }
  }

  /// Fetch News Sentiment
  Future<double> _fetchNewsSentiment() async {
    try {
      final response = await _client
          .get(Uri.parse('${_cryptoPanicUrl}?auth_token=${_cryptoPanicToken}&currencies=BTC,ETH'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final results = data['results'] as List;

        if (results.isEmpty) {
          return 0.0; // Neutral
        }

        // Calculate sentiment from news
        int positive = 0;
        int negative = 0;

        for (var item in results.take(10)) {
          final votes = item['votes'] ?? {};
          final upVotes = votes['positive'] ?? 0;
          final downVotes = votes['negative'] ?? 0;

          if (upVotes > downVotes) {
            positive++;
          } else if (downVotes > upVotes) {
            negative++;
          }
        }

        // Calculate sentiment score between -1 and 1
        final total = positive + negative;
        if (total == 0) {
          return 0.0; // Neutral
        }

        final sentiment = (positive - negative) / total;
        debugPrint('News Sentiment: $sentiment');
        return sentiment;
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching News Sentiment: $e');
      return 0.0; // Neutral value
    }
  }

  /// Fetch Holders Score based on market cap and trading volume
  Future<double> _fetchHoldersScore() async {
    try {
      // Use CoinGecko API to get market data for Bitcoin
      final response = await _client
          .get(Uri.parse('$_coinGeckoUrl/coins/bitcoin/market_chart?vs_currency=usd&days=30'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        // Get market caps and volumes
        final marketCaps = data['market_caps'] as List<dynamic>;
        final volumes = data['total_volumes'] as List<dynamic>;

        if (marketCaps.isEmpty || volumes.isEmpty) {
          debugPrint('No market data available');
          return 50.0;
        }

        // Get current values (last in the list)
        final currentMarketCap = marketCaps.last[1] as num;
        final currentVolume = volumes.last[1] as num;

        // Calculate averages for the last 30 days
        final avgMarketCap = marketCaps
            .map((entry) => entry[1] as num)
            .reduce((a, b) => a + b) / marketCaps.length;

        final avgVolume = volumes
            .map((entry) => entry[1] as num)
            .reduce((a, b) => a + b) / volumes.length;

        // Calculate ratios (current vs average)
        final marketCapRatio = (currentMarketCap / avgMarketCap).clamp(0.5, 2.0);
        final volumeRatio = (currentVolume / avgVolume).clamp(0.5, 2.0);

        // Calculate score (50% market cap, 50% volume)
        // Higher market cap and higher volume = higher score
        final rawScore = (marketCapRatio * 50 + volumeRatio * 50) / 2;

        // Normalize to 0-100 range
        final score = ((rawScore - 25) * 2).clamp(0.0, 100.0);

        debugPrint('Holders Score: $score (Market Cap Ratio: $marketCapRatio, Volume Ratio: $volumeRatio)');
        return score;
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching Holders Score: $e');
      return 50.0; // Default value
    }
  }

  /// Fetch Volume Score
  Future<double> _fetchVolumeScore() async {
    try {
      final response = await _client
          .get(Uri.parse('${_binanceUrl}/ticker/24hr?symbol=BTCUSDT'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final volume = double.parse(data['volume']);

        // Normalize volume to a score between 0 and 1
        // This is a simplified approach
        final normalizedVolume = min(volume / 100000, 1.0);

        debugPrint('Volume Score: $normalizedVolume');
        return normalizedVolume;
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching Volume Score: $e');
      return 0.5; // Default value
    }
  }

  /// Fetch Social Engagement based on Twitter followers and Reddit subscribers
  Future<double> _fetchSocialEngagement() async {
    try {
      // Use CoinGecko API to get social data for Bitcoin
      final response = await _client
          .get(Uri.parse('$_coinGeckoUrl/coins/bitcoin?localization=false&tickers=false&market_data=false&community_data=true&developer_data=false'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final communityData = data['community_data'];

        if (communityData == null) {
          debugPrint('No community data available');
          return 50.0;
        }

        // Get Twitter followers and Reddit subscribers
        final twitterFollowers = (communityData['twitter_followers'] ?? 0) as num;
        final redditSubscribers = (communityData['reddit_subscribers'] ?? 0) as num;
        final redditActiveAccounts = (communityData['reddit_accounts_active_48h'] ?? 0) as num;

        // Reference values for normalization
        const refTwitterFollowers = 5000000.0; // 5 million followers
        const refRedditSubscribers = 4000000.0; // 4 million subscribers
        const refRedditActive = 10000.0; // 10,000 active accounts

        // Calculate normalized scores (0-100)
        final twitterScore = (twitterFollowers / refTwitterFollowers * 100).clamp(0.0, 100.0);
        final redditSubScore = (redditSubscribers / refRedditSubscribers * 100).clamp(0.0, 100.0);
        final redditActiveScore = (redditActiveAccounts / refRedditActive * 100).clamp(0.0, 100.0);

        // Calculate weighted average (40% Twitter, 30% Reddit Subscribers, 30% Reddit Active)
        final score = (twitterScore * 0.4) + (redditSubScore * 0.3) + (redditActiveScore * 0.3);

        debugPrint('Social Engagement: $score (Twitter: $twitterScore, Reddit Subs: $redditSubScore, Reddit Active: $redditActiveScore)');
        return score;
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching Social Engagement: $e');
      return 50.0; // Default value
    }
  }

  /// Fetch Price Volatility
  Future<double> _fetchPriceVolatility() async {
    try {
      final response = await _client
          .get(Uri.parse('${_binanceUrl}/klines?symbol=BTCUSDT&interval=1d&limit=7'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as List;

        if (data.isEmpty) {
          return 50.0;
        }

        // Calculate volatility from price data
        final prices = data.map((e) => double.parse(e[4])).toList();
        double sum = 0;
        double sumSquared = 0;

        for (var price in prices) {
          sum += price;
          sumSquared += price * price;
        }

        final mean = sum / prices.length;
        final variance = (sumSquared / prices.length) - (mean * mean);
        final stdDev = sqrt(variance);

        // Normalize to 0-100 scale
        // Higher volatility = higher score
        final volatility = (stdDev / mean * 100).clamp(0.0, 100.0);

        debugPrint('Price Volatility: $volatility');
        return volatility;
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching Price Volatility: $e');
      return 50.0; // Default value
    }
  }

  /// Fetch Bitcoin Dominance
  Future<double> _fetchBitcoinDominance() async {
    try {
      final response = await _client
          .get(Uri.parse('${_coinGeckoUrl}/global'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final dominance = data['data']['market_cap_percentage']['btc'].toDouble();

        debugPrint('Bitcoin Dominance: $dominance');
        return dominance;
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching Bitcoin Dominance: $e');
      return 50.0; // Default value
    }
  }

  /// Fetch Altcoin Market Share
  Future<double> _fetchAltcoinShare() async {
    try {
      final response = await _client
          .get(Uri.parse('${_coinGeckoUrl}/global'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final btcDominance = data['data']['market_cap_percentage']['btc'].toDouble();
        final altcoinShare = 100.0 - btcDominance;

        debugPrint('Altcoin Share: $altcoinShare');
        return altcoinShare;
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching Altcoin Share: $e');
      return 50.0; // Default value
    }
  }

  /// Fetch Stablecoin Liquidity
  Future<double> _fetchStablecoinLiquidity() async {
    try {
      final response = await _client
          .get(Uri.parse('${_coinGeckoUrl}/coins/markets?vs_currency=usd&category=stablecoins'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as List;

        if (data.isEmpty) {
          return 50.0;
        }

        // Calculate total market cap of stablecoins in billions
        double totalMarketCap = 0.0;
        for (var coin in data) {
          totalMarketCap += (coin['market_cap'] ?? 0) / 1e9; // Convert to billions
        }

        // Normalize to 0-100 scale (assuming 200B is max)
        final normalizedValue = (totalMarketCap / 200.0 * 100.0).clamp(0.0, 100.0);

        debugPrint('Stablecoin Liquidity: $normalizedValue (Total: $totalMarketCap billion USD)');
        return normalizedValue;
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching Stablecoin Liquidity: $e');
      return 50.0; // Default value
    }
  }

  /// Fetch Put/Call Ratio (proxy via bid/ask ratio)
  Future<double> _fetchPutCallRatio() async {
    try {
      final response = await _client
          .get(Uri.parse('${_binanceUrl}/depth?symbol=BTCUSDT&limit=100'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final bids = data['bids'] as List;
        final asks = data['asks'] as List;

        // Calculate total volume for bids and asks
        double totalBids = 0.0;
        double totalAsks = 0.0;

        for (var bid in bids) {
          totalBids += double.parse(bid[1]);
        }

        for (var ask in asks) {
          totalAsks += double.parse(ask[1]);
        }

        // Calculate ratio (bids/asks)
        final ratio = totalAsks > 0 ? totalBids / totalAsks : 1.0;

        // Normalize to 0-100 scale (0.5 to 2.0 is normal range)
        final normalizedValue = ((ratio - 0.5) / 1.5 * 100.0).clamp(0.0, 100.0);

        debugPrint('Put/Call Ratio: $normalizedValue (Ratio: $ratio)');
        return normalizedValue;
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching Put/Call Ratio: $e');
      return 50.0; // Default value
    }
  }

  /// Fetch Order Book Depth
  Future<double> _fetchOrderBookDepth() async {
    try {
      final response = await _client
          .get(Uri.parse('${_binanceUrl}/depth?symbol=BTCUSDT&limit=100'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final bids = data['bids'] as List;

        // Calculate total bid volume in millions USD
        double totalBids = 0.0;
        for (var bid in bids) {
          totalBids += double.parse(bid[1]);
        }

        totalBids = totalBids / 1e6; // Convert to millions

        // Normalize to 0-100 scale (assuming 100M is max)
        final normalizedValue = (totalBids / 100.0 * 100.0).clamp(0.0, 100.0);

        debugPrint('Order Book Depth: $normalizedValue (Total: $totalBids million USD)');
        return normalizedValue;
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching Order Book Depth: $e');
      return 50.0; // Default value
    }
  }

  /// Fetch Volume Change Velocity
  Future<double> _fetchVolumeChangeVelocity() async {
    try {
      final response = await _client
          .get(Uri.parse('${_binanceUrl}/ticker/24hr?symbol=BTCUSDT'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final currentVolume = double.parse(data['volume']);

        // Get previous volume from cache
        final prefs = await SharedPreferences.getInstance();
        final prevVolume = prefs.getDouble('prevVolume') ?? currentVolume;

        // Calculate percent change
        final percentChange = ((currentVolume - prevVolume) / prevVolume * 100.0).clamp(-100.0, 100.0);

        // Save current volume for next time
        await prefs.setDouble('prevVolume', currentVolume);

        // Normalize to 0-100 scale (-100% to +100% range)
        final normalizedValue = ((percentChange + 100.0) / 2.0).clamp(0.0, 100.0);

        debugPrint('Volume Change Velocity: $normalizedValue (Change: $percentChange%)');
        return normalizedValue;
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching Volume Change Velocity: $e');
      return 50.0; // Default value
    }
  }

  /// Fetch Volatility Index
  Future<double> _fetchVolatilityIndex() async {
    try {
      final response = await _client
          .get(Uri.parse('${_binanceUrl}/klines?symbol=BTCUSDT&interval=1m&limit=30'))
          .timeout(_timeout);

      if (response.statusCode == 200) {
        final data = json.decode(response.body) as List;

        if (data.isEmpty) {
          return 50.0;
        }

        // Extract closing prices
        final prices = data.map((k) => double.parse(k[4])).toList();

        // Calculate mean
        final mean = prices.reduce((a, b) => a + b) / prices.length;

        // Calculate variance
        final variance = prices.map((x) => pow(x - mean, 2)).reduce((a, b) => a + b) / prices.length;

        // Calculate standard deviation
        final stdDev = sqrt(variance);

        // Calculate coefficient of variation (CV)
        final cv = (stdDev / mean) * 100;

        // Normalize to 0-100 scale (assuming 10% CV is max)
        final normalizedValue = (cv / 10.0 * 100.0).clamp(0.0, 100.0);

        debugPrint('Volatility Index: $normalizedValue (CV: $cv%)');
        return normalizedValue;
      } else {
        throw Exception('Server error: ${response.statusCode}');
      }
    } catch (e) {
      debugPrint('Error fetching Volatility Index: $e');
      return 50.0; // Default value
    }
  }
}
