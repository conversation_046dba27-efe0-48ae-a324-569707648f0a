import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/crypto_currency.dart';

class FavoritesService {
  // Ключ для SharedPreferences
  static const String _favoritesKey = 'favorite_cryptos';
  
  // Список избранных криптовалют
  final List<String> _favorites = [];
  
  // Конструктор, который загружает избранные из SharedPreferences
  FavoritesService() {
    _loadFavorites();
  }
  
  // Загрузка избранных из SharedPreferences
  Future<void> _loadFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final favoritesJson = prefs.getString(_favoritesKey);
      
      if (favoritesJson != null) {
        final List<dynamic> decoded = jsonDecode(favoritesJson);
        _favorites.clear();
        _favorites.addAll(decoded.map((item) => item.toString()));
        debugPrint('Loaded ${_favorites.length} favorites from storage');
      }
    } catch (e) {
      debugPrint('Error loading favorites: $e');
    }
  }
  
  // Сохранение избранных в SharedPreferences
  Future<void> _saveFavorites() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_favoritesKey, jsonEncode(_favorites));
      debugPrint('Saved ${_favorites.length} favorites to storage');
    } catch (e) {
      debugPrint('Error saving favorites: $e');
    }
  }
  
  // Получение списка избранных криптовалют
  List<String> getFavorites() {
    return List.from(_favorites);
  }
  
  // Проверка, является ли криптовалюта избранной
  bool isFavorite(String id) {
    return _favorites.contains(id);
  }
  
  // Добавление криптовалюты в избранное
  Future<void> addFavorite(String id) async {
    if (!_favorites.contains(id)) {
      _favorites.add(id);
      await _saveFavorites();
    }
  }
  
  // Удаление криптовалюты из избранного
  Future<void> removeFavorite(String id) async {
    if (_favorites.contains(id)) {
      _favorites.remove(id);
      await _saveFavorites();
    }
  }
  
  // Переключение статуса избранного для криптовалюты
  Future<bool> toggleFavorite(String id) async {
    if (_favorites.contains(id)) {
      _favorites.remove(id);
      await _saveFavorites();
      return false;
    } else {
      _favorites.add(id);
      await _saveFavorites();
      return true;
    }
  }
  
  // Фильтрация списка криптовалют, оставляя только избранные
  List<CryptoCurrency> filterFavorites(List<CryptoCurrency> cryptos) {
    return cryptos.where((crypto) => _favorites.contains(crypto.id)).toList();
  }
  
  // Сортировка списка криптовалют, помещая избранные в начало
  List<CryptoCurrency> sortWithFavoritesFirst(List<CryptoCurrency> cryptos) {
    final List<CryptoCurrency> result = List.from(cryptos);
    result.sort((a, b) {
      final aIsFavorite = _favorites.contains(a.id);
      final bIsFavorite = _favorites.contains(b.id);
      
      if (aIsFavorite && !bIsFavorite) {
        return -1;
      } else if (!aIsFavorite && bIsFavorite) {
        return 1;
      } else {
        return 0;
      }
    });
    
    return result;
  }
}
