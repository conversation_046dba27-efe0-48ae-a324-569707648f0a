import 'dart:convert';
import 'dart:math';
import 'package:http/http.dart' as http;

class HistoricalDataService {
  static const String _baseUrl = 'https://api.binance.com/api/v3/klines';
  
  // Кэш для хранения исторических данных
  static final Map<String, List<List<dynamic>>> _cache = {};
  
  /// Загружает исторические данные для криптовалюты
  /// [symbol] - символ криптовалюты (например, BTCUSDT)
  /// [interval] - интервал времени (1m, 5m, 15m, 1h, 4h, 1d)
  /// [limit] - количество свечей (максимум 1000)
  /// [startTime] - начальное время (timestamp в миллисекундах)
  /// [endTime] - конечное время (timestamp в миллисекундах)
  static Future<List<List<dynamic>>> getHistoricalData({
    required String symbol,
    String interval = '1h',
    int limit = 500,
    int? startTime,
    int? endTime,
  }) async {
    try {
      // Создаем ключ для кэша
      final cacheKey = '${symbol}_${interval}_${startTime}_${endTime}_$limit';
      
      // Проверяем кэш
      if (_cache.containsKey(cacheKey)) {
        print('📊 Загружаем данные из кэша для $symbol');
        return _cache[cacheKey]!;
      }
      
      // Формируем URL для запроса
      final uri = Uri.parse(_baseUrl).replace(queryParameters: {
        'symbol': symbol,
        'interval': interval,
        'limit': limit.toString(),
        if (startTime != null) 'startTime': startTime.toString(),
        if (endTime != null) 'endTime': endTime.toString(),
      });
      
      print('📊 Загружаем исторические данные для $symbol с $uri');
      
      final response = await http.get(uri).timeout(
        const Duration(seconds: 10),
        onTimeout: () {
          throw Exception('Timeout при загрузке данных');
        },
      );
      
      if (response.statusCode == 200) {
        final List<dynamic> data = json.decode(response.body);
        final List<List<dynamic>> klines = data.map((item) => List<dynamic>.from(item)).toList();
        
        // Сохраняем в кэш
        _cache[cacheKey] = klines;
        
        print('📊 Загружено ${klines.length} свечей для $symbol');
        return klines;
      } else {
        print('❌ Ошибка загрузки данных: ${response.statusCode}');
        throw Exception('Ошибка загрузки данных: ${response.statusCode}');
      }
    } catch (e) {
      print('❌ Ошибка при загрузке исторических данных: $e');
      // Возвращаем моковые данные в случае ошибки
      return _generateMockData(symbol, limit);
    }
  }
  
  /// Загружает данные для определенного периода времени
  static Future<List<List<dynamic>>> getDataForPeriod({
    required String symbol,
    required DateTime startDate,
    required DateTime endDate,
    String interval = '1h',
  }) async {
    final startTime = startDate.millisecondsSinceEpoch;
    final endTime = endDate.millisecondsSinceEpoch;
    
    return getHistoricalData(
      symbol: symbol,
      interval: interval,
      startTime: startTime,
      endTime: endTime,
      limit: 1000,
    );
  }
  
  /// Загружает более старые данные (для прокрутки назад)
  static Future<List<List<dynamic>>> getOlderData({
    required String symbol,
    required DateTime beforeDate,
    String interval = '1h',
    int limit = 500,
  }) async {
    final endTime = beforeDate.millisecondsSinceEpoch;
    
    return getHistoricalData(
      symbol: symbol,
      interval: interval,
      endTime: endTime,
      limit: limit,
    );
  }
  
  /// Генерирует моковые данные в случае ошибки
  static List<List<dynamic>> _generateMockData(String symbol, int count) {
    print('📊 Генерируем моковые данные для $symbol');
    
    final random = Random();
    final now = DateTime.now();
    final List<List<dynamic>> klines = [];
    
    // Начальная цена (зависит от символа)
    double basePrice = 50000; // BTC
    if (symbol.contains('ETH')) {
      basePrice = 3000;
    } else if (symbol.contains('SOL')) {
      basePrice = 150;
    } else if (symbol.contains('ADA')) {
      basePrice = 1.5;
    }
    
    double currentPrice = basePrice;
    
    for (int i = count - 1; i >= 0; i--) {
      final timestamp = now.subtract(Duration(hours: i)).millisecondsSinceEpoch;
      
      // Генерируем реалистичные изменения цены
      final change = (random.nextDouble() - 0.5) * 0.02; // ±1% изменение
      currentPrice = currentPrice * (1 + change);
      
      final open = currentPrice;
      final volatility = currentPrice * 0.01; // 1% волатильность
      final high = open + random.nextDouble() * volatility;
      final low = open - random.nextDouble() * volatility;
      final close = low + random.nextDouble() * (high - low);
      final volume = (random.nextDouble() * 1000000).toStringAsFixed(2);
      
      klines.add([
        timestamp,
        open.toStringAsFixed(2),
        high.toStringAsFixed(2),
        low.toStringAsFixed(2),
        close.toStringAsFixed(2),
        volume,
        timestamp + 3600000, // Close time
        '0', // Quote asset volume
        0, // Number of trades
        '0', // Taker buy base asset volume
        '0', // Taker buy quote asset volume
        '0' // Ignore
      ]);
      
      currentPrice = close;
    }
    
    return klines;
  }
  
  /// Очищает кэш
  static void clearCache() {
    _cache.clear();
    print('📊 Кэш исторических данных очищен');
  }
  
  /// Получает размер кэша
  static int getCacheSize() {
    return _cache.length;
  }
  
  /// Конвертирует интервал времени в миллисекунды
  static int intervalToMilliseconds(String interval) {
    switch (interval) {
      case '1m':
        return 60 * 1000;
      case '5m':
        return 5 * 60 * 1000;
      case '15m':
        return 15 * 60 * 1000;
      case '30m':
        return 30 * 60 * 1000;
      case '1h':
        return 60 * 60 * 1000;
      case '4h':
        return 4 * 60 * 60 * 1000;
      case '1d':
        return 24 * 60 * 60 * 1000;
      case '1w':
        return 7 * 24 * 60 * 60 * 1000;
      default:
        return 60 * 60 * 1000; // 1 час по умолчанию
    }
  }
}
