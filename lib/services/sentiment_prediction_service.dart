import 'dart:math';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/sentiment_history_model.dart';
import '../utils/linear_regression.dart';
import '../utils/ml_linear_regression.dart';
import '../config/storage_keys.dart';

/// Service for predicting future market sentiment based on historical data
class SentimentPredictionService {
  // Use unified storage keys from config
  final String _historicalDataKey = StorageKeys.historicalData;
  // Make prediction keys public and static so they can be accessed from MarketSentimentService
  static final String predictionsKey = StorageKeys.predictions;
  static final String lastMetricsKey = StorageKeys.metricsHash;
  final LinearRegression _regression = LinearRegression();

  /// Get historical sentiment data from local storage
  Future<SentimentHistory> getHistoricalData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_historicalDataKey);

      if (jsonString != null) {
        debugPrint('Found historical data in storage');
        final history = SentimentHistory.fromJson(jsonString);
        debugPrint('Loaded ${history.entries.length} historical entries');

        // Log the entries for debugging
        for (var entry in history.entries) {
          debugPrint('Historical entry: ${entry.date} - ${entry.value}');
        }

        return history;
      } else {
        debugPrint('No historical data found in storage');
      }
    } catch (e) {
      debugPrint('Error loading historical sentiment data: $e');
    }

    // Return empty history if no data is found or an error occurs
    debugPrint('Returning empty history');
    return SentimentHistory(entries: []);
  }

  /// Save historical sentiment data to local storage
  Future<void> saveHistoricalData(SentimentHistory history) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = history.toJson();
      await prefs.setString(_historicalDataKey, jsonString);
      debugPrint('Saved ${history.entries.length} entries to storage');
    } catch (e) {
      debugPrint('Error saving historical sentiment data: $e');
    }
  }

  /// Add a new entry to the historical data
  Future<void> addHistoricalEntry(SentimentHistoryEntry entry) async {
    try {
      debugPrint('Adding new entry to history: ${entry.date} - ${entry.value}');

      // Validate entry value
      if (entry.value.isNaN || entry.value.isInfinite) {
        debugPrint('ERROR: Invalid entry value (NaN or Infinity), not adding to history');
        return;
      }

      // Ensure value is within valid range
      double validValue = entry.value.clamp(0.0, 100.0);
      if (validValue != entry.value) {
        debugPrint('WARNING: Entry value out of range, clamping from ${entry.value} to $validValue');
        entry = SentimentHistoryEntry(
          date: entry.date,
          value: validValue,
          metrics: entry.metrics,
        );
      }

      final history = await getHistoricalData();

      // Check if we already have an entry for today
      final today = DateTime(DateTime.now().year, DateTime.now().month, DateTime.now().day);
      final todayEntry = history.entries.where((e) =>
        DateTime(e.date.year, e.date.month, e.date.day).isAtSameMomentAs(today)
      ).toList();

      if (todayEntry.isNotEmpty) {
        debugPrint('Found existing entry for today (${todayEntry.first.value}), will update to ${entry.value}');

        // Check if the value is significantly different
        final diff = (entry.value - todayEntry.first.value).abs();
        if (diff > 20.0) {
          debugPrint('WARNING: New value differs significantly from previous value (diff: $diff)');
          // We'll still update it, but log a warning
        }
      } else {
        debugPrint('No entry for today, adding new one');
      }

      // Add the entry (this will replace any existing entry for the same date)
      history.addEntry(entry);

      // Limit history size to prevent excessive storage
      if (history.entries.length > 60) { // Keep about 2 months of data
        // Sort by date (oldest first)
        history.entries.sort((a, b) => a.date.compareTo(b.date));

        // Remove oldest entries
        final entriesToRemove = history.entries.length - 60;
        if (entriesToRemove > 0) {
          debugPrint('Removing $entriesToRemove old entries to limit history size');
          history.entries.removeRange(0, entriesToRemove);
        }
      }

      // Save the updated history
      await saveHistoricalData(history);

      // Verify the entry was added
      final updatedHistory = await getHistoricalData();
      final addedEntry = updatedHistory.entries.where((e) =>
        DateTime(e.date.year, e.date.month, e.date.day).isAtSameMomentAs(today)
      ).toList();

      if (addedEntry.isNotEmpty) {
        debugPrint('Successfully added/updated entry for today: ${addedEntry.first.value}');
      } else {
        debugPrint('Failed to add entry for today');
      }
    } catch (e) {
      debugPrint('Error adding historical entry: $e');
    }
  }

  /// Calculate a hash of the metrics to detect changes
  /// Uses a more sensitive approach to detect even small changes in metrics
  String _calculateMetricsHash(Map<String, double> metrics) {
    // Sort the keys to ensure consistent order
    final sortedKeys = metrics.keys.toList()..sort();

    // Create a string representation of the metrics
    final buffer = StringBuffer();

    // Add the date to ensure predictions are stable for the same day
    // but can change between days
    final now = DateTime.now();
    final dateKey = '${now.year}-${now.month}-${now.day}';
    buffer.write('date:$dateKey;');

    // Add a minute-based component to force updates more frequently
    // This will make predictions update at least every 15 minutes
    final minuteGroup = (now.minute / 15).floor(); // 0, 1, 2, 3 (every 15 minutes)
    buffer.write('time:$minuteGroup;');

    for (final key in sortedKeys) {
      // Round to 1 decimal place to be more sensitive to changes
      // while still avoiding noise from tiny fluctuations
      final roundedValue = (metrics[key]! * 10).round() / 10;
      buffer.write('$key:$roundedValue;');
    }

    final result = buffer.toString();
    debugPrint('Calculated metrics hash: $result');
    return result;
  }

  /// Save predictions to cache
  Future<void> _savePredictions(List<SentimentHistoryEntry> predictions, Map<String, double> metrics) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Convert predictions to JSON
      final jsonList = predictions.map((entry) => entry.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      // Save predictions
      await prefs.setString(predictionsKey, jsonString);

      // Save metrics hash
      final metricsHash = _calculateMetricsHash(metrics);
      await prefs.setString(lastMetricsKey, metricsHash);

      debugPrint('Saved ${predictions.length} predictions to cache with metrics hash: $metricsHash');
    } catch (e) {
      debugPrint('Error saving predictions to cache: $e');
    }
  }

  /// Get cached predictions
  Future<List<SentimentHistoryEntry>?> _getCachedPredictions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(predictionsKey);

      if (jsonString != null) {
        final jsonList = jsonDecode(jsonString) as List;
        final predictions = jsonList
            .map((json) => SentimentHistoryEntry.fromJson(json as Map<String, dynamic>))
            .toList();

        debugPrint('Retrieved ${predictions.length} predictions from cache');
        return predictions;
      }
    } catch (e) {
      debugPrint('Error getting cached predictions: $e');
    }

    return null;
  }

  /// Check if metrics have changed significantly since last prediction
  /// Uses a more robust approach to determine if predictions should be regenerated
  Future<bool> _haveMetricsChanged(Map<String, double> currentMetrics) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final lastMetricsHash = prefs.getString(lastMetricsKey);

      // If no previous hash exists, we need to generate new predictions
      if (lastMetricsHash == null) {
        debugPrint('No previous metrics hash found, metrics considered changed');
        return true;
      }

      // Calculate current hash
      final currentHash = _calculateMetricsHash(currentMetrics);

      // Check if the hash has changed
      final hasChanged = currentHash != lastMetricsHash;

      // Log the result
      if (hasChanged) {
        debugPrint('Metrics have changed - will generate new predictions');
        debugPrint('Previous hash: $lastMetricsHash');
        debugPrint('Current hash: $currentHash');
      } else {
        debugPrint('Metrics have not changed - will use cached predictions');
      }

      // Check if we have cached predictions
      final cachedPredictions = await _getCachedPredictions();
      if (cachedPredictions == null || cachedPredictions.isEmpty) {
        debugPrint('No cached predictions found, will generate new ones regardless of metrics');
        return true;
      }

      return hasChanged;
    } catch (e) {
      debugPrint('Error checking if metrics have changed: $e');
      return true; // Assume changed on error
    }
  }

  /// Predict future sentiment values for the next specified number of days
  /// Enhanced with weighted recent data and market trend analysis
  Future<List<SentimentHistoryEntry>> predictFutureSentiment(
    int daysAhead,
    [Map<String, double>? currentMetrics]
  ) async {
    // If metrics are provided, check if they've changed
    if (currentMetrics != null) {
      final metricsChanged = await _haveMetricsChanged(currentMetrics);

      // If metrics haven't changed, try to use cached predictions
      if (!metricsChanged) {
        final cachedPredictions = await _getCachedPredictions();
        if (cachedPredictions != null && cachedPredictions.length >= daysAhead) {
          debugPrint('Using cached predictions as metrics have not changed');
          return cachedPredictions.take(daysAhead).toList();
        }
      }
    }

    final history = await getHistoricalData();

    debugPrint('Generating new predictions for the next $daysAhead days');
    debugPrint('Historical data points available: ${history.entries.length}');

    // Need at least 3 data points for meaningful prediction
    if (history.entries.length < 3) {
      debugPrint('Not enough historical data for prediction, using default values');
      final predictions = _generateDefaultPredictions(daysAhead);

      // Cache the predictions if metrics are provided
      if (currentMetrics != null) {
        await _savePredictions(predictions, currentMetrics);
      }

      return predictions;
    }

    try {
      // Sort entries by date (newest first)
      history.entries.sort((a, b) => b.date.compareTo(a.date));

      // Get the current sentiment value (most recent entry)
      final currentValue = history.entries.first.value;
      debugPrint('Current sentiment value: $currentValue');

      // Prepare data for regression with weighted recent data
      final x = <double>[];
      final y = <double>[];
      final weights = <double>[]; // For weighted regression

      // Use the last 21 days of data (or all if less than 21) - increased from 14
      final dataPoints = history.entries.length > 21
          ? history.entries.sublist(0, 21)
          : history.entries;

      debugPrint('Using ${dataPoints.length} data points for prediction');

      // Convert dates to numerical values (days since epoch)
      final firstDate = dataPoints.last.date;
      debugPrint('First date for prediction: $firstDate');

      // Calculate recent volatility to adjust predictions
      double volatility = 0;
      if (dataPoints.length >= 5) {
        final recentValues = dataPoints.take(5).map((e) => e.value).toList();
        final mean = recentValues.reduce((a, b) => a + b) / recentValues.length;
        volatility = recentValues.map((v) => (v - mean) * (v - mean)).reduce((a, b) => a + b) / recentValues.length;
        volatility = sqrt(volatility);
        debugPrint('Recent volatility: $volatility');
      }

      // Log all data points being used with weights
      for (int i = 0; i < dataPoints.length; i++) {
        final entry = dataPoints[i];
        final daysSinceFirst = entry.date.difference(firstDate).inDays.toDouble();

        // Apply higher weights to more recent data
        // Weight formula: recency weight (exponential decay)
        final recencyWeight = exp(-0.1 * i); // Higher weight for recent data

        debugPrint('Data point: ${entry.date} (day $daysSinceFirst) - value: ${entry.value}, weight: $recencyWeight');

        x.add(daysSinceFirst);
        y.add(entry.value);
        weights.add(recencyWeight);
      }

      // If all values are the same, use a more sophisticated trend based on market conditions
      if (y.toSet().length == 1) {
        debugPrint('All historical values are the same (${y.first}), using enhanced trend analysis');
        return _generateEnhancedTrendPredictions(daysAhead, y.first, volatility);
      }

      // Train the regression model with weights
      _regression.trainWeighted(x, y, weights);

      // Calculate R-squared to check prediction quality
      final rSquared = _regression.calculateRSquared(x, y);
      debugPrint('Weighted linear regression R-squared: $rSquared');

      // If R-squared is too low, use a more sophisticated approach
      if (rSquared < 0.4) {
        debugPrint('R-squared too low, using enhanced trend analysis with recent data');

        // Calculate weighted average of recent values (last 7 days or all if less)
        final recentPoints = dataPoints.length > 7 ? dataPoints.sublist(0, 7) : dataPoints;
        double weightedSum = 0;
        double weightSum = 0;

        for (int i = 0; i < recentPoints.length; i++) {
          final weight = exp(-0.2 * i); // Exponential decay weight
          weightedSum += recentPoints[i].value * weight;
          weightSum += weight;
        }

        final weightedAverage = weightedSum / weightSum;
        debugPrint('Weighted average of recent values: $weightedAverage');

        // Detect trend direction from recent data
        bool uptrend = false;
        if (recentPoints.length >= 3) {
          // Simple trend detection: compare average of first half vs second half
          final firstHalf = recentPoints.sublist(recentPoints.length ~/ 2);
          final secondHalf = recentPoints.sublist(0, recentPoints.length ~/ 2);

          final firstHalfAvg = firstHalf.map((e) => e.value).reduce((a, b) => a + b) / firstHalf.length;
          final secondHalfAvg = secondHalf.map((e) => e.value).reduce((a, b) => a + b) / secondHalf.length;

          uptrend = secondHalfAvg > firstHalfAvg;
          debugPrint('Trend detection: ${uptrend ? "Uptrend" : "Downtrend"} (recent avg: $secondHalfAvg, older avg: $firstHalfAvg)');
        }

        return _generateEnhancedTrendPredictions(daysAhead, weightedAverage, volatility, uptrend: uptrend);
      }

      // Generate predictions with the regression model
      final predictions = <SentimentHistoryEntry>[];
      final today = DateTime.now();

      // Calculate confidence based on statistical methods
      final confidences = <double>[];

      // Calculate future dates as numerical values
      final futureDays = <double>[];
      for (int i = 1; i <= daysAhead; i++) {
        final futureDate = today.add(Duration(days: i));
        final daysSinceFirst = futureDate.difference(firstDate).inDays.toDouble();
        futureDays.add(daysSinceFirst);
      }

      // Calculate confidence for each prediction day
      for (int i = 0; i < daysAhead; i++) {
        final futureDay = futureDays[i];
        final dayConfidence = _regression.calculatePredictionConfidence(futureDay, x, y);
        confidences.add(dayConfidence);
        debugPrint('Prediction confidence for day ${i+1}: $dayConfidence');
      }

      // Log average confidence
      final avgConfidence = confidences.reduce((a, b) => a + b) / confidences.length;
      debugPrint('Average prediction confidence: $avgConfidence');

      for (int i = 1; i <= daysAhead; i++) {
        final futureDate = today.add(Duration(days: i));
        final daysSinceFirst = futureDate.difference(firstDate).inDays.toDouble();

        // Predict the value
        double predictedValue = _regression.predict(daysSinceFirst);

        // Apply volatility adjustment - higher volatility means more uncertainty
        // Add small random variation based on historical volatility
        if (volatility > 0) {
          // Create a deterministic seed based on the date and prediction day
          final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;
          final seed = (dayOfYear * 1000 + i * 100 + predictedValue.round()).toInt();

          // Use seeded random for stable predictions
          final random = Random(seed);
          final volatilityFactor = volatility * 0.5; // Scale down the volatility effect
          final randomAdjustment = (random.nextDouble() * 2 - 1) * volatilityFactor;
          predictedValue += randomAdjustment;
          debugPrint('Applied volatility adjustment: $randomAdjustment (seed: $seed)');
        }

        // Clamp the value to the valid range
        predictedValue = predictedValue.clamp(0.0, 100.0);

        debugPrint('Enhanced prediction for day $i ($futureDate): $predictedValue');

        // Get the confidence for this prediction day
        final dayConfidence = confidences[i-1];

        // Create a prediction entry with confidence metrics
        final metrics = <String, double>{
          'confidence': dayConfidence * 100,
          'volatility': volatility,
        };

        predictions.add(SentimentHistoryEntry(
          date: futureDate,
          value: predictedValue,
          metrics: metrics,
        ));
      }

      // Cache the predictions if metrics are provided
      if (currentMetrics != null) {
        await _savePredictions(predictions, currentMetrics);
      }

      return predictions;
    } catch (e) {
      debugPrint('Error predicting future sentiment: $e');
      final defaultPredictions = _generateDefaultPredictions(daysAhead);

      // Cache the default predictions if metrics are provided
      if (currentMetrics != null) {
        await _savePredictions(defaultPredictions, currentMetrics);
      }

      return defaultPredictions;
    }
  }

  /// Generate enhanced predictions based on a sophisticated trend analysis
  /// Takes into account volatility and trend direction
  /// Uses a deterministic seed for random values to ensure stable predictions
  List<SentimentHistoryEntry> _generateEnhancedTrendPredictions(
    int daysAhead,
    double baseValue,
    double volatility,
    {bool uptrend = true}
  ) {
    final predictions = <SentimentHistoryEntry>[];
    final today = DateTime.now();

    // Create a deterministic seed based on the date (day of year)
    // This ensures predictions are stable for the same day
    final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;
    final seed = (dayOfYear * 1000 + baseValue.round() * 10 + volatility.round()).toInt();
    debugPrint('Using deterministic seed for predictions: $seed (based on day of year and metrics)');

    // Create a seeded random generator for stable predictions
    final random = Random(seed);

    // Determine trend direction and strength based on current value and market conditions
    double trendStrength;

    // Trend strength is higher when value is in extreme ranges (close to 0 or 100)
    // This simulates mean reversion - extreme values tend to normalize
    if (baseValue > 80) {
      // Very high values tend to revert downward
      trendStrength = -1.0 - (random.nextDouble() * 0.5);
      uptrend = false;
    } else if (baseValue < 20) {
      // Very low values tend to revert upward
      trendStrength = 1.0 + (random.nextDouble() * 0.5);
      uptrend = true;
    } else if (baseValue > 60) {
      // Moderately high values have a slight downward bias
      trendStrength = uptrend ? 0.5 : -0.8;
    } else if (baseValue < 40) {
      // Moderately low values have a slight upward bias
      trendStrength = uptrend ? 0.8 : -0.5;
    } else {
      // Neutral values can go either way with moderate strength
      trendStrength = uptrend ? 0.7 : -0.7;
    }

    // Adjust trend strength based on volatility
    // Higher volatility means stronger trends (in either direction)
    final trendVolatilityFactor = 1.0 + (volatility / 10.0);
    trendStrength *= trendVolatilityFactor;

    // Maximum change percentage over the prediction period
    final maxChangePercent = 8.0 * trendVolatilityFactor.clamp(1.0, 2.0);

    // Calculate confidence based on multiple factors
    // Base confidence starts higher for more accurate predictions
    final confidenceBase = 0.85;

    // Adjust for volatility (higher volatility = lower confidence)
    final confidenceVolatilityFactor = (volatility / 20.0).clamp(0.0, 0.3);

    // Adjust for market extremes (extreme values are less predictable)
    final extremenessFactor = (baseValue < 30 || baseValue > 70) ? 0.1 : 0.0;

    // Calculate base confidence with adjustments
    final adjustedConfidenceBase = confidenceBase - confidenceVolatilityFactor - extremenessFactor;

    debugPrint('Prediction parameters: baseValue=$baseValue, volatility=$volatility, trendStrength=$trendStrength');
    debugPrint('Prediction factors: trendVolatilityFactor=$trendVolatilityFactor, maxChangePercent=$maxChangePercent, confidenceBase=$adjustedConfidenceBase');

    for (int i = 1; i <= daysAhead; i++) {
      // Non-linear trend that accelerates/decelerates based on day
      // This creates more realistic predictions than linear trends
      final dayFactor = i / daysAhead;
      final trendCurve = 1 - pow(1 - dayFactor, 2); // Quadratic ease-out

      // Calculate change percentage with some randomness
      final changePercent = trendCurve * maxChangePercent * trendStrength;

      // Create a deterministic random generator for noise
      // Use a different seed for noise to avoid correlation with confidence
      final noiseSeed = (dayFactor * 1000 + trendStrength * 100 + i * 10).round();
      final noiseRandom = Random(noiseSeed);

      // Add volatility-based random noise that increases with prediction distance
      final noiseRange = volatility * (i / 3.0).clamp(0.5, 2.0);
      final noise = (noiseRandom.nextDouble() * 2 - 1) * noiseRange;

      // Calculate the predicted value
      final value = baseValue * (1 + changePercent / 100) + noise;

      // Log the prediction components for debugging
      debugPrint('Day $i prediction: baseValue=$baseValue, changePercent=$changePercent, noise=$noise, final=${value.clamp(0.0, 100.0)}');

      // Calculate confidence that decreases with prediction distance
      // Use a non-linear decay function for more realistic confidence drop-off
      // Confidence drops more rapidly for further predictions
      final distanceFactor = i / daysAhead;
      final distanceDecay = pow(1 - distanceFactor, 1.5);

      // Calculate day-specific confidence with more sophisticated formula
      final dayConfidence = adjustedConfidenceBase * (0.7 + 0.3 * distanceDecay);

      // Add small random variation for more realistic confidence values
      // Use deterministic seed for stable predictions
      final confidenceSeed = (dayFactor * 1000 + trendStrength * 100).round();
      final random = Random(confidenceSeed);
      final randomVariation = (random.nextDouble() * 0.06) - 0.03; // ±3% variation

      // Final confidence with small random variation, clamped to valid range
      final finalDayConfidence = (dayConfidence + randomVariation).clamp(0.3, 0.95);

      // Create metrics map with prediction metadata
      final metrics = <String, double>{
        'confidence': (finalDayConfidence * 100).clamp(30.0, 90.0),
        'volatility': volatility,
        'trend_strength': trendStrength,
      };

      predictions.add(SentimentHistoryEntry(
        date: today.add(Duration(days: i)),
        value: value.clamp(0.0, 100.0),
        metrics: metrics,
      ));
    }

    return predictions;
  }



  /// Generate default predictions when historical data is insufficient
  /// Uses the enhanced prediction algorithm with default values
  List<SentimentHistoryEntry> _generateDefaultPredictions(int daysAhead) {
    // Use the enhanced prediction algorithm with default values
    // Base value of 50 (neutral), low volatility, and slight uptrend
    return _generateEnhancedTrendPredictions(
      daysAhead,
      50.0, // Default neutral value
      3.0,  // Low default volatility
      uptrend: true, // Slight uptrend by default
    );
  }

  /// Clear all historical data (for testing)
  Future<void> clearHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_historicalDataKey);
      debugPrint('Cleared all historical data');
    } catch (e) {
      debugPrint('Error clearing history: $e');
    }
  }

  /// Predict future sentiment values using ML-based linear regression
  /// This method uses the ml_linalg package for more accurate predictions
  Future<List<SentimentHistoryEntry>> predictFutureSentimentML(
    int daysAhead,
    [Map<String, double>? currentMetrics]
  ) async {
    // If metrics are provided, check if they've changed
    if (currentMetrics != null) {
      final metricsChanged = await _haveMetricsChanged(currentMetrics);

      // If metrics haven't changed, try to use cached predictions
      if (!metricsChanged) {
        final cachedPredictions = await _getCachedPredictions();
        if (cachedPredictions != null && cachedPredictions.length >= daysAhead) {
          debugPrint('Using cached predictions as metrics have not changed');
          return cachedPredictions.take(daysAhead).toList();
        }
      }
    }

    final history = await getHistoricalData();

    debugPrint('Generating new ML-based predictions for the next $daysAhead days');
    debugPrint('Historical data points available: ${history.entries.length}');

    // Need at least 2 data points for ML-based prediction
    if (history.entries.length < 2) {
      debugPrint('Not enough historical data for ML prediction, using default values');
      final predictions = _generateDefaultPredictions(daysAhead);

      // Cache the predictions if metrics are provided
      if (currentMetrics != null) {
        await _savePredictions(predictions, currentMetrics);
      }

      return predictions;
    }

    try {
      // Sort entries by date (newest first)
      history.entries.sort((a, b) => b.date.compareTo(a.date));

      // Extract historical values (newest first)
      final historicalValues = history.entries.map((entry) => entry.value).toList();

      // Reverse the list to have oldest first (required for prediction)
      final orderedValues = historicalValues.reversed.toList();

      // Use ML-based linear regression to predict future values
      final predictedValues = await MLLinearRegression.predictFutureValues(
        orderedValues,
        daysAhead
      );

      debugPrint('ML prediction results: $predictedValues');

      // Create prediction entries
      final predictions = <SentimentHistoryEntry>[];
      final today = DateTime.now();

      // Calculate confidence based on the number of historical data points
      // More data points = higher confidence
      final baseConfidence = (history.entries.length / 30).clamp(0.5, 0.9);

      for (int i = 0; i < daysAhead; i++) {
        final futureDate = today.add(Duration(days: i + 1));
        final predictedValue = predictedValues[i];

        // Confidence decreases with prediction distance
        final distanceFactor = (i + 1) / daysAhead;
        final dayConfidence = baseConfidence * (1 - distanceFactor * 0.3);

        // Create metrics map with prediction metadata
        final metrics = <String, double>{
          'confidence': (dayConfidence * 100).clamp(30.0, 90.0),
          'ml_prediction': 1.0, // Flag to indicate this was an ML-based prediction
        };

        predictions.add(SentimentHistoryEntry(
          date: futureDate,
          value: predictedValue,
          metrics: metrics,
        ));
      }

      // Cache the predictions if metrics are provided
      if (currentMetrics != null) {
        await _savePredictions(predictions, currentMetrics);
      }

      return predictions;
    } catch (e) {
      debugPrint('Error predicting future sentiment with ML: $e');

      // Fallback to the original prediction method
      debugPrint('Falling back to original prediction method');
      return await predictFutureSentiment(daysAhead, currentMetrics);
    }
  }
}
