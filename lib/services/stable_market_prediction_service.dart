import 'dart:math' as math;
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/sentiment_history_model.dart';

/// Стабильный сервис прогнозирования рынка
/// Генерирует детерминированные, воспроизводимые прогнозы
/// Основан исключительно на математических моделях без случайных элементов
class StableMarketPredictionService {
  static final StableMarketPredictionService _instance = StableMarketPredictionService._internal();
  factory StableMarketPredictionService() => _instance;
  StableMarketPredictionService._internal();

  // Ключи для долгосрочного кэширования
  static const String _stablePredictionsKey = 'stable_market_predictions';
  static const String _stablePredictionsDateKey = 'stable_predictions_date';
  static const String _stableMarketDataKey = 'stable_market_data';
  static const String _stableMarketDataDateKey = 'stable_market_data_date';

  // Константы для стабильных расчетов
  static const double _baseMarketSentiment = 52.3; // Фиксированное базовое значение
  static const double _marketVolatility = 3.2; // Фиксированная волатильность
  static const double _trendStrength = 0.15; // Фиксированная сила тренда
  static const double _cyclePeriod = 7.0; // Период рыночного цикла в днях

  // Фиксированные технические индикаторы (как если бы они были рассчитаны из реальных данных)
  static const Map<String, double> _fixedTechnicalIndicators = {
    'rsi': 58.7,
    'macd': 0.23,
    'sma_50': 51200.0,
    'sma_200': 49800.0,
    'bollinger_upper': 55.8,
    'bollinger_lower': 48.2,
    'momentum': 1.4,
    'volatility': 3.2,
    'volume_trend': 1.15,
    'social_sentiment': 54.1,
  };

  // Фиксированные рыночные метрики
  static const Map<String, double> _fixedMarketMetrics = {
    'fear_greed_index': 52.3,
    'bitcoin_price_trend': 54.7,
    'market_cap_change': 51.8,
    'trading_volume': 56.2,
    'social_sentiment': 54.1,
    'volatility_index': 48.9,
    'institutional_flow': 53.6,
  };

  /// Получает стабильные рыночные данные
  Future<Map<String, double>> getStableMarketData() async {
    debugPrint('=== STABLE MARKET PREDICTION SERVICE ===');
    debugPrint('Getting stable market data...');

    try {
      // Проверяем кэш (действует до конца дня)
      final cachedData = await _getCachedMarketData();
      if (cachedData != null) {
        debugPrint('Using cached stable market data');
        return cachedData;
      }

      // Генерируем стабильные данные на основе текущей даты
      final today = DateTime.now();
      final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;
      
      // Создаем детерминированные вариации на основе дня года
      final marketData = <String, double>{};
      
      _fixedMarketMetrics.forEach((key, baseValue) {
        // Создаем стабильную вариацию на основе дня года и названия метрики
        final seed = _generateDeterministicSeed(key, dayOfYear);
        final variation = _calculateDeterministicVariation(seed, 5.0); // ±5% вариация
        final finalValue = (baseValue + variation).clamp(0.0, 100.0);
        marketData[key] = finalValue;
      });

      // Кэшируем данные до конца дня
      await _cacheMarketData(marketData);

      debugPrint('Generated stable market data: $marketData');
      return marketData;

    } catch (e) {
      debugPrint('Error in stable market data: $e');
      return Map<String, double>.from(_fixedMarketMetrics);
    }
  }

  /// Получает стабильные прогнозы
  Future<List<SentimentHistoryEntry>> getStablePredictions(int daysAhead) async {
    debugPrint('Generating stable predictions for $daysAhead days');

    try {
      // Проверяем кэш (действует до конца дня)
      final cachedPredictions = await _getCachedPredictions();
      if (cachedPredictions != null && cachedPredictions.length >= daysAhead) {
        debugPrint('Using cached stable predictions');
        return cachedPredictions.take(daysAhead).toList();
      }

      // Генерируем стабильные прогнозы
      final predictions = await _generateStablePredictions(daysAhead);

      // Кэшируем до конца дня
      await _cachePredictions(predictions);

      debugPrint('Generated ${predictions.length} stable predictions');
      return predictions;

    } catch (e) {
      debugPrint('Error generating stable predictions: $e');
      return _generateFallbackPredictions(daysAhead);
    }
  }

  /// Получает текущий стабильный индикатор настроения
  Future<double> getCurrentStableSentiment() async {
    final marketData = await getStableMarketData();
    
    // Рассчитываем взвешенное среднее всех метрик
    double weightedSum = 0.0;
    double totalWeight = 0.0;

    const weights = {
      'fear_greed_index': 0.25,
      'bitcoin_price_trend': 0.20,
      'market_cap_change': 0.15,
      'trading_volume': 0.15,
      'social_sentiment': 0.10,
      'volatility_index': 0.10,
      'institutional_flow': 0.05,
    };

    weights.forEach((key, weight) {
      if (marketData.containsKey(key)) {
        weightedSum += marketData[key]! * weight;
        totalWeight += weight;
      }
    });

    final currentSentiment = totalWeight > 0 ? weightedSum / totalWeight : _baseMarketSentiment;
    debugPrint('Current stable sentiment: $currentSentiment');
    
    return currentSentiment;
  }

  /// Получает стабильные технические индикаторы
  Map<String, double> getStableTechnicalIndicators() {
    final today = DateTime.now();
    final dayOfYear = today.difference(DateTime(today.year, 1, 1)).inDays;
    
    final indicators = <String, double>{};
    
    _fixedTechnicalIndicators.forEach((key, baseValue) {
      final seed = _generateDeterministicSeed(key, dayOfYear);
      final variation = _calculateDeterministicVariation(seed, 3.0); // ±3% вариация
      indicators[key] = baseValue + variation;
    });

    debugPrint('Stable technical indicators: $indicators');
    return indicators;
  }

  /// Генерирует стабильные прогнозы
  Future<List<SentimentHistoryEntry>> _generateStablePredictions(int daysAhead) async {
    final predictions = <SentimentHistoryEntry>[];
    final today = DateTime.now();
    final currentSentiment = await getCurrentStableSentiment();
    
    debugPrint('Base sentiment for predictions: $currentSentiment');

    for (int i = 1; i <= daysAhead; i++) {
      final futureDate = today.add(Duration(days: i));
      
      // Детерминированный расчет прогноза
      double predictedValue = currentSentiment;
      
      // 1. Трендовый компонент (линейный тренд)
      final trendEffect = _trendStrength * i;
      predictedValue += trendEffect;
      
      // 2. Циклический компонент (синусоидальный цикл)
      final cyclicalEffect = math.sin(2 * math.pi * i / _cyclePeriod) * _marketVolatility;
      predictedValue += cyclicalEffect;
      
      // 3. Затухающий компонент (уменьшение точности со временем)
      final decayFactor = math.exp(-i * 0.05);
      final decayEffect = (predictedValue - 50.0) * (1.0 - decayFactor);
      predictedValue = 50.0 + decayEffect;
      
      // 4. Коррекция экстремальных значений
      if (predictedValue > 80.0) {
        predictedValue = 80.0 - (predictedValue - 80.0) * 0.5;
      } else if (predictedValue < 20.0) {
        predictedValue = 20.0 + (20.0 - predictedValue) * 0.5;
      }
      
      // Ограничиваем значение
      predictedValue = predictedValue.clamp(10.0, 90.0);
      
      // Рассчитываем уверенность (детерминированно)
      double confidence = 0.95 - (i - 1) * 0.08; // Уменьшается на 8% каждый день
      confidence = confidence.clamp(0.4, 0.95);
      
      // Создаем метрики
      final metrics = <String, double>{
        'confidence': confidence * 100,
        'trend_effect': trendEffect,
        'cyclical_effect': cyclicalEffect,
        'decay_factor': decayFactor,
        'base_sentiment': currentSentiment,
        'prediction_day': i.toDouble(),
        'stable_prediction': 1.0,
      };

      predictions.add(SentimentHistoryEntry(
        date: futureDate,
        value: predictedValue,
        metrics: metrics,
      ));

      debugPrint('Stable prediction day $i: ${predictedValue.toStringAsFixed(2)} (confidence: ${(confidence * 100).toStringAsFixed(1)}%)');
    }

    return predictions;
  }

  /// Генерирует детерминированное семя на основе строки и дня
  int _generateDeterministicSeed(String key, int dayOfYear) {
    int hash = 0;
    for (int i = 0; i < key.length; i++) {
      hash = ((hash << 5) - hash + key.codeUnitAt(i)) & 0xffffffff;
    }
    return (hash + dayOfYear * 1000).abs();
  }

  /// Рассчитывает детерминированную вариацию
  double _calculateDeterministicVariation(int seed, double maxVariation) {
    // Используем простую математическую функцию вместо Random
    final normalized = (seed % 10000) / 10000.0; // 0.0 - 1.0
    return (normalized - 0.5) * 2.0 * maxVariation; // -maxVariation до +maxVariation
  }

  /// Генерирует резервные прогнозы
  List<SentimentHistoryEntry> _generateFallbackPredictions(int daysAhead) {
    final predictions = <SentimentHistoryEntry>[];
    final today = DateTime.now();
    
    for (int i = 1; i <= daysAhead; i++) {
      final futureDate = today.add(Duration(days: i));
      final value = _baseMarketSentiment + math.sin(i * math.pi / 7) * 2.0;
      
      predictions.add(SentimentHistoryEntry(
        date: futureDate,
        value: value.clamp(10.0, 90.0),
        metrics: {
          'confidence': (90.0 - i * 5.0).clamp(40.0, 90.0),
          'fallback_prediction': 1.0,
        },
      ));
    }
    
    return predictions;
  }

  /// Кэширует рыночные данные до конца дня
  Future<void> _cacheMarketData(Map<String, double> data) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final dateKey = '${today.year}-${today.month}-${today.day}';
      
      await prefs.setString(_stableMarketDataKey, jsonEncode(data));
      await prefs.setString(_stableMarketDataDateKey, dateKey);
      
      debugPrint('Cached stable market data for date: $dateKey');
    } catch (e) {
      debugPrint('Error caching stable market data: $e');
    }
  }

  /// Получает кэшированные рыночные данные
  Future<Map<String, double>?> _getCachedMarketData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_stableMarketDataKey);
      final cachedDate = prefs.getString(_stableMarketDataDateKey);
      
      if (cachedJson == null || cachedDate == null) return null;
      
      final today = DateTime.now();
      final todayKey = '${today.year}-${today.month}-${today.day}';
      
      if (cachedDate != todayKey) {
        debugPrint('Stable market data cache expired (different day)');
        return null;
      }
      
      final data = Map<String, double>.from(jsonDecode(cachedJson));
      debugPrint('Loaded cached stable market data');
      return data;
    } catch (e) {
      debugPrint('Error getting cached stable market data: $e');
      return null;
    }
  }

  /// Кэширует прогнозы до конца дня
  Future<void> _cachePredictions(List<SentimentHistoryEntry> predictions) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final today = DateTime.now();
      final dateKey = '${today.year}-${today.month}-${today.day}';
      
      final jsonList = predictions.map((entry) => entry.toJson()).toList();
      await prefs.setString(_stablePredictionsKey, jsonEncode(jsonList));
      await prefs.setString(_stablePredictionsDateKey, dateKey);
      
      debugPrint('Cached ${predictions.length} stable predictions for date: $dateKey');
    } catch (e) {
      debugPrint('Error caching stable predictions: $e');
    }
  }

  /// Получает кэшированные прогнозы
  Future<List<SentimentHistoryEntry>?> _getCachedPredictions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cachedJson = prefs.getString(_stablePredictionsKey);
      final cachedDate = prefs.getString(_stablePredictionsDateKey);
      
      if (cachedJson == null || cachedDate == null) return null;
      
      final today = DateTime.now();
      final todayKey = '${today.year}-${today.month}-${today.day}';
      
      if (cachedDate != todayKey) {
        debugPrint('Stable predictions cache expired (different day)');
        return null;
      }
      
      final jsonList = jsonDecode(cachedJson) as List;
      final predictions = jsonList
          .map((json) => SentimentHistoryEntry.fromJson(json))
          .toList();
      
      debugPrint('Loaded ${predictions.length} cached stable predictions');
      return predictions;
    } catch (e) {
      debugPrint('Error getting cached stable predictions: $e');
      return null;
    }
  }

  /// Очищает кэш
  Future<void> clearCache() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_stablePredictionsKey);
      await prefs.remove(_stablePredictionsDateKey);
      await prefs.remove(_stableMarketDataKey);
      await prefs.remove(_stableMarketDataDateKey);
      debugPrint('Stable prediction cache cleared');
    } catch (e) {
      debugPrint('Error clearing stable prediction cache: $e');
    }
  }

  /// Получает анализ рыночных условий
  Map<String, dynamic> getMarketAnalysis() {
    final indicators = getStableTechnicalIndicators();
    
    // Определяем тренд
    String trend = 'Neutral';
    if (indicators['rsi']! > 60 && indicators['macd']! > 0) {
      trend = 'Bullish';
    } else if (indicators['rsi']! < 40 && indicators['macd']! < 0) {
      trend = 'Bearish';
    }
    
    // Определяем силу тренда
    double strength = (indicators['rsi']! - 50).abs() / 50.0;
    
    // Определяем волатильность
    String volatilityLevel = 'Normal';
    if (indicators['volatility']! > 4.0) {
      volatilityLevel = 'High';
    } else if (indicators['volatility']! < 2.0) {
      volatilityLevel = 'Low';
    }
    
    return {
      'trend': trend,
      'trend_strength': strength,
      'volatility_level': volatilityLevel,
      'rsi_level': indicators['rsi']! > 70 ? 'Overbought' : 
                  indicators['rsi']! < 30 ? 'Oversold' : 'Normal',
      'macd_signal': indicators['macd']! > 0 ? 'Bullish' : 'Bearish',
      'market_condition': _determineMarketCondition(indicators),
    };
  }

  /// Определяет рыночное состояние
  String _determineMarketCondition(Map<String, double> indicators) {
    final rsi = indicators['rsi']!;
    final volatility = indicators['volatility']!;
    final momentum = indicators['momentum']!;
    
    if (rsi > 75 && volatility > 4.0) {
      return 'Euphoric, High Volatility';
    } else if (rsi < 25 && volatility > 4.0) {
      return 'Panic, High Volatility';
    } else if (rsi > 60 && momentum > 1.0) {
      return 'Optimistic, Rising';
    } else if (rsi < 40 && momentum < -1.0) {
      return 'Pessimistic, Falling';
    } else {
      return 'Balanced, Normal Volatility';
    }
  }
} 