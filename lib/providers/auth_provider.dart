import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AuthProvider with ChangeNotifier {
  bool _isAuthenticated = false;
  bool get isAuthenticated => _isAuthenticated;

  String _username = '';
  String get username => _username;

  String _email = '';
  String get email => _email;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  String _error = '';
  String get error => _error;

  // Initialize auth state from shared preferences
  Future<void> initAuth() async {
    _setLoading(true);

    try {
      final prefs = await SharedPreferences.getInstance();
      final storedUsername = prefs.getString('username');
      final storedEmail = prefs.getString('email');

      if (storedUsername != null && storedEmail != null) {
        _username = storedUsername;
        _email = storedEmail;
        _isAuthenticated = true;
      }
    } catch (e) {
      _setError('Failed to initialize auth: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Login
  Future<bool> login(String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      // In a real app, this would make an API call to authenticate
      await Future.delayed(const Duration(seconds: 1));

      // Mock validation
      if (email.isEmpty || !email.contains('@')) {
        _setError('Please enter a valid email');
        return false;
      }

      if (password.isEmpty || password.length < 6) {
        _setError('Password must be at least 6 characters');
        return false;
      }

      // Mock successful login
      _isAuthenticated = true;
      _email = email;
      _username = email.split('@')[0]; // Simple username extraction

      // Save to shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('username', _username);
      await prefs.setString('email', _email);

      notifyListeners();
      return true;
    } catch (e) {
      _setError('Login failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Register
  Future<bool> register(String username, String email, String password) async {
    _setLoading(true);
    _clearError();

    try {
      // In a real app, this would make an API call to register
      await Future.delayed(const Duration(seconds: 1));

      // Enhanced validation
      if (username.trim().isEmpty) {
        _setError('Please enter a username');
        return false;
      }

      if (username.trim().length < 2) {
        _setError('Username must be at least 2 characters');
        return false;
      }

      // Enhanced email validation
      final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
      if (email.trim().isEmpty || !emailRegex.hasMatch(email.trim())) {
        _setError('Please enter a valid email address');
        return false;
      }

      // Enhanced password validation
      if (password.isEmpty || password.length < 8) {
        _setError('Password must be at least 8 characters');
        return false;
      }

      if (!RegExp(r'^(?=.*[a-zA-Z])(?=.*\d)').hasMatch(password)) {
        _setError('Password must contain both letters and numbers');
        return false;
      }

      // Mock check for existing email (simulate server validation)
      if (email.toLowerCase() == '<EMAIL>') {
        _setError('This email is already registered');
        return false;
      }

      // Mock successful registration
      _isAuthenticated = true;
      _username = username.trim();
      _email = email.trim().toLowerCase();

      // Save to shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('username', _username);
      await prefs.setString('email', _email);

      notifyListeners();
      return true;
    } catch (e) {
      _setError('Registration failed: ${e.toString()}');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Logout
  Future<void> logout() async {
    _setLoading(true);

    try {
      // Clear shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('username');
      await prefs.remove('email');

      _isAuthenticated = false;
      _username = '';
      _email = '';

      notifyListeners();
    } catch (e) {
      _setError('Logout failed: $e');
    } finally {
      _setLoading(false);
    }
  }

  // Авторизация через Google
  Future<bool> loginWithGoogle() async {
    _setLoading(true);
    _clearError();

    try {
      // В реальном приложении здесь был бы код для авторизации через Google API
      await Future.delayed(const Duration(seconds: 1));

      // Имитация успешной авторизации
      _isAuthenticated = true;
      _email = '<EMAIL>';
      _username = 'Google User';

      // Сохраняем в shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('username', _username);
      await prefs.setString('email', _email);

      notifyListeners();
      return true;
    } catch (e) {
      _setError('Google login failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Авторизация через Facebook
  Future<bool> loginWithFacebook() async {
    _setLoading(true);
    _clearError();

    try {
      // В реальном приложении здесь был бы код для авторизации через Facebook API
      await Future.delayed(const Duration(seconds: 1));

      // Имитация успешной авторизации
      _isAuthenticated = true;
      _email = '<EMAIL>';
      _username = 'Facebook User';

      // Сохраняем в shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('username', _username);
      await prefs.setString('email', _email);

      notifyListeners();
      return true;
    } catch (e) {
      _setError('Facebook login failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Авторизация через LinkedIn
  Future<bool> loginWithLinkedIn() async {
    _setLoading(true);
    _clearError();

    try {
      // В реальном приложении здесь был бы код для авторизации через LinkedIn API
      await Future.delayed(const Duration(seconds: 1));

      // Имитация успешной авторизации
      _isAuthenticated = true;
      _email = '<EMAIL>';
      _username = 'LinkedIn User';

      // Сохраняем в shared preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('username', _username);
      await prefs.setString('email', _email);

      notifyListeners();
      return true;
    } catch (e) {
      _setError('LinkedIn login failed: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Регистрация через Google
  Future<bool> registerWithGoogle() async {
    return loginWithGoogle(); // В данном случае процесс аналогичен авторизации
  }

  // Регистрация через Facebook
  Future<bool> registerWithFacebook() async {
    return loginWithFacebook(); // В данном случае процесс аналогичен авторизации
  }

  // Регистрация через LinkedIn
  Future<bool> registerWithLinkedIn() async {
    return loginWithLinkedIn(); // В данном случае процесс аналогичен авторизации
  }

  // Helper methods
  void _setLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  void _setError(String message) {
    _error = message;
    notifyListeners();
  }

  void _clearError() {
    _error = '';
    notifyListeners();
  }
}
