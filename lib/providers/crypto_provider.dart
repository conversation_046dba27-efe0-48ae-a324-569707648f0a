import 'dart:async';
import 'package:flutter/foundation.dart';
import '../models/crypto_currency.dart';
import '../services/binance_service.dart';
import '../services/coingecko_service.dart';
import '../services/favorites_service.dart';

class CryptoProvider with ChangeNotifier {
  final BinanceService _binanceService = BinanceService();
  final CoinGeckoService _coinGeckoService = CoinGeckoService();
  final FavoritesService _favoritesService = FavoritesService();

  List<CryptoCurrency> _allCryptos = [];
  List<CryptoCurrency> get allCryptos => _allCryptos;

  List<CryptoCurrency> _trendingCryptos = [];
  List<CryptoCurrency> get trendingCryptos => _trendingCryptos;

  List<CryptoCurrency> _topChangeCryptos = [];
  List<CryptoCurrency> get topChangeCryptos => _topChangeCryptos;

  List<CryptoCurrency> _favoritesCryptos = [];
  List<CryptoCurrency> get favoritesCryptos => _favoritesCryptos;

  List<CryptoCurrency> _aiAgentsCryptos = [];
  List<CryptoCurrency> get aiAgentsCryptos => _aiAgentsCryptos;

  List<CryptoCurrency> _memesCryptos = [];
  List<CryptoCurrency> get memesCryptos => _memesCryptos;

  bool _isLoading = false;
  bool get isLoading => _isLoading;

  String _error = '';
  String get error => _error;

  Timer? _refreshTimer;

  // Время последнего обновления мини-графиков
  DateTime _lastChartUpdateTime = DateTime.now();

  // Initialize the provider and start periodic updates
  Future<void> initialize() async {
    await loadAllCryptos();

    // Set up a timer to refresh data every minute
    _refreshTimer = Timer.periodic(const Duration(minutes: 1), (_) {
      refreshPrices();
    });
  }

  // Load all cryptocurrency data (optimized version)
  Future<void> loadAllCryptos() async {
    if (_isLoading) return;

    _isLoading = true;
    _error = '';
    notifyListeners();

    try {
      // Загружаем данные параллельно для ускорения
      final futures = await Future.wait([
        _coinGeckoService.getTopCoins(limit: 250), // Увеличиваем лимит до 250 для большего количества токенов
        _coinGeckoService.getTrendingCoins(),
        _binanceService.getTopCryptos(),
        _coinGeckoService.getCoinsByCategory('AI Agents'),
        _coinGeckoService.getCoinsByCategory('Memes'),
      ]);

      // Получаем результаты
      final coinGeckoTopCoins = futures[0] as List<CryptoCurrency>;
      final coinGeckoTrendingCoins = futures[1] as List<CryptoCurrency>;
      final binanceTopCoins = futures[2] as List<CryptoCurrency>;
      _aiAgentsCryptos = futures[3] as List<CryptoCurrency>;
      _memesCryptos = futures[4] as List<CryptoCurrency>;

      // Объединяем данные из разных источников
      final Map<String, CryptoCurrency> combinedCoins = {};

      // Создаем набор для отслеживания уже добавленных символов, чтобы избежать дублирования
      final Set<String> addedSymbols = {};

      // Сначала добавляем монеты из CoinGecko (они имеют лучшие изображения)
      for (var coin in coinGeckoTopCoins) {
        if (!addedSymbols.contains(coin.symbol)) {
          combinedCoins[coin.id] = coin;
          addedSymbols.add(coin.symbol);
        }
      }

      // Добавляем трендовые монеты из CoinGecko
      for (var coin in coinGeckoTrendingCoins) {
        if (!addedSymbols.contains(coin.symbol) && !combinedCoins.containsKey(coin.id)) {
          combinedCoins[coin.id] = coin;
          addedSymbols.add(coin.symbol);
        }
      }

      // Добавляем монеты из категорий AI Agents
      for (var coin in _aiAgentsCryptos) {
        // Всегда добавляем монеты из категорий, даже если символ уже существует
        // Это обеспечит отображение токенов из категорий в общем списке
        combinedCoins[coin.id] = coin;
        // Но не добавляем в addedSymbols, чтобы не блокировать добавление других монет с тем же символом из других категорий
      }

      // Добавляем монеты из категорий Memes
      for (var coin in _memesCryptos) {
        // Всегда добавляем монеты из категорий, даже если символ уже существует
        combinedCoins[coin.id] = coin;
      }

      // Добавляем монеты из Binance только если их символ еще не добавлен
      for (var coin in binanceTopCoins) {
        if (!addedSymbols.contains(coin.symbol)) {
          final coinId = coin.id.toLowerCase();
          combinedCoins[coinId] = coin;
          addedSymbols.add(coin.symbol);
        } else if (combinedCoins.containsKey(coin.id.toLowerCase())) {
          // Если монета уже есть в списке, но у нее нет изображения, обновляем изображение
          final existingCoin = combinedCoins[coin.id.toLowerCase()]!;
          if (existingCoin.imageUrl.contains('placeholder') && !coin.imageUrl.contains('placeholder')) {
            combinedCoins[coin.id.toLowerCase()] = CryptoCurrency(
              id: existingCoin.id,
              name: existingCoin.name,
              symbol: existingCoin.symbol,
              price: existingCoin.price,
              priceChangePercentage24h: existingCoin.priceChangePercentage24h,
              marketCap: existingCoin.marketCap,
              volume24h: existingCoin.volume24h,
              imageUrl: coin.imageUrl,
              isFavorite: existingCoin.isFavorite,
              priceHistory: existingCoin.priceHistory,
              categories: existingCoin.categories,
            );
          }
        }
      }

      // Преобразуем Map в List
      _allCryptos = combinedCoins.values.toList();

      // Обновляем статус избранного для каждой монеты
      _updateFavoriteStatus();

      // Сортируем и фильтруем криптовалюты
      _sortAndFilterCryptos();

      _isLoading = false;
      notifyListeners();
    } catch (e) {
      // В случае ошибки пробуем загрузить данные только из Binance
      try {
        _allCryptos = await _binanceService.getTopCryptos();
        _updateFavoriteStatus();
        _sortAndFilterCryptos();
      } catch (e2) {
        _error = 'Failed to load cryptocurrencies: $e2';
      }

      _isLoading = false;
      notifyListeners();
    }
  }

  // Обновление статуса избранного для всех монет
  void _updateFavoriteStatus() {
    for (int i = 0; i < _allCryptos.length; i++) {
      final crypto = _allCryptos[i];
      final isFavorite = _favoritesService.isFavorite(crypto.id);

      if (crypto.isFavorite != isFavorite) {
        _allCryptos[i] = CryptoCurrency(
          id: crypto.id,
          name: crypto.name,
          symbol: crypto.symbol,
          price: crypto.price,
          priceChangePercentage24h: crypto.priceChangePercentage24h,
          marketCap: crypto.marketCap,
          volume24h: crypto.volume24h,
          imageUrl: crypto.imageUrl,
          isFavorite: isFavorite,
          priceHistory: crypto.priceHistory,
        );
      }
    }
  }

  // Refresh only the prices without reloading all data (optimized version)
  Future<void> refreshPrices() async {
    if (_allCryptos.isEmpty) {
      await loadAllCryptos();
      return;
    }

    try {
      final prices = await _binanceService.getAllPrices();

      if (prices.isNotEmpty) {
        // Update prices for existing cryptocurrencies
        for (int i = 0; i < _allCryptos.length; i++) {
          final crypto = _allCryptos[i];
          final symbol = '${crypto.symbol}USDT';

          if (prices.containsKey(symbol)) {
            // Получаем новую цену из API
            final newPrice = prices[symbol]!;

            // Для процентного изменения используем существующее значение из объекта
            // Не обновляем процентное изменение при каждом обновлении цены
            // Это обеспечит стабильное отображение суточного изменения

            // Обновляем историю цен, добавляя новую цену в начало
            final List<PricePoint> updatedHistory = List.from(crypto.priceHistory);

            // Добавляем новую точку цены в начало истории для часового графика
            // Но делаем это только каждые 15 минут, чтобы соответствовать требованиям
            final DateTime now = DateTime.now();

            // Проверяем, прошло ли 15 минут с момента последнего обновления мини-графиков
            final bool shouldUpdateCharts = now.difference(_lastChartUpdateTime).inMinutes >= 15;

            // Обновляем историю только если прошло 15 минут или история пуста
            final bool shouldAddHistoryPoint = updatedHistory.isEmpty || shouldUpdateCharts;

            if (shouldAddHistoryPoint) {
              updatedHistory.insert(0, PricePoint(now, newPrice));

              // Ограничиваем количество точек для оптимизации производительности
              if (updatedHistory.length > 100) {
                updatedHistory.removeLast();
              }

              // Обновляем время последнего обновления мини-графиков
              if (shouldUpdateCharts) {
                _lastChartUpdateTime = now;
              }
            }

            // Create updated cryptocurrency object
            _allCryptos[i] = CryptoCurrency(
              id: crypto.id,
              name: crypto.name,
              symbol: crypto.symbol,
              price: newPrice,
              priceChangePercentage24h: crypto.priceChangePercentage24h,
              marketCap: crypto.marketCap,
              volume24h: crypto.volume24h,
              imageUrl: crypto.imageUrl,
              isFavorite: crypto.isFavorite,
              priceHistory: updatedHistory,
            );
          }
        }

        _sortAndFilterCryptos();
        notifyListeners();
      }
    } catch (e) {
      // Don't update error state for background refreshes
    }
  }

  // Sort and filter cryptocurrencies for different categories
  void _sortAndFilterCryptos() {
    // Фильтруем стейблкоины
    _allCryptos = _filterStablecoins(_allCryptos);

    // Сортируем основной список по рыночной капитализации
    _allCryptos.sort((a, b) => b.marketCap.compareTo(a.marketCap));

    // Обновляем список избранных криптовалют
    _favoritesCryptos = _allCryptos.where((crypto) => crypto.isFavorite).toList();

    // Обновляем список трендовых криптовалют
    // Сначала ищем криптовалюты с категорией "Trending"
    final trendingFromCategory = _allCryptos.where((crypto) =>
      crypto.categories.contains('Trending')).toList();

    // Если нет криптовалют с категорией "Trending", используем криптовалюты с наибольшим положительным ростом
    if (trendingFromCategory.isEmpty) {
      _trendingCryptos = List.from(_allCryptos)
        ..sort((a, b) => b.priceChangePercentage24h.compareTo(a.priceChangePercentage24h));
      _trendingCryptos = _trendingCryptos.take(20).toList();
    } else {
      _trendingCryptos = trendingFromCategory;
    }

    // Обновляем список криптовалют с наибольшим изменением (по модулю)
    _topChangeCryptos = List.from(_allCryptos)
      ..sort((a, b) => b.priceChangePercentage24h.abs().compareTo(a.priceChangePercentage24h.abs()));
    _topChangeCryptos = _topChangeCryptos.take(20).toList();

    // Обновляем списки для категорий AI Agents и Memes
    // Если списки уже заполнены, не обновляем их
    if (_aiAgentsCryptos.isEmpty) {
      _aiAgentsCryptos = _allCryptos.where((crypto) =>
        crypto.categories.contains('AI Agents')).toList();
    }

    if (_memesCryptos.isEmpty) {
      _memesCryptos = _allCryptos.where((crypto) =>
        crypto.categories.contains('Memes')).toList();
    }
  }

  // Get a specific cryptocurrency by symbol
  CryptoCurrency? getCryptoBySymbol(String symbol) {
    try {
      return _allCryptos.firstWhere((crypto) => crypto.symbol == symbol);
    } catch (e) {
      return null;
    }
  }

  // Get a specific cryptocurrency by id
  CryptoCurrency? getCryptoById(String id) {
    try {
      return _allCryptos.firstWhere((crypto) => crypto.id == id);
    } catch (e) {
      return null;
    }
  }

  // Фильтрация стейблкоинов
  List<CryptoCurrency> _filterStablecoins(List<CryptoCurrency> cryptos) {
    // Список известных стейблкоинов (символы)
    final stablecoins = [
      'USDT', 'USDC', 'BUSD', 'DAI', 'TUSD', 'UST', 'USDP', 'GUSD', 'USDD', 'FRAX',
      'LUSD', 'USDN', 'USDJ', 'SUSD', 'EURS', 'EURT', 'XAUT', 'PAX', 'HUSD', 'OUSD'
    ];

    // Фильтруем криптовалюты, исключая стейблкоины
    return cryptos.where((crypto) =>
      !stablecoins.contains(crypto.symbol) &&
      !crypto.name.toLowerCase().contains('usd') &&
      !crypto.name.toLowerCase().contains('stablecoin') &&
      // Проверяем, что изменение цены не слишком маленькое (характерно для стейблкоинов)
      crypto.priceChangePercentage24h.abs() > 0.1
    ).toList();
  }

  // Toggle favorite status for a cryptocurrency
  Future<void> toggleFavorite(String id) async {
    final isFavorite = await _favoritesService.toggleFavorite(id);

    // Обновляем статус избранного в списке криптовалют
    final index = _allCryptos.indexWhere((crypto) => crypto.id == id);
    if (index != -1) {
      final crypto = _allCryptos[index];
      _allCryptos[index] = CryptoCurrency(
        id: crypto.id,
        name: crypto.name,
        symbol: crypto.symbol,
        price: crypto.price,
        priceChangePercentage24h: crypto.priceChangePercentage24h,
        marketCap: crypto.marketCap,
        volume24h: crypto.volume24h,
        imageUrl: crypto.imageUrl,
        isFavorite: isFavorite,
        priceHistory: crypto.priceHistory,
      );

      // Обновляем отфильтрованные списки
      _sortAndFilterCryptos();
      notifyListeners();
    }
  }

  // Add a cryptocurrency to favorites
  Future<void> addToFavorites(String id) async {
    await _favoritesService.addFavorite(id);
    _updateFavoriteStatus();
    _sortAndFilterCryptos();
    notifyListeners();
  }

  // Remove a cryptocurrency from favorites
  Future<void> removeFromFavorites(String id) async {
    await _favoritesService.removeFavorite(id);
    _updateFavoriteStatus();
    _sortAndFilterCryptos();
    notifyListeners();
  }

  @override
  void dispose() {
    _refreshTimer?.cancel();
    super.dispose();
  }
}
