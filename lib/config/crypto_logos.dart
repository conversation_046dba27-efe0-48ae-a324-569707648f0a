// Константы URL логотипов криптовалют
class CryptoLogos {
  // Базовый URL для логотипов CoinGecko
  static const String _baseUrl = 'https://assets.coingecko.com/coins/images/';
  
  // Маппинг символов криптовалют на URL логотипов
  static final Map<String, String> logoUrls = {
    'BTC': '$_baseUrl/1/large/bitcoin.png',
    'ETH': '$_baseUrl/279/large/ethereum.png',
    'BNB': '$_baseUrl/825/large/bnb-icon2_2x.png',
    'SOL': '$_baseUrl/4128/large/solana.png',
    'ADA': '$_baseUrl/2010/large/cardano.png',
    'XRP': '$_baseUrl/44/large/xrp-symbol-white-128.png',
    'DOT': '$_baseUrl/12171/large/polkadot.png',
    'DOGE': '$_baseUrl/5/large/dogecoin.png',
    'AVAX': '$_baseUrl/12559/large/avalanche-avax-logo.png',
    'SHIB': '$_baseUrl/11939/large/shiba.png',
    'MATIC': '$_baseUrl/4713/large/matic-token.png',
    'LTC': '$_baseUrl/2/large/litecoin.png',
    'LINK': '$_baseUrl/877/large/chainlink.png',
    'UNI': '$_baseUrl/12504/large/uniswap.png',
    'ATOM': '$_baseUrl/1481/large/cosmos_hub.png',
    'XLM': '$_baseUrl/100/large/stellar.png',
    'ALGO': '$_baseUrl/4030/large/algorand.png',
    'FIL': '$_baseUrl/12817/large/filecoin.png',
    'NEAR': '$_baseUrl/10365/large/near.png',
    'ICP': '$_baseUrl/14495/large/icp.png',
    'VET': '$_baseUrl/1167/large/VeChain-Logo-768x725.png',
    'MANA': '$_baseUrl/878/large/decentraland.png',
    'SAND': '$_baseUrl/12129/large/sandbox_logo.jpg',
    'AXS': '$_baseUrl/13029/large/axie_infinity_logo.png',
    'GALA': '$_baseUrl/12493/large/GALA-COINGECKO.png',
    'ENJ': '$_baseUrl/1102/large/enjin-coin-logo.png',
    'CHZ': '$_baseUrl/4066/large/chiliz.png',
    'ONE': '$_baseUrl/3945/large/harmony.png',
    'HOT': '$_baseUrl/2682/large/holo.png',
    'ZIL': '$_baseUrl/2687/large/zilliqa-logo.png',
    'NEO': '$_baseUrl/480/large/neo.jpg',
    'BCC': '$_baseUrl/1831/large/bitcoin-cash-circle.png',
    'QTUM': '$_baseUrl/1714/large/qtum.png',
    'EOS': '$_baseUrl/738/large/eos-eos-logo.png',
    'TUSD': '$_baseUrl/3449/large/tusd.png',
    'IOTA': '$_baseUrl/1720/large/iota.png',
    'ONT': '$_baseUrl/2566/large/ont.jpg',
    'TRX': '$_baseUrl/1094/large/tron.png',
  };
  
  // Метод для получения URL логотипа по символу
  static String getLogoUrl(String symbol) {
    // Приводим символ к верхнему регистру для соответствия ключам в маппинге
    final upperSymbol = symbol.trim().toUpperCase();
    
    // Проверяем, есть ли символ в маппинге
    if (logoUrls.containsKey(upperSymbol)) {
      return logoUrls[upperSymbol]!;
    }
    
    // Если символа нет в маппинге, возвращаем заглушку
    return 'https://via.placeholder.com/50?text=$symbol';
  }
}
