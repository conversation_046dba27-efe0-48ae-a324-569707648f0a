import 'package:flutter_dotenv/flutter_dotenv.dart';

class EnvConfig {
  // Загрузка переменных окружения
  static Future<void> load() async {
    try {
      await dotenv.load(fileName: '.env');
    } catch (e) {
      print('Warning: Could not load .env file: $e');
      // Продолжаем выполнение без .env файла
    }
  }

  // Получение API ключа для News API
  static String get newsApiKey {
    return dotenv.env['NEWS_API_KEY'] ?? '';
  }
  
  // Получение API ключа для CryptoCompare
  static String get cryptoCompareApiKey {
    return dotenv.env['CRYPTOCOMPARE_API_KEY'] ?? '';
  }
  
  // Добавьте другие переменные окружения по мере необходимости
}
