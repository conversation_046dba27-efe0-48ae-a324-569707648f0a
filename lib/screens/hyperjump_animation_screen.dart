import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui';
import 'dart:async';

class HyperjumpAnimationScreen extends StatefulWidget {
  const HyperjumpAnimationScreen({Key? key}) : super(key: key);

  @override
  State<HyperjumpAnimationScreen> createState() => _HyperjumpAnimationScreenState();
}

class _HyperjumpAnimationScreenState extends State<HyperjumpAnimationScreen> with TickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  final List<Star> _stars = [];



  // Контроллер для вращения изображений
  late AnimationController _rotationController;
  late Animation<double> _rotationAnimation;

  // Изображение для анимации гиперпрыжка
  final String _mainHyperjumpImagePath = 'assets/images/hyperjump_background.png';

  late ImageProvider _mainHyperjumpImage;
  bool _mainImageLoaded = false;

  @override
  void initState() {
    super.initState();

    // Загружаем основное изображение гиперпрыжка
    _mainHyperjumpImage = AssetImage(_mainHyperjumpImagePath);
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Устанавливаем флаг загрузки изображения в true, чтобы не пытаться загружать файл-заглушку
    _mainImageLoaded = true;

    // Настраиваем контроллер для основной анимации гиперпрыжка
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 4500), // 4.5 секунд
    );

    // Используем более сложную кривую для лучшего контроля над этапами анимации
    _animation = CurvedAnimation(
      parent: _controller,
      // Начинается медленно, затем ускоряется, и в конце снова замедляется
      curve: Curves.easeInOutCubic,
    );



    // Настраиваем контроллер для вращения изображений
    _rotationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 10000), // 10 секунд для полного оборота
    );

    // Анимация вращения от 0 до 2π (полный оборот)
    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2 * math.pi,
    ).animate(CurvedAnimation(
      parent: _rotationController,
      curve: Curves.linear,
    ));

    // Запускаем анимацию с небольшой задержкой
    Future.delayed(const Duration(milliseconds: 300), () {
      _controller.forward();
      _rotationController.repeat(); // Зацикливаем анимацию вращения
    });

    // После завершения анимации переходим на главную страницу
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            Navigator.pushReplacementNamed(context, '/news');
          }
        });
      }
    });

    // Создаем больше звезд для более насыщенной анимации
    for (int i = 0; i < 500; i++) {
      // Распределяем звезды равномерно по всем направлениям от центра
      final angle = math.Random().nextDouble() * 2 * math.pi;

      // Используем разные радиусы для создания эффекта глубины
      // Большинство звезд ближе к центру для лучшего эффекта ускорения
      double radius;
      if (i < 300) {
        radius = math.Random().nextDouble() * 0.4 + 0.1; // от 0.1 до 0.5 (ближе к центру)
      } else {
        radius = math.Random().nextDouble() * 0.3 + 0.5; // от 0.5 до 0.8 (дальше от центра)
      }

      // Добавляем цвет для звезд, чтобы они сочетались с синим фоном гиперпрыжка
      final starColor = math.Random().nextDouble() > 0.7
          ? Colors.blue.shade300 // 30% звезд будут голубыми
          : Colors.white; // 70% звезд будут белыми

      _stars.add(Star(
        x: math.cos(angle) * radius, // координата x на единичной окружности
        y: math.sin(angle) * radius, // координата y на единичной окружности
        size: math.Random().nextDouble() * 2.5 + 1.0, // Немного увеличиваем размер звезд
        brightness: math.Random().nextDouble() * 0.5 + 0.5, // Случайная яркость для разнообразия
        color: starColor, // Добавляем цвет звезды
      ));
    }


  }

  @override
  void dispose() {
    _controller.dispose();
    _rotationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Основное фоновое изображение гиперпрыжка
          if (_mainImageLoaded)
            Positioned.fill(
              child: Opacity(
                opacity: _animation.value < 0.3
                    ? _animation.value / 0.3
                    : _animation.value > 0.8
                        ? 1.0 - ((_animation.value - 0.8) / 0.2)
                        : 1.0,
                child: Image(
                  image: _mainHyperjumpImage,
                  fit: BoxFit.cover,
                ),
              ),
            ),

          // Полностью черный слой для скрытия надписи на изображениях
          Positioned.fill(
            child: Container(
              color: Colors.black, // Полностью непрозрачный черный цвет
            ),
          ),



          // Анимация звезд
          AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return CustomPaint(
                size: MediaQuery.of(context).size,
                painter: EnhancedHyperjumpPainter(_stars, _animation.value),
              );
            },
          ),

          // Индикатор загрузки, если изображение еще не загружено
          if (!_mainImageLoaded)
            const Center(
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
        ],
      ),
    );
  }
}

// Класс для представления звезды
class Star {
  final double x;
  final double y;
  final double size;
  final double brightness; // Параметр яркости для разнообразия
  final Color color; // Цвет звезды для соответствия фону гиперпрыжка

  Star({
    required this.x,
    required this.y,
    required this.size,
    this.brightness = 1.0,
    this.color = Colors.white,
  });
}

// Улучшенный кастомный painter для отрисовки анимации гиперпрыжка
class EnhancedHyperjumpPainter extends CustomPainter {
  final List<Star> stars;
  final double animationValue;

  EnhancedHyperjumpPainter(this.stars, this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeCap = StrokeCap.round;

    final center = Offset(size.width / 2, size.height / 2);

    // Определяем этапы анимации
    // Этап 1: Начальное свечение и старт (0.0 - 0.2)
    // Этап 2: Ускорение и удлинение линий (0.2 - 0.5)
    // Этап 3: Максимальная скорость с длинными линиями (0.5 - 0.8)
    // Этап 4: Затухание и переход (0.8 - 1.0)

    // Рисуем яркое пятно в центре в начале анимации (Этап 1)
    // Уменьшаем интенсивность центрального свечения, так как у нас есть фоновое изображение
    if (animationValue < 0.2) {
      final glowOpacity = math.max(0.0, math.min(0.7, 0.7 - (animationValue / 0.2) * 0.5));
      final glowRadius = 50.0 * (1.0 - animationValue);

      final glowPaint = Paint()
        ..color = Colors.white.withOpacity(glowOpacity)
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, glowRadius);

      canvas.drawCircle(center, glowRadius, glowPaint);
    }

    // Рисуем звезды с разными эффектами в зависимости от этапа анимации
    for (var star in stars) {
      // Начальная позиция звезды (близко к центру)
      final startX = center.dx + star.x * 50;
      final startY = center.dy + star.y * 50;

      // Конечная позиция звезды (растянутая к краям экрана и за них)
      // Увеличиваем множитель для более длинных линий
      final endX = center.dx + star.x * size.width * 3.0;
      final endY = center.dy + star.y * size.height * 3.0;

      // Прогресс движения зависит от этапа анимации
      double progress;

      if (animationValue < 0.2) {
        // Этап 1: Медленное начало
        progress = math.pow(animationValue / 0.2, 1.2).toDouble() * 0.2;
      } else if (animationValue < 0.5) {
        // Этап 2: Ускорение
        progress = 0.2 + math.pow((animationValue - 0.2) / 0.3, 1.0).toDouble() * 0.3;
      } else if (animationValue < 0.8) {
        // Этап 3: Максимальная скорость
        progress = 0.5 + math.pow((animationValue - 0.5) / 0.3, 0.8).toDouble() * 0.3;
      } else {
        // Этап 4: Замедление перед переходом
        progress = 0.8 + math.pow((animationValue - 0.8) / 0.2, 0.5).toDouble() * 0.2;
      }

      // Текущая позиция звезды в зависимости от прогресса анимации
      final currentX = lerpDouble(startX, endX, progress)!;
      final currentY = lerpDouble(startY, endY, progress)!;

      // Длина линии зависит от прогресса анимации и этапа
      double lineLength;

      if (animationValue < 0.2) {
        // Этап 1: Короткие линии
        lineLength = size.width * 0.05 * (animationValue / 0.2);
      } else if (animationValue < 0.5) {
        // Этап 2: Линии удлиняются
        lineLength = size.width * (0.05 + 0.25 * ((animationValue - 0.2) / 0.3));
      } else if (animationValue < 0.8) {
        // Этап 3: Максимально длинные линии
        lineLength = size.width * 0.3;
      } else {
        // Этап 4: Линии начинают укорачиваться
        lineLength = size.width * 0.3 * (1.0 - ((animationValue - 0.8) / 0.2) * 0.5);
      }

      // Направление от центра к звезде
      final dx = currentX - center.dx;
      final dy = currentY - center.dy;
      final distance = math.sqrt(dx * dx + dy * dy);

      if (distance < 0.001) continue; // Избегаем деления на ноль

      // Нормализованное направление
      final dirX = dx / distance;
      final dirY = dy / distance;

      // Начальная и конечная точки линии
      final lineStart = Offset(currentX - dirX * lineLength, currentY - dirY * lineLength);
      final lineEnd = Offset(currentX, currentY);

      // Толщина линии зависит от размера звезды, яркости и этапа анимации
      double lineWidth;

      if (animationValue < 0.5) {
        // Этапы 1-2: Линии тоньше
        lineWidth = star.size * (0.8 + progress * 0.7);
      } else {
        // Этапы 3-4: Линии толще
        lineWidth = star.size * (1.5 + (progress - 0.5) * 0.5);
      }

      paint.strokeWidth = lineWidth * star.brightness;

      // Яркость линии зависит от этапа анимации
      double opacity;

      if (animationValue < 0.2) {
        // Этап 1: Нарастание яркости
        opacity = 0.5 + (animationValue / 0.2) * 0.5;
      } else if (animationValue < 0.5) {
        // Этап 2: Высокая яркость
        opacity = 1.0;
      } else if (animationValue < 0.8) {
        // Этап 3: Максимальная яркость
        opacity = 1.0;
      } else {
        // Этап 4: Постепенное затухание
        opacity = 1.0 - ((animationValue - 0.8) / 0.2) * 0.3;
      }

      // Применяем индивидуальную яркость звезды
      opacity *= star.brightness;

      // Убедимся, что значение opacity находится в диапазоне от 0.0 до 1.0
      opacity = math.max(0.0, math.min(1.0, opacity));

      // Используем цвет звезды вместо белого
      paint.color = star.color.withOpacity(opacity);

      // Рисуем линию
      canvas.drawLine(lineStart, lineEnd, paint);

      // Добавляем небольшую точку в конце линии для более яркого эффекта
      if (animationValue > 0.3 && animationValue < 0.9) {
        canvas.drawCircle(
          lineEnd,
          lineWidth * 0.7,
          Paint()..color = star.color.withOpacity(opacity * 0.8)
        );
      }
    }

    // Затемнение экрана в конце анимации (Этап 4)
    if (animationValue > 0.8) {
      final fadeValue = math.max(0.0, math.min(1.0, (animationValue - 0.8) / 0.2)); // от 0 до 1
      canvas.drawRect(
        Rect.fromLTWH(0, 0, size.width, size.height),
        Paint()..color = Colors.black.withOpacity(fadeValue),
      );
    }

    // Добавляем эффект свечения по краям экрана в середине анимации (Этапы 2-3)
    if (animationValue > 0.2 && animationValue < 0.8) {
      final glowIntensity = math.max(0.0, math.min(1.0,
        animationValue < 0.5
          ? (animationValue - 0.2) / 0.3 // Нарастание
          : 1.0 - (animationValue - 0.5) / 0.3 // Затухание
      ));

      final glowPaint = Paint()
        ..shader = RadialGradient(
          colors: [
            Colors.white.withOpacity(0.0),
            Colors.white.withOpacity(math.max(0.0, math.min(1.0, 0.15 * glowIntensity))),
          ],
          stops: const [0.6, 1.0], // Увеличиваем область свечения
        ).createShader(Rect.fromCircle(center: center, radius: size.width));

      canvas.drawRect(
        Rect.fromLTWH(0, 0, size.width, size.height),
        glowPaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
