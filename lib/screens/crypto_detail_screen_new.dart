import 'package:flutter/material.dart';
import '../models/crypto_currency.dart';
import '../widgets/simple_crypto_chart.dart';
import 'dart:math' as math;

class CryptoDetailScreenNew extends StatefulWidget {
  final CryptoCurrency crypto;

  const CryptoDetailScreenNew({
    super.key,
    required this.crypto,
  });

  @override
  State<CryptoDetailScreenNew> createState() => _CryptoDetailScreenNewState();
}

class _CryptoDetailScreenNewState extends State<CryptoDetailScreenNew> {
  String _selectedTimeframe = '24h';

  @override
  Widget build(BuildContext context) {
    final crypto = widget.crypto;
    final isPositive = crypto.priceChangePercentage24h >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;
    final changeSign = isPositive ? '+' : '';

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(crypto.name),
        backgroundColor: Colors.black,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Основная информация о криптовалюте
            _buildCryptoHeader(crypto, changeColor, changeSign),
            
            // Селектор временного интервала
            _buildTimeframeSelector(),
            
            // График
            _buildChart(crypto),
            
            // Информация о рынке
            _buildMarketInfo(crypto),
            
            // Дополнительная информация
            _buildAdditionalInfo(crypto),
          ],
        ),
      ),
    );
  }

  Widget _buildCryptoHeader(CryptoCurrency crypto, Color changeColor, String changeSign) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Цена и изменение
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '\$${_formatPrice(crypto.price)}',
                style: const TextStyle(
                  fontSize: 28,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: changeColor.withAlpha(51),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  '$changeSign${crypto.priceChangePercentage24h.toStringAsFixed(2)}%',
                  style: TextStyle(
                    color: changeColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          
          // Иконка криптовалюты
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(25),
            ),
            alignment: Alignment.center,
            child: Text(
              crypto.symbol.substring(0, math.min(2, crypto.symbol.length)),
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 18,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeframeSelector() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            _buildTimeframeButton('1h'),
            _buildTimeframeButton('24h'),
            _buildTimeframeButton('7d'),
            _buildTimeframeButton('30d'),
            _buildTimeframeButton('all'),
          ],
        ),
      ),
    );
  }

  Widget _buildChart(CryptoCurrency crypto) {
    return Container(
      height: 300,
      margin: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(16.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(100),
            blurRadius: 10.0,
            spreadRadius: 1.0,
          ),
        ],
      ),
      child: SimpleCryptoChart.fromCrypto(
        crypto: crypto,
        timeframe: _selectedTimeframe,
        showGrid: true,
        showLabels: true,
      ),
    );
  }

  Widget _buildMarketInfo(CryptoCurrency crypto) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Market Information',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildInfoRow('Market Cap', '\$${_formatLargeNumber(crypto.marketCap)}'),
          const SizedBox(height: 8),
          _buildInfoRow('24h Volume', '\$${_formatLargeNumber(crypto.volume24h)}'),
          const SizedBox(height: 8),
          _buildInfoRow('Rank', '#${(crypto.id.hashCode % 100).abs() + 1}'),
          const SizedBox(height: 8),
          _buildInfoRow('Circulating Supply', '${_formatLargeNumber(crypto.marketCap / crypto.price)} ${crypto.symbol}'),
        ],
      ),
    );
  }

  Widget _buildAdditionalInfo(CryptoCurrency crypto) {
    return Container(
      margin: const EdgeInsets.all(16.0),
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(16.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'About',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc eu nisl. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, quis aliquam nisl nunc eu nisl.',
            style: TextStyle(
              color: Colors.grey[300],
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 16),
          const Text(
            'Links',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          _buildLinkRow('Website', 'https://${crypto.symbol.toLowerCase()}.org'),
          const SizedBox(height: 4),
          _buildLinkRow('Explorer', 'https://explorer.${crypto.symbol.toLowerCase()}.org'),
          const SizedBox(height: 4),
          _buildLinkRow('GitHub', 'https://github.com/${crypto.symbol.toLowerCase()}'),
        ],
      ),
    );
  }

  Widget _buildTimeframeButton(String timeframe) {
    final isSelected = _selectedTimeframe == timeframe;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTimeframe = timeframe;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.blue : Colors.grey[800],
          borderRadius: BorderRadius.circular(20),
        ),
        child: Text(
          timeframe.toUpperCase(),
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[400],
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[400],
            fontSize: 14,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildLinkRow(String label, String url) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[400],
            fontSize: 14,
          ),
        ),
        Text(
          url,
          style: const TextStyle(
            color: Colors.blue,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(2);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  String _formatLargeNumber(double value) {
    if (value >= 1000000000) {
      return '${(value / 1000000000).toStringAsFixed(2)}B';
    } else if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(2)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(2)}K';
    } else {
      return value.toStringAsFixed(2);
    }
  }
}
