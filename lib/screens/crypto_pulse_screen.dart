import 'package:flutter/material.dart';
import '../models/sentiment_history_model.dart';
import '../services/market_sentiment_service.dart';
import '../services/sentiment_prediction_service.dart';
import '../widgets/app_bottom_navigation.dart';

/// A redesigned Sinusoid screen with a "Crypto Pulse" theme
class CryptoPulseScreen extends StatefulWidget {
  const CryptoPulseScreen({super.key});

  @override
  State<CryptoPulseScreen> createState() => _CryptoPulseScreenState();
}

class _CryptoPulseScreenState extends State<CryptoPulseScreen> {
  final MarketSentimentService _sentimentService = MarketSentimentService();
  final SentimentPredictionService _predictionService = SentimentPredictionService();

  double _indicatorValue = 50.0; // Default neutral value
  bool _isLoading = true;
  bool _isLoadingHistory = true;
  bool _isLoadingPredictions = true;

  Map<String, double> _metricValues = {
    'Fear & Greed Index': 50.0,
    'News Sentiment': 50.0,
    'Holders Score': 50.0,
    'Volume Score': 50.0,
    'Social Engagement': 50.0,
    'Price Volatility': 50.0,
    'Bitcoin Dominance': 50.0,
  };

  // Historical data
  SentimentHistoryEntry? _yesterdayEntry;
  SentimentHistoryEntry? _lastWeekEntry;

  // Prediction data
  List<SentimentHistoryEntry> _predictions = [];

  @override
  void initState() {
    super.initState();
    _fetchMarketSentiment();
    _fetchHistoricalData();
    _fetchPredictions();
  }

  // Fetch current market sentiment
  Future<void> _fetchMarketSentiment() async {
    debugPrint('Fetching market sentiment...');

    setState(() {
      _isLoading = true;
    });

    try {
      // Get indicator value and metrics
      final indicator = await _sentimentService.calculateMarketSentiment();
      debugPrint('Market sentiment indicator: $indicator');

      // Get individual metrics
      final fearGreedIndex = await _sentimentService.fetchFearGreedIndex();
      final newsSentiment = await _sentimentService.fetchNewsSentiment();
      final holdersScore = await _sentimentService.fetchHoldersScore();
      final volumeScore = await _sentimentService.fetchVolumeScore();
      final socialEngagement = await _sentimentService.fetchSocialEngagement();
      final priceVolatility = await _sentimentService.fetchPriceVolatility();
      final bitcoinDominance = await _sentimentService.fetchBitcoinDominance();

      // Normalize news sentiment from [-1, 1] to [0, 100]
      final normalizedNewsSentiment = (newsSentiment + 1) * 50;

      // Normalize volume score from [0, 2.0] to [0, 100]
      final normalizedVolumeScore = volumeScore.clamp(0.0, 1.0) * 100;

      // Create metrics map
      final metrics = {
        'Fear & Greed Index': fearGreedIndex,
        'News Sentiment': normalizedNewsSentiment,
        'Holders Score': holdersScore,
        'Volume Score': normalizedVolumeScore,
        'Social Engagement': socialEngagement,
        'Price Volatility': priceVolatility,
        'Bitcoin Dominance': bitcoinDominance,
      };

      // Manually save to history to ensure it's saved
      await _sentimentService.saveToHistory(indicator, metrics);
      debugPrint('Saved current data to history');

      // Update state
      if (mounted) {
        setState(() {
          _indicatorValue = indicator;
          _metricValues = metrics;
          _isLoading = false;
        });
      }
    } catch (e) {
      debugPrint('Error fetching market sentiment: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  // Fetch historical sentiment data
  Future<void> _fetchHistoricalData() async {
    debugPrint('Fetching historical sentiment data...');

    setState(() {
      _isLoadingHistory = true;
    });

    try {
      // Get historical data
      final history = await _predictionService.getHistoricalData();
      debugPrint('Retrieved ${history.entries.length} historical entries');

      // Get yesterday's and last week's entries
      final yesterdayEntry = history.getYesterdayEntry();
      final lastWeekEntry = history.getLastWeekEntry();

      debugPrint('Yesterday entry: ${yesterdayEntry?.date} - ${yesterdayEntry?.value}');
      debugPrint('Last week entry: ${lastWeekEntry?.date} - ${lastWeekEntry?.value}');

      if (mounted) {
        setState(() {
          _yesterdayEntry = yesterdayEntry;
          _lastWeekEntry = lastWeekEntry;
          _isLoadingHistory = false;
        });
      }
    } catch (e) {
      debugPrint('Error fetching historical data: $e');
      if (mounted) {
        setState(() {
          _isLoadingHistory = false;
        });
      }
    }
  }

  // Fetch prediction data
  Future<void> _fetchPredictions() async {
    debugPrint('Fetching predictions...');

    setState(() {
      _isLoadingPredictions = true;
    });

    try {
      // Generate simple predictions (in a real app, this would use more sophisticated algorithms)
      final today = DateTime.now();
      final predictions = <SentimentHistoryEntry>[];

      // Create 3 days of predictions with slight variations from current value
      for (int i = 1; i <= 3; i++) {
        // Add some random variation to the current value
        final random = i % 2 == 0 ? 5.0 : -5.0;
        final predictedValue = (_indicatorValue + random).clamp(0.0, 100.0);

        predictions.add(SentimentHistoryEntry(
          date: today.add(Duration(days: i)),
          value: predictedValue,
          metrics: {}, // Empty metrics for predictions
        ));
      }

      debugPrint('Generated ${predictions.length} predictions');

      if (mounted) {
        setState(() {
          _predictions = predictions;
          _isLoadingPredictions = false;
        });
      }
    } catch (e) {
      debugPrint('Error fetching predictions: $e');
      if (mounted) {
        setState(() {
          _isLoadingPredictions = false;
        });
      }
    }
  }

  // Get the current sentiment level text
  String _getCurrentLevel(double value) {
    if (value <= 20) {
      return 'Extreme Fear';
    } else if (value <= 40) {
      return 'Fear';
    } else if (value <= 60) {
      return 'Stasis';
    } else if (value <= 80) {
      return 'Greed';
    } else {
      return 'Extreme Greed';
    }
  }

  // Get color for the thermometer indicator
  Color _getIndicatorColor(double value) {
    if (value <= 20) {
      return Colors.red;
    } else if (value <= 40) {
      return Colors.orange;
    } else if (value <= 60) {
      return Colors.yellow;
    } else if (value <= 80) {
      return Colors.green.shade300;
    } else {
      return Colors.green;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0F1723), // Dark navy background
      body: _isLoading
          ? const Center(child: CircularProgressIndicator(color: Colors.white))
          : RefreshIndicator(
              onRefresh: _fetchMarketSentiment,
              color: Colors.blue,
              backgroundColor: Colors.grey.shade900,
              child: SingleChildScrollView(
                physics: const AlwaysScrollableScrollPhysics(),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Crypto Pulse title
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 24),
                          decoration: BoxDecoration(
                            color: const Color(0xFF1C2A3D), // Slightly lighter navy
                            borderRadius: BorderRadius.circular(30),
                          ),
                          child: const Text(
                            'Crypto Pulse',
                            style: TextStyle(
                              fontSize: 32,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ),

                        const SizedBox(height: 40),

                        // Reactor image and sentiment value
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            // Reactor image
                            Image.asset(
                              'logo/Sinusoid/Reactor.png',
                              width: 180,
                              height: 180,
                            ),

                            const SizedBox(width: 20),

                            // Sentiment value and status
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  _indicatorValue.round().toString(),
                                  style: const TextStyle(
                                    fontSize: 80,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                  ),
                                ),
                                Text(
                                  _getCurrentLevel(_indicatorValue),
                                  style: TextStyle(
                                    fontSize: 32,
                                    color: _getIndicatorColor(_indicatorValue),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),

                        const SizedBox(height: 30),

                        // Thermometer indicator
                        Column(
                          children: [
                            // Color bar
                            Container(
                              height: 12,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(6),
                                gradient: const LinearGradient(
                                  colors: [
                                    Colors.red,
                                    Colors.orange,
                                    Colors.yellow,
                                    Colors.lightGreen,
                                    Colors.green,
                                  ],
                                ),
                              ),
                            ),

                            // Pointer
                            Stack(
                              alignment: Alignment.topCenter,
                              children: [
                                // Triangle pointer
                                Transform.translate(
                                  offset: Offset(
                                    (MediaQuery.of(context).size.width - 32) * (_indicatorValue / 100) - 8,
                                    0,
                                  ),
                                  child: Icon(
                                    Icons.arrow_drop_up,
                                    color: Colors.white,
                                    size: 24,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),

                        const SizedBox(height: 40),

                        // Chart button
                        ElevatedButton(
                          onPressed: () {
                            // Navigate to chart screen
                            Navigator.pushNamed(context, '/charts');
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: const Color(0xFF1C2A3D),
                            padding: const EdgeInsets.symmetric(horizontal: 40, vertical: 12),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(30),
                            ),
                          ),
                          child: const Text(
                            'Chart',
                            style: TextStyle(
                              fontSize: 18,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
      // Add bottom navigation bar
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 2, // Sinusoid tab
        onTap: (index) {
          if (index != 2) {
            switch (index) {
              case 0:
                Navigator.pushReplacementNamed(context, '/news');
                break;
              case 1:
                Navigator.pushReplacementNamed(context, '/charts');
                break;
              case 3:
                Navigator.pushReplacementNamed(context, '/courses');
                break;
              case 4:
                Navigator.pushReplacementNamed(context, '/profile');
                break;
            }
          }
        },
      ),
    );
  }
}
