import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/crypto_currency.dart';
import '../services/binance_service.dart';
import 'package:intl/intl.dart';
import '../widgets/chart_painters.dart';
import '../providers/crypto_provider.dart';
import 'dart:math' as math;

class CryptoDetailScreen extends StatefulWidget {
  final CryptoCurrency crypto;

  const CryptoDetailScreen({super.key, required this.crypto});

  @override
  State<CryptoDetailScreen> createState() => _CryptoDetailScreenState();
}

class _CryptoDetailScreenState extends State<CryptoDetailScreen> {
  final BinanceService _binanceService = BinanceService();
  bool _isLineChart = false; // По умолчанию свечной график
  String _selectedTimeframe = '1Д'; // По умолчанию 1 день

  late Future<List<List<dynamic>>> _klinesFuture;
  CryptoCurrency? _latestCryptoData;

  @override
  void initState() {
    super.initState();

    // Show a message when the screen is first loaded
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Loading ${widget.crypto.name} chart...'),
          duration: const Duration(seconds: 1),
          backgroundColor: Colors.blue,
        ),
      );
    });

    // Проверяем, что переданная криптовалюта имеет все необходимые данные
    if (widget.crypto.symbol.isEmpty) {
      // Если символ пустой, используем заглушку
      _latestCryptoData = CryptoCurrency(
        id: 'bitcoin',
        name: 'Bitcoin',
        symbol: 'BTC',
        price: 50000.0,
        priceChangePercentage24h: 2.5,
        marketCap: 1000000000000,
        volume24h: 50000000000,
        imageUrl: 'https://via.placeholder.com/50?text=BTC',
        priceHistory: CryptoCurrency.getMockItems().first.priceHistory,
      );
    } else {
      // Use the provided crypto data
      _latestCryptoData = widget.crypto;
    }

    // Инициализируем _klinesFuture с моковыми данными, чтобы сразу был график
    final List<List<dynamic>> initialMockData = _generateInitialMockData();
    _klinesFuture = Future.value(initialMockData);

    // Загружаем данные для графика
    _loadKlineData();

    // Check if we have updated data in the provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      try {
        final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);
        final updatedCrypto = cryptoProvider.getCryptoBySymbol(_latestCryptoData!.symbol);
        if (updatedCrypto != null) {
          setState(() {
            _latestCryptoData = updatedCrypto;
            // Перезагружаем данные для графика с обновленной криптовалютой
            _loadKlineData();
          });
        }
      } catch (e) {
        // Игнорируем ошибки при получении данных из провайдера
      }
    });
  }

  // Generate initial mock data for the chart
  List<List<dynamic>> _generateInitialMockData() {
    final List<List<dynamic>> mockData = [];
    final now = DateTime.now().millisecondsSinceEpoch;
    final basePrice = widget.crypto.price;

    for (int i = 0; i < 96; i++) {
      final timestamp = now - (96 - i) * 60000;
      final open = basePrice * (1 + (i % 5 - 2) * 0.005);
      final close = open * (1 + (i % 3 - 1) * 0.003);
      final high = math.max(open, close) * 1.01;
      final low = math.min(open, close) * 0.99;
      final volume = basePrice * 1000 * (i % 10 + 1);

      mockData.add([
        timestamp,
        open.toString(),
        high.toString(),
        low.toString(),
        close.toString(),
        volume.toString(),
      ]);
    }

    return mockData;
  }

  void _loadKlineData() {
    try {
      // Определяем интервал и лимит на основе выбранного таймфрейма
      String interval;
      int limit;

      switch (_selectedTimeframe) {
        case '1Д':
          interval = '15m'; // 15-минутные свечи для дневного графика
          limit = 96; // 24 часа / 15 минут = 96 свечей
          break;
        case '1Н':
          interval = '1h';
          limit = 168; // 7 дней * 24 часа = 168 часов
          break;
        case '1М':
          interval = '1d';
          limit = 30; // 30 дней
          break;
        case '3М':
          interval = '1d';
          limit = 90; // 90 дней
          break;
        case '6М':
          interval = '1d';
          limit = 180; // 180 дней
          break;
        case 'Текущий год':
          interval = '1w';
          limit = 52; // 52 недели
          break;
        default:
          interval = '15m';
          limit = 96;
      }

      final crypto = _latestCryptoData ?? widget.crypto;

      // Создаем моковые данные для графика
      final List<List<dynamic>> mockData = [];
      final now = DateTime.now().millisecondsSinceEpoch;
      final basePrice = crypto.price;

      for (int i = 0; i < limit; i++) {
        final timestamp = now - (limit - i) * 60000; // Интервал в миллисекундах
        final open = basePrice * (1 + (i % 5 - 2) * 0.005);
        final close = open * (1 + (i % 3 - 1) * 0.003);
        final high = math.max(open, close) * 1.01;
        final low = math.min(open, close) * 0.99;
        final volume = basePrice * 1000 * (i % 10 + 1);

        mockData.add([
          timestamp,
          open.toString(),
          high.toString(),
          low.toString(),
          close.toString(),
          volume.toString(),
        ]);
      }

      // Сначала устанавливаем моковые данные, чтобы экран сразу отобразился
      setState(() {
        _klinesFuture = Future.value(mockData);
      });

      // Затем пытаемся загрузить реальные данные
      _binanceService.getKlines(
        symbol: '${crypto.symbol}USDT',
        interval: interval,
        limit: limit,
      ).then((realData) {
        if (mounted && realData.isNotEmpty) {
          setState(() {
            _klinesFuture = Future.value(realData);
          });
        }
      }).catchError((error) {
        // В случае ошибки оставляем моковые данные
      });
    } catch (e) {
      // В случае любой ошибки используем пустой список
      _klinesFuture = Future.value([]);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Use the latest data from provider if available, otherwise use the widget data
    final crypto = _latestCryptoData ?? widget.crypto;

    final isPositive = crypto.priceChangePercentage24h >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;
    final changeSign = isPositive ? '+' : '';

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        title: Text(
          '${crypto.name} Chart',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: () {
              // Show a message to indicate that we're refreshing the data
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Refreshing chart data...'),
                  duration: Duration(seconds: 1),
                  backgroundColor: Colors.blue,
                ),
              );

              // Refresh data
              _loadKlineData();

              // Check for updated crypto data in provider
              final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);
              final updatedCrypto = cryptoProvider.getCryptoBySymbol(widget.crypto.symbol);
              if (updatedCrypto != null) {
                setState(() {
                  _latestCryptoData = updatedCrypto;
                });
              }
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_horiz, color: Colors.white),
            onPressed: () {},
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Заголовок с названием и ценой
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            crypto.name,
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 24,
                            ),
                          ),
                          Text(
                            '${crypto.symbol} · USD',
                            style: TextStyle(
                              color: Colors.grey[400],
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            '\$${_formatPrice(crypto.price)}',
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 24,
                            ),
                          ),
                          Text(
                            '$changeSign${crypto.priceChangePercentage24h.toStringAsFixed(2)}%',
                            style: TextStyle(
                              color: changeColor,
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Переключатели временных интервалов
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: [
                    _buildTimeframeButton('1Д'),
                    _buildTimeframeButton('1Н'),
                    _buildTimeframeButton('1М'),
                    _buildTimeframeButton('3М'),
                    _buildTimeframeButton('6М'),
                    _buildTimeframeButton('Текущий год'),
                  ],
                ),
              ),
            ),

            // График - увеличиваем высоту для лучшего отображения
            Container(
              height: 350, // Increased height for better visibility
              margin: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 8.0),
              decoration: BoxDecoration(
                color: Colors.grey[900],
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(51), // 0.2 * 255 = 51
                    spreadRadius: 1,
                    blurRadius: 5,
                    offset: const Offset(0, 3),
                  ),
                ],
              ),
              child: FutureBuilder<List<List<dynamic>>>(
                future: _klinesFuture,
                builder: (context, snapshot) {
                  // Всегда показываем график, даже если данные еще загружаются или произошла ошибка
                  // Это предотвратит появление сообщения "Showing details for"
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    // Показываем индикатор загрузки поверх заглушки графика
                    return Stack(
                      children: [
                        // Заглушка графика
                        _isLineChart
                            ? _buildLineChart([])
                            : _buildCandlestickChart([]),
                        // Индикатор загрузки с текстом
                        Container(
                          color: Colors.black.withAlpha(100),
                          child: const Center(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                CircularProgressIndicator(
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                                  strokeWidth: 3,
                                ),
                                SizedBox(height: 16),
                                Text(
                                  'Loading chart data...',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    );
                  } else if (snapshot.hasError || !snapshot.hasData || snapshot.data!.isEmpty) {
                    // В случае ошибки или отсутствия данных показываем заглушку графика с сообщением
                    return Stack(
                      children: [
                        _isLineChart
                            ? _buildLineChart([])
                            : _buildCandlestickChart([]),
                        Center(
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.black.withAlpha(200),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: const Text(
                              'No chart data available',
                              style: TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    );
                  }

                  // Chart loaded successfully

                  return Stack(
                    children: [
                      // График
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: ClipRect(
                          child: _isLineChart
                            ? _buildLineChart(snapshot.data!)
                            : _buildCandlestickChart(snapshot.data!),
                        ),
                      ),

                      // Переключатель типа графика
                      Positioned(
                        top: 8,
                        right: 8,
                        child: Row(
                          children: [
                            _buildChartTypeButton(
                              icon: Icons.show_chart,
                              isSelected: _isLineChart,
                              onTap: () {
                                setState(() {
                                  _isLineChart = true;
                                });
                              },
                            ),
                            const SizedBox(width: 8),
                            _buildChartTypeButton(
                              icon: Icons.candlestick_chart,
                              isSelected: !_isLineChart,
                              onTap: () {
                                setState(() {
                                  _isLineChart = false;
                                });
                              },
                            ),
                          ],
                        ),
                      ),

                      // Показатели на графике
                      Positioned(
                        top: 16,
                        right: 80,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            Text(
                              '41 239',
                              style: TextStyle(
                                color: Colors.grey[400],
                                fontSize: 12,
                              ),
                            ),
                            SizedBox(height: 60),
                            Text(
                              '41 134',
                              style: TextStyle(
                                color: Colors.grey[400],
                                fontSize: 12,
                              ),
                            ),
                            SizedBox(height: 60),
                            Text(
                              '41 030',
                              style: TextStyle(
                                color: Colors.grey[400],
                                fontSize: 12,
                              ),
                            ),
                            SizedBox(height: 60),
                            Text(
                              '40 925',
                              style: TextStyle(
                                color: Colors.grey[400],
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Временные метки под графиком
                      Positioned(
                        bottom: 0,
                        left: 16,
                        right: 16,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text('17', style: TextStyle(color: Colors.grey[400], fontSize: 12)),
                            Text('18', style: TextStyle(color: Colors.grey[400], fontSize: 12)),
                            Text('19', style: TextStyle(color: Colors.grey[400], fontSize: 12)),
                            Text('20', style: TextStyle(color: Colors.grey[400], fontSize: 12)),
                            Text('21', style: TextStyle(color: Colors.grey[400], fontSize: 12)),
                            Text('22', style: TextStyle(color: Colors.grey[400], fontSize: 12)),
                            Text('23', style: TextStyle(color: Colors.grey[400], fontSize: 12)),
                          ],
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),

            // Информация о торгах
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  // Разделительная линия
                  Container(
                    height: 1,
                    color: Colors.grey[900],
                  ),
                  const SizedBox(height: 16),

                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildInfoItem('Открытие', '\$${_formatPrice(_getOpenPrice())}'),
                      _buildInfoItem('Объем', '${_formatVolume((_latestCryptoData ?? widget.crypto).volume24h)} млн'),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildInfoItem('Максимум', '\$${_formatPrice(_getHighPrice())}'),
                      _buildInfoItem('Цена/приб.', '—'),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildInfoItem('Минимум', '\$${_formatPrice(_getLowPrice())}'),
                      _buildInfoItem('Рын. кап.', '—'),
                    ],
                  ),

                  // Разделительная линия
                  const SizedBox(height: 16),
                  Container(
                    height: 1,
                    color: Colors.grey[900],
                  ),
                ],
              ),
            ),

            // Дополнительная информация
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Заголовок
                  Text(
                    'О криптовалюте',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),

                  // Описание
                  Text(
                    _getCryptoDescription(widget.crypto.symbol),
                    style: TextStyle(
                      color: Colors.grey[400],
                      fontSize: 14,
                      height: 1.5,
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Ссылка на дополнительную информацию
                  GestureDetector(
                    onTap: () {
                      // Открыть дополнительную информацию
                    },
                    child: Row(
                      children: [
                        Text(
                          'Еще данные (Binance)',
                          style: TextStyle(
                            color: Colors.blue[300],
                            fontSize: 16,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          Icons.arrow_forward_ios,
                          size: 14,
                          color: Colors.blue[300],
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Статус рынка
                  Text(
                    'Binance',
                    style: TextStyle(
                      color: Colors.grey[500],
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Рынок криптовалют ${_isMarketOpen() ? "открыт" : "закрыт"}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTimeframeButton(String timeframe) {
    final isSelected = _selectedTimeframe == timeframe;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedTimeframe = timeframe;
          _loadKlineData();
        });
      },
      child: Container(
        margin: const EdgeInsets.only(right: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.grey[800] : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
          border: isSelected
              ? null
              : Border.all(color: Colors.grey[800]!, width: 1),
        ),
        child: Text(
          timeframe,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[400],
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 13,
          ),
        ),
      ),
    );
  }

  Widget _buildChartTypeButton({
    required IconData icon,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: isSelected ? Colors.grey[800] : Colors.grey[900],
          borderRadius: BorderRadius.circular(8),
        ),
        child: Icon(
          icon,
          color: isSelected ? Colors.white : Colors.grey[400],
          size: 20,
        ),
      ),
    );
  }

  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(
            color: Colors.grey[500],
            fontSize: 14,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildLineChart(List<List<dynamic>> klines) {
    if (klines.isEmpty) {
      // Если данных нет, создаем моковые данные для графика
      final basePrice = (_latestCryptoData ?? widget.crypto).price;
      final List<double> mockPrices = [];

      // Генерируем 24 точки с небольшими колебаниями
      for (int i = 0; i < 24; i++) {
        final randomFactor = (i % 3 == 0) ? 0.01 : (i % 5 == 0) ? -0.01 : 0.005;
        mockPrices.add(basePrice * (1 + randomFactor * (i % 7)));
      }

      final maxPrice = mockPrices.reduce((a, b) => a > b ? a : b);
      final minPrice = mockPrices.reduce((a, b) => a < b ? a : b);

      // Add padding to min and max prices for better visualization
      final padding = (maxPrice - minPrice) * 0.1;
      final adjustedMaxPrice = maxPrice + padding;
      final adjustedMinPrice = math.max(0.0, minPrice - padding).toDouble(); // Ensure min price is not negative

      return Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(8),
        ),
        child: CustomPaint(
          size: const Size(double.infinity, double.infinity),
          painter: LineChartPainter(
            prices: mockPrices,
            maxPrice: adjustedMaxPrice,
            minPrice: adjustedMinPrice,
            color: (_latestCryptoData ?? widget.crypto).priceChangePercentage24h >= 0 ? Colors.green : Colors.red,
          ),
        ),
      );
    }

    try {
      // Извлекаем цены закрытия для линейного графика
      final List<double> prices = [];

      for (var kline in klines) {
        try {
          if (kline.length >= 5) {
            final price = double.parse(kline[4].toString());
            if (price > 0) {
              prices.add(price);
            }
          }
        } catch (e) {
          // Пропускаем некорректные данные
          continue;
        }
      }

      if (prices.isEmpty) {
        // Если не удалось извлечь цены, используем цену криптовалюты
        final basePrice = (_latestCryptoData ?? widget.crypto).price;
        for (int i = 0; i < 24; i++) {
          final randomFactor = (i % 3 == 0) ? 0.01 : (i % 5 == 0) ? -0.01 : 0.005;
          prices.add(basePrice * (1 + randomFactor * (i % 7)));
        }
      }

      final maxPrice = prices.reduce((a, b) => a > b ? a : b);
      final minPrice = prices.reduce((a, b) => a < b ? a : b);

      // Add padding to min and max prices for better visualization
      final padding = (maxPrice - minPrice) * 0.1;
      final adjustedMaxPrice = maxPrice + padding;
      final adjustedMinPrice = math.max(0.0, minPrice - padding).toDouble(); // Ensure min price is not negative

      return Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(8),
        ),
        child: CustomPaint(
          size: const Size(double.infinity, double.infinity),
          painter: LineChartPainter(
            prices: prices,
            maxPrice: adjustedMaxPrice,
            minPrice: adjustedMinPrice,
            color: (_latestCryptoData ?? widget.crypto).priceChangePercentage24h >= 0 ? Colors.green : Colors.red,
          ),
        ),
      );
    } catch (e) {
      // В случае ошибки возвращаем простой график с базовой ценой
      final basePrice = (_latestCryptoData ?? widget.crypto).price;
      final List<double> fallbackPrices = List.generate(24, (i) => basePrice);

      // Add some variation to the fallback prices for a more realistic chart
      for (int i = 0; i < fallbackPrices.length; i++) {
        fallbackPrices[i] = basePrice * (1 + (i % 5 - 2) * 0.002);
      }

      return Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(8),
        ),
        child: CustomPaint(
          size: const Size(double.infinity, double.infinity),
          painter: LineChartPainter(
            prices: fallbackPrices,
            maxPrice: basePrice * 1.01,
            minPrice: basePrice * 0.99,
            color: Colors.grey,
          ),
        ),
      );
    }
  }

  Widget _buildCandlestickChart(List<List<dynamic>> klines) {
    if (klines.isEmpty) {
      // Если данных нет, создаем моковые данные для свечного графика
      final basePrice = (_latestCryptoData ?? widget.crypto).price;
      final List<CandleData> mockCandles = [];
      final now = DateTime.now();

      // Генерируем 24 свечи с небольшими колебаниями
      for (int i = 0; i < 24; i++) {
        final time = now.subtract(Duration(hours: 24 - i));
        final open = basePrice * (1 + (i % 5 - 2) * 0.005);
        final close = open * (1 + (i % 3 - 1) * 0.003);
        final high = math.max(open, close) * 1.01;
        final low = math.min(open, close) * 0.99;

        mockCandles.add(CandleData(
          time: time,
          open: open,
          high: high,
          low: low,
          close: close,
          volume: basePrice * 1000 * (i % 10 + 1),
        ));
      }

      return Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(8),
        ),
        child: CustomPaint(
          size: const Size(double.infinity, double.infinity),
          painter: CandlestickChartPainter(candles: mockCandles),
        ),
      );
    }

    try {
      // Преобразуем данные для свечного графика
      final List<CandleData> candles = [];

      for (var kline in klines) {
        try {
          if (kline.length >= 6) {
            candles.add(CandleData(
              time: DateTime.fromMillisecondsSinceEpoch(kline[0] as int),
              open: double.parse(kline[1].toString()),
              high: double.parse(kline[2].toString()),
              low: double.parse(kline[3].toString()),
              close: double.parse(kline[4].toString()),
              volume: double.parse(kline[5].toString()),
            ));
          }
        } catch (e) {
          // Пропускаем некорректные данные
          continue;
        }
      }

      if (candles.isEmpty) {
        // Если не удалось создать свечи, используем моковые данные
        final basePrice = (_latestCryptoData ?? widget.crypto).price;
        final now = DateTime.now();

        for (int i = 0; i < 24; i++) {
          final time = now.subtract(Duration(hours: 24 - i));
          final open = basePrice * (1 + (i % 5 - 2) * 0.005);
          final close = open * (1 + (i % 3 - 1) * 0.003);
          final high = math.max(open, close) * 1.01;
          final low = math.min(open, close) * 0.99;

          candles.add(CandleData(
            time: time,
            open: open,
            high: high,
            low: low,
            close: close,
            volume: basePrice * 1000 * (i % 10 + 1),
          ));
        }
      }

      return Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(8),
        ),
        child: CustomPaint(
          size: const Size(double.infinity, double.infinity),
          painter: CandlestickChartPainter(candles: candles),
        ),
      );
    } catch (e) {
      // В случае ошибки возвращаем простой график с базовой ценой
      final basePrice = (_latestCryptoData ?? widget.crypto).price;
      final List<CandleData> fallbackCandles = [];
      final now = DateTime.now();

      for (int i = 0; i < 24; i++) {
        fallbackCandles.add(CandleData(
          time: now.subtract(Duration(hours: 24 - i)),
          open: basePrice,
          high: basePrice * 1.005,
          low: basePrice * 0.995,
          close: basePrice,
          volume: basePrice * 1000,
        ));
      }

      return Container(
        decoration: BoxDecoration(
          color: Colors.black,
          borderRadius: BorderRadius.circular(8),
        ),
        child: CustomPaint(
          size: const Size(double.infinity, double.infinity),
          painter: CandlestickChartPainter(candles: fallbackCandles),
        ),
      );
    }
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return NumberFormat('#,##0').format(price);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  String _formatVolume(double volume) {
    return (volume / 1000000).toStringAsFixed(1);
  }

  double _getOpenPrice() {
    // Возвращаем цену открытия (можно было бы получить из API)
    final crypto = _latestCryptoData ?? widget.crypto;
    return crypto.price * 0.99;
  }

  double _getHighPrice() {
    // Возвращаем максимальную цену (можно было бы получить из API)
    final crypto = _latestCryptoData ?? widget.crypto;
    return crypto.price * 1.02;
  }

  double _getLowPrice() {
    // Возвращаем минимальную цену (можно было бы получить из API)
    final crypto = _latestCryptoData ?? widget.crypto;
    return crypto.price * 0.98;
  }

  bool _isMarketOpen() {
    // Крипторынок всегда открыт
    return true;
  }

  String _getCryptoDescription(String symbol) {
    final Map<String, String> descriptions = {
      'BTC': 'Bitcoin (BTC) - первая и самая известная криптовалюта, созданная в 2009 году анонимным разработчиком или группой разработчиков под псевдонимом Сатоши Накамото. Bitcoin работает на технологии блокчейн, которая обеспечивает децентрализованную, защищенную от подделок систему транзакций.',
      'ETH': 'Ethereum (ETH) - вторая по величине криптовалюта, запущенная в 2015 году. Ethereum - это не только криптовалюта, но и платформа для создания децентрализованных приложений (dApps) и смарт-контрактов. Ethereum позволяет разработчикам создавать и запускать децентрализованные приложения без вмешательства третьих сторон.',
      'BNB': 'Binance Coin (BNB) - криптовалюта, выпущенная криптовалютной биржей Binance. Изначально BNB был создан как токен на блокчейне Ethereum, но позже перешел на собственный блокчейн Binance Chain. BNB используется для оплаты комиссий на бирже Binance, участия в токенсейлах на платформе Binance Launchpad и других целей.',
      'SOL': 'Solana (SOL) - высокопроизводительный блокчейн, поддерживающий смарт-контракты и децентрализованные приложения. Solana использует инновационный механизм консенсуса Proof of History (PoH) в сочетании с Proof of Stake (PoS), что позволяет обрабатывать тысячи транзакций в секунду с минимальными комиссиями.',
      'ADA': 'Cardano (ADA) - блокчейн-платформа третьего поколения, разработанная для создания смарт-контрактов и децентрализованных приложений. Cardano использует научный подход и академические исследования для создания безопасной и устойчивой платформы. Проект был основан Чарльзом Хоскинсоном, одним из соучредителей Ethereum.',
      'XRP': 'XRP - цифровой актив, созданный компанией Ripple для использования в сети RippleNet. XRP используется для обеспечения ликвидности и быстрых трансграничных платежей. В отличие от многих других криптовалют, XRP не использует майнинг, а все монеты были выпущены при создании.',
    };

    return descriptions[symbol] ?? 'Информация о данной криптовалюте временно недоступна.';
  }
}
