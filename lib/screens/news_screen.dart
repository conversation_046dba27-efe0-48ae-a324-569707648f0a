import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:provider/provider.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../models/news_item.dart';
import '../widgets/ios_style_news_card.dart';
import '../widgets/ios_style_filter_panel.dart';
import '../widgets/app_bottom_navigation.dart';
import '../widgets/news_categories_dock.dart';
import '../providers/news_provider.dart';
import '../providers/theme_provider.dart';
import '../providers/auth_provider.dart';
import '../utils/device_type.dart';

class NewsScreen extends StatefulWidget {
  const NewsScreen({super.key});

  @override
  State<NewsScreen> createState() => _NewsScreenState();
}

class _NewsScreenState extends State<NewsScreen> {
  final TextEditingController _searchController = TextEditingController();
  bool _showFilters = false;
  int _topNavSelectedIndex = 1; // По умолчанию выбрана категория "Крипто"

  // Функция для показа диалога поиска
  void _showSearchDialog(BuildContext context, NewsProvider newsProvider) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);
    showCupertinoModalPopup(
      context: context,
      builder: (context) => CupertinoPopupSurface(
        isSurfacePainted: true,
        child: Container(
          height: 200,
          padding: const EdgeInsets.all(16),
          color: themeProvider.isDarkMode ? const Color(0xFF1C1C1E) : Colors.white,
          child: Column(
            children: [
              Text(
                'Поиск новостей',
                style: TextStyle(
                  color: themeProvider.isDarkMode ? Colors.white : Colors.black,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Container(
                height: 40,
                padding: const EdgeInsets.symmetric(horizontal: 8.0),
                decoration: BoxDecoration(
                  color: themeProvider.isDarkMode ? const Color(0xFF2C2C2E) : const Color(0xFFF2F2F7),
                  borderRadius: BorderRadius.circular(10.0),
                ),
                child: Row(
                  children: [
                    Icon(
                      CupertinoIcons.search,
                      color: themeProvider.isDarkMode ? Colors.grey[400] : Colors.grey[500],
                      size: 16,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: TextField(
                        controller: _searchController,
                        style: TextStyle(
                          color: themeProvider.isDarkMode ? Colors.white : Colors.black,
                          fontSize: 14.0,
                        ),
                        decoration: InputDecoration(
                          hintText: 'Введите запрос для поиска...',
                          hintStyle: TextStyle(
                            color: themeProvider.isDarkMode ? Colors.grey[400] : Colors.grey[500],
                            fontSize: 14.0,
                          ),
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.symmetric(vertical: 10.0),
                        ),
                        onSubmitted: (value) {
                          if (value.isNotEmpty) {
                            newsProvider.searchNews(value);
                            Navigator.pop(context);
                          }
                        },
                      ),
                    ),
                    if (_searchController.text.isNotEmpty)
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            _searchController.clear();
                          });
                        },
                        child: Icon(
                          CupertinoIcons.clear_circled_solid,
                          color: themeProvider.isDarkMode ? Colors.grey[400] : Colors.grey[500],
                          size: 16,
                        ),
                      ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    child: Text('Отмена'),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                  ),
                  CupertinoButton(
                    padding: EdgeInsets.zero,
                    child: Text('Поиск'),
                    onPressed: () {
                      if (_searchController.text.isNotEmpty) {
                        newsProvider.searchNews(_searchController.text);
                        Navigator.pop(context);
                      }
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  void initState() {
    super.initState();

    // Fetch news when screen initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Provider.of<NewsProvider>(context, listen: false).fetchTopHeadlines();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
  
  // Вспомогательный метод для получения строкового представления категории
  String _getCategoryString(NewsCategory category) {
    switch (category) {
      case NewsCategory.all:
        return 'all';
      case NewsCategory.crypto:
        return 'crypto';
      case NewsCategory.stocks:
        return 'stock';
      case NewsCategory.whales:
        return 'whales';
      default:
        return 'all';
    }
  }

  // This method is no longer needed as we're using the NewsProvider
  // for filtering news items

  // Build news list based on device type
  Widget _buildNewsList(BuildContext context, NewsProvider newsProvider, ThemeProvider themeProvider) {
    final isDesktop = DeviceUtils.isDesktop(context);

    return RefreshIndicator(
      onRefresh: () => newsProvider.fetchTopHeadlines(),
      child: isDesktop
          ? _buildDesktopNewsList(context, newsProvider)
          : _buildMobileNewsList(context, newsProvider),
    );
  }

  // Desktop layout with grid view
  Widget _buildDesktopNewsList(BuildContext context, NewsProvider newsProvider) {
    // Фиксируем количество колонок равным 2, как на скриншоте 2
    const columns = 2;
    
    // Соотношение сторон карточек – значительно увеличиваем, чтобы карточки стали очень низкими
    const aspectRatio = 4.0; // Гораздо более низкие карточки, чтобы в колонке помещалось минимум 3

    return GridView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 10.0), // Увеличиваем отступы для соответствия дизайну на скриншоте 2
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: columns,
        childAspectRatio: aspectRatio,
        crossAxisSpacing: 20.0, // Увеличиваем отступ между колонками
        mainAxisSpacing: 16.0, // Увеличиваем отступ между рядами
      ),
      itemCount: newsProvider.filteredNews.length,
      itemBuilder: (context, index) {
        final newsItem = newsProvider.filteredNews[index];
        return IOSStyleNewsCard(
          newsItem: newsItem,
          isFeatured: false, // Убираем выделение для всех новостей
          onTap: () {
            Navigator.pushNamed(
              context,
              '/news_detail',
              arguments: newsItem,
            );
          },
        ).animate().fadeIn(
          delay: Duration(milliseconds: 30 * index),
          duration: 300.ms,
        );
      },
    );
  }

  // Mobile layout with animated list view
  Widget _buildMobileNewsList(BuildContext context, NewsProvider newsProvider) {
    final GlobalKey<AnimatedListState> _listKey = GlobalKey<AnimatedListState>();

    // Используем ключ для доступа к состоянию списка после построения
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Имитируем появление элементов один за другим
      for (int i = 0; i < newsProvider.filteredNews.length; i++) {
        Future.delayed(Duration(milliseconds: 50 * i), () {
          if (_listKey.currentState != null) {
            _listKey.currentState!.insertItem(i, duration: const Duration(milliseconds: 300));
          }
        });
      }
    });

    return AnimatedList(
      key: _listKey,
      initialItemCount: 0, // Начинаем с пустого списка
      itemBuilder: (context, index, animation) {
        if (index >= newsProvider.filteredNews.length) return const SizedBox();

        final newsItem = newsProvider.filteredNews[index];
        return SlideTransition(
          position: animation.drive(Tween<Offset>(
            begin: const Offset(1, 0), // Появление справа
            end: Offset.zero,
          )),
          child: FadeTransition(
            opacity: animation,
            child: IOSStyleNewsCard(
              newsItem: newsItem,
              isFeatured: false,
              onTap: () {
                Navigator.pushNamed(
                  context,
                  '/news_detail',
                  arguments: newsItem,
                );
              },
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final newsProvider = Provider.of<NewsProvider>(context);
    final themeProvider = Provider.of<ThemeProvider>(context);
    final authProvider = Provider.of<AuthProvider>(context);

    return Scaffold(
      backgroundColor: const Color(0xFF0A0B0D), // Dark background like sinusoid
      extendBody: true, // Allow body to extend behind navigation bar
      appBar: AppBar(
        title: Row(
          children: [
            ShaderMask(
              shaderCallback: (bounds) {
                return LinearGradient(
                  colors: themeProvider.isDarkMode
                      ? [
                          const Color(0xFF5E5CE6),
                          const Color(0xFF007AFF),
                          const Color(0xFF34C759),
                        ]
                      : [
                          const Color(0xFF007AFF),
                          const Color(0xFF5856D6),
                          const Color(0xFF34C759),
                        ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ).createShader(bounds);
              },
              child: Text(
                'Crypto News',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 22,
                  color: Colors.white,
                  letterSpacing: -0.5,
                ),
              ),
            ),
            const Spacer(),
            // Search button
            GestureDetector(
              onTap: () {
                // Открываем диалог для поиска
                _showSearchDialog(context, newsProvider);
              },
              child: Container(
                height: 36,
                width: 36,
                decoration: BoxDecoration(
                  color: themeProvider.isDarkMode ? const Color(0xFF2C2C2E) : const Color(0xFFF2F2F7),
                  borderRadius: BorderRadius.circular(18.0),
                ),
                child: Center(
                  child: Icon(
                    CupertinoIcons.search,
                    color: themeProvider.isDarkMode ? Colors.white70 : Colors.black54,
                    size: 20,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            // Theme toggle
            GestureDetector(
              onTap: () {
                themeProvider.toggleTheme();
              },
              child: Container(
                height: 36,
                width: 36,
                decoration: BoxDecoration(
                  color: themeProvider.isDarkMode ? const Color(0xFF2C2C2E) : const Color(0xFFF2F2F7),
                  borderRadius: BorderRadius.circular(18.0),
                ),
                child: Center(
                  child: Icon(
                    themeProvider.isDarkMode ? CupertinoIcons.sun_max : CupertinoIcons.moon,
                    color: themeProvider.isDarkMode ? Colors.white70 : Colors.black54,
                    size: 20,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 8),
            // User avatar
            if (authProvider.isAuthenticated)
              GestureDetector(
                onTap: () {
                  Navigator.pushNamed(context, '/profile');
                },
                child: Container(
                  height: 36,
                  width: 36,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: const LinearGradient(
                      colors: [Color(0xFF5E5CE6), Color(0xFF007AFF)],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      authProvider.username.isNotEmpty
                          ? authProvider.username[0].toUpperCase()
                          : 'U',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                ),
              ).animate().fadeIn(duration: 300.ms),
          ],
        ),
        backgroundColor: themeProvider.isDarkMode ? const Color(0xFF1C1C1E) : Colors.white,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(1.0),
          child: Container(
            color: themeProvider.isDarkMode ? const Color(0xFF38383A) : const Color(0xFFE5E5EA),
            height: 0.5,
          ),
        ),
      ),
      body: Column(
        children: [
          // Анимированный топбар для категорий новостей
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
            child: AnimatedNewsCategoriesDock(
              selectedCategory: _getCategoryString(newsProvider.selectedCategory),
              selectedColor: themeProvider.isDarkMode ? Colors.blue : Colors.blue.shade600,
              unselectedColor: themeProvider.isDarkMode ? Colors.grey.shade400 : Colors.grey.shade600,
              onCategoryChanged: (category) {
                setState(() {
                  // Обновляем выбранный индекс для сохранения совместимости с существующим кодом
                  switch (category) {
                    case 'all':
                      _topNavSelectedIndex = 0;
                      newsProvider.setCategory(NewsCategory.all);
                      break;
                    case 'crypto':
                      _topNavSelectedIndex = 1;
                      newsProvider.setCategory(NewsCategory.crypto);
                      break;
                    case 'stock':
                      _topNavSelectedIndex = 2;
                      newsProvider.setCategory(NewsCategory.stocks);
                      break;
                    case 'whales':
                      _topNavSelectedIndex = 3;
                      newsProvider.setCategory(NewsCategory.whales);
                      break;
                  }
                });
              },
            ),
          ),
          
          // Пустое пространство вместо поисковой строки
          const SizedBox(height: 10),

          // Панель фильтров (скрыта по умолчанию)
          if (_showFilters)
            IOSStyleFilterPanel(
              selectedTags: newsProvider.selectedTags,
              selectedSentiment: newsProvider.selectedSentiment,
              onTagToggle: (tag) {
                newsProvider.toggleTag(tag);
              },
              onSentimentChanged: (sentiment) {
                newsProvider.setSentiment(sentiment);
              },
            ).animate().fadeIn(duration: 200.ms).slideY(
              begin: -0.1,
              end: 0,
              curve: Curves.easeOut,
              duration: 200.ms,
            ),

          // News list
          Expanded(
            child: newsProvider.isLoading
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const CupertinoActivityIndicator(radius: 16),
                        const SizedBox(height: 16),
                        Text(
                          'Loading News...',
                          style: TextStyle(
                            color: themeProvider.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  )
                : newsProvider.filteredNews.isEmpty
                    ? Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                color: themeProvider.isDarkMode ? const Color(0xFF2C2C2E) : const Color(0xFFE5E5EA),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Icon(
                                CupertinoIcons.news,
                                size: 40,
                                color: themeProvider.isDarkMode ? Colors.grey[500] : Colors.grey[400],
                              ),
                            ),
                            const SizedBox(height: 16),
                            Text(
                              'No News Found',
                              style: TextStyle(
                                color: themeProvider.isDarkMode ? Colors.white : Colors.black,
                                fontSize: 20,
                                fontWeight: FontWeight.bold,
                                letterSpacing: -0.5,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'Try changing your search or filters',
                              style: TextStyle(
                                color: themeProvider.isDarkMode ? Colors.grey[400] : Colors.grey[600],
                                fontSize: 16,
                              ),
                            ),
                            const SizedBox(height: 24),
                            CupertinoButton(
                              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                              color: CupertinoColors.systemBlue,
                              borderRadius: BorderRadius.circular(24),
                              onPressed: () {
                                newsProvider.clearFilters();
                                _searchController.clear();
                                newsProvider.fetchTopHeadlines();
                              },
                              child: const Text(
                                'Reset Filters',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    : _buildNewsList(context, newsProvider, themeProvider),
          ),
        ],
      ),
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 0,
        onTap: (index) {
          if (index != 0) {
            switch (index) {
              case 1:
                Navigator.pushReplacementNamed(context, '/charts');
                break;
              case 2:
                Navigator.pushReplacementNamed(context, '/sinusoid');
                break;
              case 3:
                Navigator.pushReplacementNamed(context, '/courses');
                break;
              case 4:
                Navigator.pushReplacementNamed(context, '/profile');
                break;
            }
          }
        },
      ),
    );
  }
}

