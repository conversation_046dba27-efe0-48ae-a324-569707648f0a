import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import '../models/candle.dart';
import '../widgets/tradingview_chart.dart';

class TradingSimulatorGame extends StatefulWidget {
  final String symbol;
  final int intervalSeconds;

  const TradingSimulatorGame({
    super.key,
    required this.symbol,
    required this.intervalSeconds,
  });

  @override
  State<TradingSimulatorGame> createState() => _TradingSimulatorGameState();
}

class _TradingSimulatorGameState extends State<TradingSimulatorGame> {
  List<Candle> _candles = [];
  List<Candle> _futureCandles = []; // Будущие свечи для показа после выбора
  bool _loading = true;
  bool _hasResult = false;
  bool _won = false;
  bool _userPredictedUp = true; // По умолчанию предполагаем рост
  bool _showFutureCandles = false; // Флаг для показа будущих свечей
  int _level = 1;
  double _balance = 1000.0;
  double? _entryPrice;
  int _consecutiveWins = 0;
  String _currentScenario = ''; // Идентификатор текущего сценария

  @override
  void initState() {
    super.initState();
    _fetchCandles();
  }

  Future<void> _fetchCandles() async {
    setState(() {
      _loading = true;
      _hasResult = false;
      _entryPrice = null;
      _showFutureCandles = false;
      _futureCandles = [];
    });

    try {
      // Добавляем заголовки для улучшения стабильности API-запросов
      final headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json',
      };

      // Используем более точный интервал для API Binance
      final interval = _getBinanceInterval(widget.intervalSeconds);

      // Запрашиваем больше свечей, чтобы иметь данные для будущих свечей
      final url = Uri.parse(
        'https://api.binance.com/api/v3/klines?symbol=${widget.symbol}&interval=$interval&limit=50',
      );

      final resp = await http.get(url, headers: headers);

      if (resp.statusCode == 200) {
        final data = jsonDecode(resp.body) as List;

        if (data.isNotEmpty) {
          // Генерируем уникальный идентификатор сценария
          _currentScenario = DateTime.now().millisecondsSinceEpoch.toString();

          // Берем только последние 20 свечей для отображения
          final allCandles = data.map((c) => Candle.fromList(c)).toList();

          // В режиме Custom Mode используем случайный сегмент исторических данных
          if (widget.symbol == 'CUSTOM') {
            final random = math.Random();
            final startIndex = random.nextInt(allCandles.length - 27); // 20 для отображения + 7 для будущих свечей
            _candles = allCandles.sublist(startIndex, startIndex + 20);

            // Сохраняем 7 следующих свечей как будущие
            _futureCandles = allCandles.sublist(startIndex + 20, startIndex + 27);
          } else {
            // В обычном режиме используем последние свечи
            _candles = allCandles.sublist(allCandles.length - 27, allCandles.length - 7);
            _futureCandles = allCandles.sublist(allCandles.length - 7);
          }

          setState(() {
            _loading = false;
          });
        } else {
          _handleApiError('No data received from Binance');
        }
      } else {
        // Handle API error
        _handleApiError('API Error: ${resp.statusCode}');
      }
    } catch (e) {
      // Handle network or parsing error
      _handleApiError('Error: $e');
    }
  }

  // Преобразует секунды в формат интервала Binance
  String _getBinanceInterval(int seconds) {
    if (seconds <= 60) return '1m';
    if (seconds <= 300) return '5m';
    if (seconds <= 900) return '15m';
    if (seconds <= 1800) return '30m';
    if (seconds <= 3600) return '1h';
    if (seconds <= 14400) return '4h';
    if (seconds <= 86400) return '1d';
    return '1w';
  }

  void _handleApiError(String message) {
    // Log error and generate synthetic data
    developer.log('API Error: $message', name: 'TradingSimulator');

    // Show error snackbar if context is available
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Using demo data: $message'),
          duration: const Duration(seconds: 3),
          backgroundColor: Colors.orange,
        ),
      );
    }

    setState(() {
      // Generate some random candles for demo purposes
      _candles = _generateSyntheticCandles();
      _loading = false;
    });
  }

  List<Candle> _generateSyntheticCandles() {
    // Generate synthetic candles for demo (20 for display + 7 for future)
    final List<Candle> syntheticCandles = [];
    final basePrice = 30000.0;
    double lastClose = basePrice;

    // Создаем случайный тренд для этого сценария
    final random = math.Random();
    final trendStrength = random.nextDouble() * 0.6 - 0.3; // От -0.3 до +0.3
    final volatility = random.nextDouble() * 0.01 + 0.005; // От 0.005 до 0.015

    // Генерируем уникальный идентификатор сценария
    _currentScenario = DateTime.now().millisecondsSinceEpoch.toString();

    for (int i = 0; i < 27; i++) { // 20 для отображения + 7 для будущих свечей
      // Добавляем случайность, но сохраняем общий тренд
      final randomFactor = random.nextDouble() * 2 - 1; // От -1 до 1
      final trendFactor = trendStrength * (1 + 0.1 * i); // Тренд усиливается со временем
      final change = (trendFactor + randomFactor * volatility) * basePrice;

      final open = lastClose;
      final close = open + change;

      // Создаем high и low с учетом направления свечи
      final max = math.max(open, close);
      final min = math.min(open, close);
      final wickSize = basePrice * volatility * (1 + random.nextDouble());

      final high = max + wickSize * 0.6;
      final low = min - wickSize * 0.8;

      syntheticCandles.add(Candle(
        open: open,
        high: high,
        low: low,
        close: close,
        timestamp: DateTime.now().subtract(Duration(minutes: (27 - i) * widget.intervalSeconds ~/ 60)),
      ));

      lastClose = close;
    }

    // Разделяем на основные и будущие свечи
    _futureCandles = syntheticCandles.sublist(20);
    return syntheticCandles.sublist(0, 20);
  }

  void _makeGuess(bool up) async {
    if (_loading || _hasResult) return;

    setState(() {
      _entryPrice = _candles.last.close;
      _loading = true;
      _userPredictedUp = up;
    });

    // Небольшая задержка для анимации загрузки
    await Future.delayed(const Duration(milliseconds: 500));

    // Определяем результат на основе последней будущей свечи
    final lastFutureCandle = _futureCandles.last;
    final priceWentUp = lastFutureCandle.close > _entryPrice!;
    final won = (up && priceWentUp) || (!up && !priceWentUp);
    final profit = won ? _balance * 0.1 : -_balance * 0.1;

    setState(() {
      _loading = false;
      _won = won;
      _balance += profit;
      if (_balance <= 0) _balance = 0;

      // Update consecutive wins
      if (won) {
        _consecutiveWins++;
      } else {
        _consecutiveWins = 0;
      }

      // Показываем будущие свечи
      _showFutureCandles = true;
    });
  }

  // Callback после завершения анимации показа будущих свечей
  void _onFutureCandlesAnimationComplete() {
    setState(() {
      _hasResult = true;
    });
  }

  // Метод _fetchNextCandle больше не нужен, так как мы заранее получаем будущие свечи

  // Метод _handleNextCandleError больше не нужен, так как мы заранее получаем будущие свечи

  Widget _buildCandleChart() {
    if (_candles.isEmpty) {
      return const Center(child: Text('No data available', style: TextStyle(color: Colors.white)));
    }

    // Форматирование цены для отображения
    String formatPrice(double price) {
      if (price > 1000) {
        return price.toStringAsFixed(0);
      } else if (price > 100) {
        return price.toStringAsFixed(1);
      } else if (price > 1) {
        return price.toStringAsFixed(2);
      } else {
        return price.toStringAsFixed(4);
      }
    }

    // Объединяем основные и будущие свечи для отображения
    final allCandles = [..._candles];
    if (_showFutureCandles) {
      allCandles.addAll(_futureCandles);
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.black12,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.white10),
      ),
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          // Price info
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                widget.symbol,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Current: ${formatPrice(_candles.last.close)}',
                style: TextStyle(
                  color: _candles.last.isGreen ? Colors.green : Colors.red,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // Chart
          Expanded(
            child: TradingViewChart(
              candles: allCandles,
              entryPrice: _entryPrice,
              showEntryPoint: _entryPrice != null,
              visibleFutureCandles: _futureCandles.length,
              showFutureCandles: _showFutureCandles,
              onAnimationComplete: _showFutureCandles ? _onFutureCandlesAnimationComplete : null,
            ),
          ),

          // Временная шкала
          SizedBox(
            height: 20,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatDateTime(_candles.first.timestamp),
                  style: const TextStyle(color: Colors.white54, fontSize: 10),
                ),
                Text(
                  _formatDateTime(_candles.last.timestamp),
                  style: const TextStyle(color: Colors.white54, fontSize: 10),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _nextLevel() {
    if (_balance <= 0) {
      // Game over
      Navigator.of(context).pop();
      return;
    }

    setState(() {
      _level++;
      _hasResult = false;
      _entryPrice = null;
    });

    _fetchCandles();
  }



  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Level $_level - Balance \$${_balance.toStringAsFixed(2)}'),
        backgroundColor: Colors.black,
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.black,
              Colors.blueGrey.shade900,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Stats row
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black26,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    _buildStatItem('Balance', '\$${_balance.toStringAsFixed(2)}', Icons.account_balance_wallet),
                    _buildStatItem('Level', '$_level', Icons.trending_up),
                    _buildStatItem('Streak', '$_consecutiveWins', Icons.whatshot),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Chart
              Expanded(
                child: _loading
                    ? const Center(child: CircularProgressIndicator())
                    : _buildCandleChart(),
              ),

              const SizedBox(height: 16),

              // Action buttons or result
              if (!_hasResult) ...[
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildActionButton(true),
                    _buildActionButton(false),
                  ],
                ),
              ] else ...[
                // Результат прогноза
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: _won
                        ? [Colors.green.shade800, Colors.green.shade600]
                        : [Colors.red.shade800, Colors.red.shade600],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: _won ? Colors.green.withAlpha(100) : Colors.red.withAlpha(100),
                        blurRadius: 10,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _won ? Icons.check_circle : Icons.cancel,
                            color: Colors.white,
                            size: 32,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            _won ? 'You Won!' : 'You Lost',
                            style: const TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _won
                          ? 'You earned \$${(_balance * 0.1).toStringAsFixed(2)}!'
                          : 'You lost \$${(_balance * 0.1).toStringAsFixed(2)}',
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 16),
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(30),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        padding: const EdgeInsets.all(8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              'Entry: \$${_entryPrice!.toStringAsFixed(2)}',
                              style: const TextStyle(color: Colors.white),
                            ),
                            Text(
                              'Close: \$${_candles.last.close.toStringAsFixed(2)}',
                              style: const TextStyle(color: Colors.white),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      ElevatedButton(
                        onPressed: _nextLevel,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: _won ? Colors.green.shade800 : Colors.red.shade800,
                          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(30),
                          ),
                        ),
                        child: Text(
                          _balance > 0 ? 'Next Level' : 'Game Over',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ]
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Row(
          children: [
            Icon(icon, size: 16, color: Colors.white70),
            const SizedBox(width: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white70,
                fontSize: 12,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(bool isUp) {
    final color = isUp ? Colors.green : Colors.red;
    final lightColor = isUp ? Colors.green.shade300 : Colors.red.shade300;
    final darkColor = isUp ? Colors.green.shade800 : Colors.red.shade800;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(30),
        boxShadow: [
          BoxShadow(
            color: color.withAlpha(100),
            blurRadius: 10,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _makeGuess(isUp),
          borderRadius: BorderRadius.circular(30),
          splashColor: color.withAlpha(50),
          highlightColor: color.withAlpha(30),
          child: Ink(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [lightColor, darkColor],
              ),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isUp ? Icons.arrow_upward : Icons.arrow_downward,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    isUp ? 'Up' : 'Down',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }



  // Форматирование даты и времени
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final date = DateTime(dateTime.year, dateTime.month, dateTime.day);

    if (date == today) {
      return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else {
      return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')} ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    }
  }
}
