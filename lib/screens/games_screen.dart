import 'package:flutter/material.dart';

class GamesScreen extends StatelessWidget {
  const GamesScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Trading Games'),
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildGameCard(
            context,
            'Anti-FOMO Simulator',
            'Learn to control your FOMO emotions in trading',
            '/anti_fomo_simulator',
            Icons.psychology,
          ),
          _buildGameCard(
            context,
            'Crypto Trading Simulator',
            'Practice crypto trading in a risk-free environment',
            '/crypto_simulator_mode_selection',
            Icons.currency_bitcoin,
          ),
          _buildGameCard(
            context,
            'Market Sentiment',
            'Analyze and understand market sentiment',
            '/market_sentiment',
            Icons.trending_up,
          ),
          _buildGameCard(
            context,
            'Reactor Sinusoid',
            'Master price movement patterns',
            '/enhanced_sinusoid',
            Icons.waves,
          ),
        ],
      ),
    );
  }

  Widget _buildGameCard(
    BuildContext context,
    String title,
    String description,
    String route,
    IconData icon,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: InkWell(
        onTap: () => Navigator.pushNamed(context, route),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Icon(icon, size: 48.0, color: Theme.of(context).primaryColor),
              const SizedBox(width: 16.0),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.titleLarge,
                    ),
                    const SizedBox(height: 4.0),
                    Text(
                      description,
                      style: Theme.of(context).textTheme.bodyMedium,
                    ),
                  ],
                ),
              ),
              const Icon(Icons.arrow_forward_ios),
            ],
          ),
        ),
      ),
    );
  }
} 