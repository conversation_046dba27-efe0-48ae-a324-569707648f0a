import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import '../models/trading_simulator_models.dart';
import '../widgets/gradient_background.dart';
import '../widgets/animated_button.dart';

class InfinitePatternsLeverageScreen extends StatefulWidget {
  const InfinitePatternsLeverageScreen({super.key});

  @override
  _InfinitePatternsLeverageScreenState createState() => _InfinitePatternsLeverageScreenState();
}

class _InfinitePatternsLeverageScreenState extends State<InfinitePatternsLeverageScreen> with SingleTickerProviderStateMixin {
  double leverage = 10.0;
  String selectedDifficulty = 'Medium'; // Default difficulty level
  
  // Predefined leverage values
  final List<double> leverageOptions = [1, 2, 5, 10, 20, 50, 100, 250, 500, 1000];
  
  // Difficulty levels
  final List<String> difficultyLevels = ['Easy', 'Medium', 'Hard', 'Expert'];
  
  // Animation controller for the leverage display
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  
  @override
  void initState() {
    super.initState();
    
    // Set up a subtle pulse animation for the leverage display
    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);
    
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.03).animate(
      CurvedAnimation(
        parent: _pulseController,
        curve: Curves.easeInOut,
      ),
    );
  }
  
  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(CupertinoIcons.back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        title: const Text(
          'Setup Trading',
          style: TextStyle(
            color: Colors.white,
            fontSize: 17,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      body: GradientBackground(
        gradientColors: [
          Colors.black,
          const Color(0xFF191919),
          const Color(0xFF222222),
        ],
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                const SizedBox(height: 16),
                
                // Two settings columns side by side: Difficulty and Leverage
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Difficulty selection - left side
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Title
                          Row(
                            children: const [
                              Icon(
                                CupertinoIcons.speedometer,
                                color: Colors.orange,
                                size: 16,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Difficulty Level',
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          // Difficulty selector
                          Container(
                            decoration: BoxDecoration(
                              color: const Color(0xFF1A1A1A),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.2),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                              border: Border.all(
                                color: Colors.white.withOpacity(0.1),
                                width: 1,
                              ),
                            ),
                            padding: const EdgeInsets.all(8),
                            child: Column(
                              children: difficultyLevels.map((level) => 
                                _buildDifficultyOption(level)
                              ).toList(),
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    // Leverage display - right side
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Title
                          Row(
                            children: const [
                              Icon(
                                CupertinoIcons.chart_bar_alt_fill,
                                color: Colors.orange,
                                size: 16,
                              ),
                              SizedBox(width: 8),
                              Text(
                                'Leverage',
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          // More compact leverage display (reduced by ~10%)
                          AnimatedBuilder(
                            animation: _pulseAnimation,
                            builder: (context, child) {
                              return Transform.scale(
                                scale: _pulseAnimation.value,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 14),
                                  decoration: BoxDecoration(
                                    color: const Color(0xFF1A1A1A),
                                    borderRadius: BorderRadius.circular(14),
                                    boxShadow: [
                                      BoxShadow(
                                        color: _getLeverageColor(leverage).withOpacity(0.2),
                                        blurRadius: 15,
                                        spreadRadius: 1,
                                        offset: const Offset(0, 4),
                                      ),
                                    ],
                                    border: Border.all(
                                      color: _getLeverageColor(leverage).withOpacity(0.3),
                                      width: 1.5,
                                    ),
                                  ),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        crossAxisAlignment: CrossAxisAlignment.baseline,
                                        textBaseline: TextBaseline.alphabetic,
                                        children: [
                                          Text(
                                            '${leverage.toInt()}',
                                            style: TextStyle(
                                              fontSize: 29,
                                              fontWeight: FontWeight.bold,
                                              color: _getLeverageColor(leverage),
                                            ),
                                          ),
                                          Text(
                                            'x',
                                            style: TextStyle(
                                              fontSize: 18,
                                              fontWeight: FontWeight.bold,
                                              color: _getLeverageColor(leverage),
                                            ),
                                          ),
                                        ],
                                      ),
                                      const SizedBox(height: 6),
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
                                        decoration: BoxDecoration(
                                          color: _getLeverageColor(leverage).withOpacity(0.15),
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                        child: Text(
                                          _getLeverageRiskLevel(leverage),
                                          style: TextStyle(
                                            fontSize: 11,
                                            fontWeight: FontWeight.w500,
                                            color: _getLeverageColor(leverage),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 24),
                
                // Higher leverage explanation
                Container(
                  padding: const EdgeInsets.all(16),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.05),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.white.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.orange.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          CupertinoIcons.exclamationmark_triangle,
                          color: Colors.orange,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Higher leverage increases both potential profits and losses.',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white70,
                              ),
                            ),
                            SizedBox(height: 4),
                            Text(
                              'Higher difficulty creates more challenging market scenarios.',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Quick selection chips
                const Text(
                  'Quick Select Leverage',
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                
                const SizedBox(height: 12),
                
                // Leverage chips in a row with scrolling
                SizedBox(
                  height: 45,
                  child: ListView(
                    scrollDirection: Axis.horizontal,
                    children: leverageOptions.map((option) => Padding(
                      padding: const EdgeInsets.only(right: 8),
                      child: _buildLeverageChip(option),
                    )).toList(),
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Slider
                const Text(
                  'Adjust Leverage',
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: Colors.white,
                  ),
                ),
                
                const SizedBox(height: 16),
                
                SliderTheme(
                  data: SliderThemeData(
                    trackHeight: 4,
                    thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 8),
                    overlayShape: const RoundSliderOverlayShape(overlayRadius: 16),
                    trackShape: const RoundedRectSliderTrackShape(),
                    thumbColor: _getLeverageColor(leverage),
                    overlayColor: _getLeverageColor(leverage).withOpacity(0.2),
                  ),
                  child: Slider(
                    value: leverage,
                    min: 1,
                    max: 1000,
                    divisions: 999,
                    activeColor: _getLeverageColor(leverage),
                    inactiveColor: _getLeverageColor(leverage).withOpacity(0.2),
                    onChanged: (value) {
                      setState(() {
                        leverage = value;
                      });
                    },
                  ),
                ),
                
                // Scale marks under slider
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: const [
                      Text('1x', style: TextStyle(fontSize: 12, color: Colors.white70)),
                      Text('250x', style: TextStyle(fontSize: 12, color: Colors.white70)),
                      Text('500x', style: TextStyle(fontSize: 12, color: Colors.white70)),
                      Text('750x', style: TextStyle(fontSize: 12, color: Colors.white70)),
                      Text('1000x', style: TextStyle(fontSize: 12, color: Colors.white70)),
                    ],
                  ),
                ),
                
                const Spacer(),
                
                // Start Trading button
                AnimatedButton(
                  onTap: _startTrading,
                  child: Container(
                    height: 56,
                    margin: const EdgeInsets.only(bottom: 24),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          _getLeverageColor(leverage),
                          _getLeverageColor(leverage).withOpacity(0.7),
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: _getLeverageColor(leverage).withOpacity(0.3),
                          blurRadius: 12,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: const [
                        Icon(
                          CupertinoIcons.chart_bar_fill,
                          color: Colors.white,
                          size: 18,
                        ),
                        SizedBox(width: 10),
                        Text(
                          'Start Trading',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            letterSpacing: 0.5,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
  
  // Difficulty option widget
  Widget _buildDifficultyOption(String level) {
    bool isSelected = selectedDifficulty == level;
    
    Color getColorForDifficulty(String level) {
      switch (level) {
        case 'Easy': return Colors.green;
        case 'Medium': return Colors.blue;
        case 'Hard': return Colors.orange;
        case 'Expert': return Colors.red;
        default: return Colors.blue;
      }
    }
    
    return GestureDetector(
      onTap: () {
        setState(() {
          selectedDifficulty = level;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 12),
        margin: const EdgeInsets.only(bottom: 4),
        decoration: BoxDecoration(
          color: isSelected ? getColorForDifficulty(level).withOpacity(0.2) : Colors.transparent,
          borderRadius: BorderRadius.circular(10),
          border: isSelected ? Border.all(
            color: getColorForDifficulty(level),
            width: 1,
          ) : null,
        ),
        child: Row(
          children: [
            Icon(
              isSelected ? CupertinoIcons.checkmark_circle_fill : CupertinoIcons.circle,
              color: isSelected ? getColorForDifficulty(level) : Colors.white.withOpacity(0.5),
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              level,
              style: TextStyle(
                color: isSelected ? getColorForDifficulty(level) : Colors.white.withOpacity(0.8),
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                fontSize: 14,
              ),
            ),
            const Spacer(),
            Text(
              _getDifficultyDescription(level),
              style: TextStyle(
                fontSize: 10,
                color: Colors.white.withOpacity(0.5),
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Leverage chip for quick selection
  Widget _buildLeverageChip(double value) {
    final isSelected = value == leverage;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          leverage = value;
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 150),
        padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected
              ? _getLeverageColor(value)
              : Colors.grey[850],
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: isSelected 
                ? Colors.white.withOpacity(0.2)
                : _getLeverageColor(value).withOpacity(0.3),
            width: 1,
          ),
          boxShadow: isSelected ? [
            BoxShadow(
              color: _getLeverageColor(value).withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ] : null,
        ),
        child: Text(
          '${value.toInt()}x',
          style: TextStyle(
            color: isSelected ? Colors.white : _getLeverageColor(value),
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
            fontSize: 14,
          ),
        ),
      ),
    );
  }
  
  // Get color based on leverage value
  Color _getLeverageColor(double leverage) {
    if (leverage <= 5) {
      return Colors.green;
    } else if (leverage <= 20) {
      return Colors.blue;
    } else if (leverage <= 100) {
      return Colors.orange;
    } else if (leverage <= 500) {
      return Colors.deepOrange;
    } else {
      return Colors.red;
    }
  }
  
  // Get risk level text based on leverage value
  String _getLeverageRiskLevel(double leverage) {
    if (leverage <= 5) {
      return 'Low Risk';
    } else if (leverage <= 20) {
      return 'Medium Risk';
    } else if (leverage <= 100) {
      return 'High Risk';
    } else if (leverage <= 500) {
      return 'Very High Risk';
    } else {
      return 'Extreme Risk';
    }
  }
  
  // Get full description based on leverage value
  String _getLeverageDescription(double leverage) {
    if (leverage <= 5) {
      return 'Low risk, suitable for beginners';
    } else if (leverage <= 20) {
      return 'Medium risk, for experienced traders';
    } else if (leverage <= 50) {
      return 'High risk, for advanced traders';
    } else if (leverage <= 500) {
      return 'Very high risk, significant experience needed';
    } else {
      return 'Extreme risk, for professional traders only';
    }
  }
  
  // Get difficulty level description
  String _getDifficultyDescription(String level) {
    switch (level) {
      case 'Easy': return 'Gentle price movements';
      case 'Medium': return 'Moderate volatility';
      case 'Hard': return 'Unpredictable patterns';
      case 'Expert': return 'Extreme market conditions';
      default: return '';
    }
  }
  
  // Start trading
  void _startTrading() {
    Navigator.pushNamed(
      context,
      '/crypto_simulator',
      arguments: {
        'mode': SimulatorMode.infinitePatterns,
        'leverage': leverage,
        'difficulty': selectedDifficulty,
        'initialBalance': 1000.0,
      },
    );
  }
}
