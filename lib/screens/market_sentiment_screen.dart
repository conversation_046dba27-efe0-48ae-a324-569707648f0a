import 'dart:async';
import 'package:flutter/material.dart';
import '../services/market_sentiment_data_service.dart';
import '../utils/design_system.dart';

class MarketSentimentScreen extends StatefulWidget {
  const MarketSentimentScreen({super.key});

  @override
  State<MarketSentimentScreen> createState() => _MarketSentimentScreenState();
}

class _MarketSentimentScreenState extends State<MarketSentimentScreen> {
  final MarketSentimentDataService _marketSentimentService = MarketSentimentDataService();
  Map<String, dynamic> _metrics = {};
  double _overallScore = 50.0;
  DateTime _lastUpdate = DateTime.now();
  Timer? _dataRefreshTimer;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadInitialData();
    _startPeriodicRefresh();
  }

  void _loadInitialData() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Получаем данные о рыночном сентименте
      final data = await _marketSentimentService.fetchMarketSentiment();

      // Получаем исторические данные для последнего обновления
      final history = await _marketSentimentService.getHistoricalData();
      final lastEntry = history.isNotEmpty ? history.last : null;

      if (lastEntry != null) {
        setState(() {
          _overallScore = data.overallScore;
          _metrics = Map<String, dynamic>.from(data.detailedMetrics);
          _lastUpdate = data.timestamp;
          _isLoading = false;
        });
      } else {
        // Если нет исторических данных, используем только общий показатель
        setState(() {
          _overallScore = data.overallScore;
          _metrics = Map<String, dynamic>.from(data.detailedMetrics);
          _lastUpdate = data.timestamp;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar();
    }
  }

  void _startPeriodicRefresh() {
    // Обновление данных каждый час
    _dataRefreshTimer = Timer.periodic(const Duration(hours: 1), (_) {
      _loadInitialData();
    });
  }

  void _showErrorSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Не удалось обновить данные. Показаны последние доступные.'),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  void dispose() {
    _dataRefreshTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Рыночный Sentiment'),
        centerTitle: true,
        backgroundColor: DesignSystem.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildSentimentView(),
      floatingActionButton: FloatingActionButton(
        onPressed: _loadInitialData,
        backgroundColor: DesignSystem.accentColor,
        child: const Icon(Icons.refresh),
      ),
    );
  }

  Widget _buildSentimentView() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Общий индикатор настроения
          _buildOverallSentimentCard(),

          const SizedBox(height: 16),

          // Детальные метрики
          _buildDetailedMetricsSection(),

          const SizedBox(height: 16),

          // Временная метка последнего обновления
          _buildUpdateTimestampCard(),
        ],
      ),
    );
  }

  Widget _buildOverallSentimentCard() {
    final score = _overallScore;
    Color scoreColor = _getScoreColor(score);

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              scoreColor.withAlpha(25),
              scoreColor.withAlpha(76),
            ],
          ),
        ),
        padding: const EdgeInsets.all(20.0),
        child: Column(
          children: [
            Text(
              'Общий индекс настроения',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: DesignSystem.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              score.toStringAsFixed(1),
              style: TextStyle(
                fontSize: 48,
                fontWeight: FontWeight.bold,
                color: scoreColor,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              _getSentimentDescription(score),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: DesignSystem.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailedMetricsSection() {
    if (_metrics.isEmpty) {
      return const Card(
        elevation: 4,
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Text('Детальные метрики недоступны'),
        ),
      );
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Детальные метрики',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.w600,
                color: DesignSystem.textPrimary,
              ),
            ),
            const SizedBox(height: 16),
            ..._metrics.entries.map((entry) => _buildMetricRow(
              entry.key,
              entry.value as double
            )),
          ],
        ),
      ),
    );
  }

  Widget _buildMetricRow(String metricName, double value) {
    final Color metricColor = _getScoreColor(value);

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 2,
                child: Text(
                  _getMetricLabel(metricName),
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                    color: DesignSystem.textPrimary,
                  ),
                ),
              ),
              Text(
                value.toStringAsFixed(1),
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: metricColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          // Индикатор прогресса
          ClipRRect(
            borderRadius: BorderRadius.circular(4),
            child: LinearProgressIndicator(
              value: value / 100,
              backgroundColor: Colors.grey.withAlpha(51),
              valueColor: AlwaysStoppedAnimation<Color>(metricColor),
              minHeight: 8,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpdateTimestampCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.update, size: 20, color: DesignSystem.textSecondary),
            const SizedBox(width: 8),
            Text(
              'Последнее обновление: ${_formatDateTime(_lastUpdate)}',
              style: TextStyle(
                fontSize: 14,
                color: DesignSystem.textSecondary,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Вспомогательные методы
  Color _getScoreColor(double score) {
    if (score < 20) return Colors.red;
    if (score < 40) return Colors.orange;
    if (score < 60) return Colors.yellow.shade700;
    if (score < 80) return Colors.lightGreen;
    return Colors.green;
  }

  String _getSentimentDescription(double score) {
    if (score < 20) return 'Крайне негативный (Crash)';
    if (score < 40) return 'Негативный (Anxiety)';
    if (score < 60) return 'Нейтральный (Stasis)';
    if (score < 80) return 'Позитивный (Lift)';
    return 'Крайне позитивный (Surge)';
  }

  String _getMetricLabel(String metricName) {
    final Map<String, String> labels = {
      'Fear & Greed Index': 'Индекс страха и жадности',
      'News Sentiment': 'Новостной сентимент',
      'Holders Score': 'Активность холдеров',
      'Volume Score': 'Объем торгов',
      'Social Engagement': 'Социальная активность',
      'Price Volatility': 'Волатильность цены',
      'Bitcoin Dominance': 'Доминирование Bitcoin',
    };
    return labels[metricName] ?? metricName;
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}.${dateTime.month.toString().padLeft(2, '0')}.${dateTime.year} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
