import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:intl/intl.dart';
import '../models/crypto_currency.dart';
import '../providers/crypto_provider.dart';
import '../services/binance_service.dart';
import '../widgets/mini_chart.dart';
import '../widgets/candlestick_chart.dart';

class CoinDetailScreen extends StatefulWidget {
  const CoinDetailScreen({Key? key}) : super(key: key);

  @override
  State<CoinDetailScreen> createState() => _CoinDetailScreenState();
}

class _CoinDetailScreenState extends State<CoinDetailScreen> {
  String _selectedTimeframe = '1Ч';
  final List<String> _timeframes = ['30М', '1Ч', '4Ч', '1Д', '1Н'];
  bool _isChartTypeCandlestick = true; // Флаг для переключения типа графика (свечной график по умолчанию)

  final BinanceService _binanceService = BinanceService();
  // Инициализируем _klinesFuture пустым списком, чтобы избежать LateInitializationError
  Future<List<List<dynamic>>> _klinesFuture = Future.value([]);
  CryptoCurrency? _latestCryptoData;
  bool _isLoading = false;

  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    // Инициализируем _klinesFuture с пустым списком
    _klinesFuture = Future.value([]);

    // Загружаем данные для графика сразу при инициализации
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // Безопасно получаем аргументы
      try {
        final args = ModalRoute.of(context)?.settings.arguments;
        if (args is CryptoCurrency) {
          // Если аргументы есть, сохраняем их
          _latestCryptoData = args;

          // Проверяем, есть ли обновленные данные в провайдере
          final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);
          final updatedCrypto = cryptoProvider.getCryptoBySymbol(args.symbol);
          if (updatedCrypto != null) {
            setState(() {
              _latestCryptoData = updatedCrypto;
            });
          }
        }
      } catch (e) {
        // Игнорируем ошибки при получении данных из провайдера
        // Используем debugPrint вместо print для отладки
        debugPrint('Ошибка при получении данных: $e');
      }

      // Сразу загружаем реальные данные
      _loadKlineData();

      // Запускаем автоматическую прокрутку бегущей строки
      _startAutoScroll();
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // Автоматическая прокрутка бегущей строки
  void _startAutoScroll() {
    if (!mounted) return;

    Future.delayed(const Duration(milliseconds: 50), () {
      if (_scrollController.hasClients) {
        final maxScrollExtent = _scrollController.position.maxScrollExtent;
        final currentOffset = _scrollController.offset;

        // Если скролл уже близок к концу, прыгаем в начало без анимации
        if (currentOffset >= maxScrollExtent * 0.9) {
          _scrollController.jumpTo(0);
          // Сразу запускаем следующую итерацию
          if (mounted) _startAutoScroll();
          return;
        }

        // Анимируем скролл на небольшое расстояние для плавности
        _scrollController.animateTo(
          currentOffset + 1.0, // Прокручиваем на очень маленькое расстояние
          duration: const Duration(milliseconds: 50), // Очень короткая анимация
          curve: Curves.linear,
        ).then((_) {
          if (mounted) {
            _startAutoScroll(); // Рекурсивно вызываем для бесконечной прокрутки
          }
        });
      } else if (mounted) {
        // Если клиенты еще не готовы, пробуем снова
        _startAutoScroll();
      }
    });
  }



  // Загружаем данные для графика
  Future<void> _loadKlineData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Безопасное получение данных о криптовалюте
      CryptoCurrency crypto;

      if (_latestCryptoData != null) {
        crypto = _latestCryptoData!;
      } else {
        final args = ModalRoute.of(context)?.settings.arguments;
        if (args is CryptoCurrency) {
          crypto = args;
        } else {
          // Если данные не получены, используем заглушку
          crypto = CryptoCurrency(
            id: 'bitcoin',
            name: 'Bitcoin',
            symbol: 'BTC',
            price: 50000.0,
            priceChangePercentage24h: 2.5,
            marketCap: 1000000000000,
            volume24h: 50000000000,
            imageUrl: 'https://via.placeholder.com/50?text=BTC',
            priceHistory: _generateDefaultPriceHistory(50000.0),
          );
        }
      }

      String interval;

      // Определяем интервал в зависимости от выбранного таймфрейма
      // Таймфрейм означает, сколько времени представляет одна свеча
      switch (_selectedTimeframe) {
        case '30М': // 30 минут
          interval = '30m'; // 30-минутные свечи
          break;
        case '1Ч': // 1 час
          interval = '1h'; // Часовые свечи
          break;
        case '4Ч': // 4 часа
          interval = '4h'; // 4-часовые свечи
          break;
        case '1Д': // 1 день
          interval = '1d'; // Дневные свечи
          break;
        case '1Н': // 1 неделя
          interval = '1w'; // Недельные свечи
          break;
        default:
          interval = '1h'; // По умолчанию часовые свечи
      }

      // Определяем количество точек данных в зависимости от таймфрейма
      // Ограничиваем количество свечей, чтобы график был читаемым
      int limit;
      switch (_selectedTimeframe) {
        case '30М': // 30 минут
          limit = 24; // 12 часов (24 свечи по 30 минут)
          break;
        case '1Ч': // 1 час
          limit = 24; // 24 часа (24 свечи по 1 часу)
          break;
        case '4Ч': // 4 часа
          limit = 30; // 5 дней (30 свечей по 4 часа)
          break;
        case '1Д': // 1 день
          limit = 30; // 30 дней (30 свечей по 1 дню)
          break;
        case '1Н': // 1 неделя
          limit = 20; // 20 недель (20 свечей по 1 неделе)
          break;
        default:
          limit = 24;
      }

      // Загружаем данные
      _klinesFuture = _binanceService.getKlines(
        symbol: '${crypto.symbol}USDT',
        interval: interval,
        limit: limit,
      );
    } catch (e) {
      // В случае ошибки показываем сообщение об ошибке
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Ошибка загрузки данных: ${e.toString()}'),
          duration: const Duration(seconds: 2),
        ),
      );
      // Используем пустой список данных
      _klinesFuture = Future.value([]);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    // Безопасное получение данных о криптовалюте
    CryptoCurrency crypto;

    try {
      if (_latestCryptoData != null) {
        crypto = _latestCryptoData!;
      } else {
        final args = ModalRoute.of(context)?.settings.arguments;
        if (args is CryptoCurrency) {
          crypto = args;
        } else {
          // Если данные не получены, используем заглушку
          crypto = CryptoCurrency(
            id: 'bitcoin',
            name: 'Bitcoin',
            symbol: 'BTC',
            price: 50000.0,
            priceChangePercentage24h: 2.5,
            marketCap: 1000000000000,
            volume24h: 50000000000,
            imageUrl: 'https://via.placeholder.com/50?text=BTC',
            priceHistory: _generateDefaultPriceHistory(50000.0),
          );

          // Загружаем данные, если они еще не загружены
          if (!_isLoading) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              _loadKlineData();
            });
          }
        }
      }
    } catch (e) {
      // В случае ошибки используем заглушку
      crypto = CryptoCurrency(
        id: 'bitcoin',
        name: 'Bitcoin',
        symbol: 'BTC',
        price: 50000.0,
        priceChangePercentage24h: 2.5,
        marketCap: 1000000000000,
        volume24h: 50000000000,
        imageUrl: 'https://via.placeholder.com/50?text=BTC',
        priceHistory: _generateDefaultPriceHistory(50000.0),
      );
    }

    final isPositive = crypto.priceChangePercentage24h >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => Navigator.pop(context),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              _loadKlineData();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Обновление данных...'),
                  duration: Duration(seconds: 1),
                ),
              );
            },
          ),
          IconButton(
            icon: const Icon(Icons.more_horiz),
            onPressed: () {},
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Бегущая строка с монетами
            Container(
              height: 55,
              color: Colors.black,
              child: Consumer<CryptoProvider>(
                builder: (context, cryptoProvider, _) {
                  final topCryptos = cryptoProvider.trendingCryptos.take(8).toList();

                  return ListView.builder(
                    controller: _scrollController,
                    scrollDirection: Axis.horizontal,
                    itemCount: topCryptos.length,
                    itemBuilder: (context, index) {
                      final coin = topCryptos[index];
                      final isPositive = coin.priceChangePercentage24h >= 0;
                      final changeSign = isPositive ? '+' : '';
                      final changeColor = isPositive ? Colors.green : Colors.red;

                      return InkWell(
                        onTap: () {
                          // Останавливаем автоскролл перед переходом
                          _scrollController.animateTo(
                            _scrollController.offset,
                            duration: Duration.zero,
                            curve: Curves.linear,
                          );

                          // Переходим на страницу детальной информации о монете
                          Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                              builder: (context) => CoinDetailScreen(),
                              settings: RouteSettings(arguments: coin),
                            ),
                          );
                        },
                        // Добавляем обратную связь при нажатии
                        splashColor: Colors.grey.shade600.withAlpha(76), // 0.3 * 255 = 76
                        highlightColor: Colors.grey.shade600.withAlpha(25), // 0.1 * 255 = 25
                        child: Container(
                          width: 160,
                          padding: const EdgeInsets.symmetric(horizontal: 6.0),
                          decoration: BoxDecoration(
                            // Добавляем эффект при наведении/нажатии
                            borderRadius: BorderRadius.circular(8),
                            color: Colors.transparent,
                          ),
                          child: Row(
                            children: [
                              // Мини-график
                              SizedBox(
                                width: 60,
                                height: 32,
                                child: MiniChart(
                                  crypto: coin,
                                  color: changeColor,
                                  strokeWidth: 1.8,
                                  showArea: true,
                                ),
                              ),
                              const SizedBox(width: 6),
                              // Название и цена
                              Expanded(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          coin.symbol,
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 13,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 1,
                                        ),
                                        const SizedBox(width: 2),
                                        Text(
                                          'USD',
                                          style: TextStyle(
                                            color: Colors.grey.shade400,
                                            fontWeight: FontWeight.normal,
                                            fontSize: 11,
                                          ),
                                        ),
                                      ],
                                    ),
                                    Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Text(
                                          '\$${_formatPrice(coin.price)}',
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontSize: 11,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 1,
                                        ),
                                        const SizedBox(width: 2),
                                        Text(
                                          '$changeSign${coin.priceChangePercentage24h.toStringAsFixed(1)}%',
                                          style: TextStyle(
                                            color: changeColor,
                                            fontSize: 11,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                          maxLines: 1,
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  );
                },
              ),
            ),
            // Заголовок с информацией о монете и ценой
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Название
                  Text(
                    crypto.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(width: 8),

                  // Символ/USD
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade800,
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Text(
                      '${crypto.symbol}/USD',
                      style: TextStyle(
                        color: Colors.grey.shade300,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),

                  const Spacer(),

                  // Цена и изменение
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '\$${_formatPrice(crypto.price)}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 24,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                        decoration: BoxDecoration(
                          color: changeColor.withAlpha(50),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          '${isPositive ? '+' : ''}${crypto.priceChangePercentage24h.toStringAsFixed(2)}%',
                          style: TextStyle(
                            color: changeColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Селектор временного интервала
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Селектор временного интервала
                  SizedBox(
                    height: 32,
                    child: ListView.builder(
                      shrinkWrap: true,
                      scrollDirection: Axis.horizontal,
                      itemCount: _timeframes.length,
                      itemBuilder: (context, index) {
                        final timeframe = _timeframes[index];
                        final isSelected = timeframe == _selectedTimeframe;

                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              _selectedTimeframe = timeframe;
                              _loadKlineData();
                            });
                          },
                          child: Container(
                            margin: const EdgeInsets.only(right: 8),
                            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                            decoration: BoxDecoration(
                              color: isSelected ? Colors.grey.shade800 : Colors.grey.shade900,
                              borderRadius: BorderRadius.circular(6),
                              border: Border.all(
                                color: isSelected ? Colors.blue.shade700 : Colors.transparent,
                                width: 1.5,
                              ),
                            ),
                            child: Text(
                              timeframe,
                              style: TextStyle(
                                color: isSelected ? Colors.white : Colors.grey.shade400,
                                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                                fontSize: 13,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),

            // Переключатель типа графика (встроен в ту же строку, что и таймфреймы)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Text(
                    'Тип графика:',
                    style: TextStyle(
                      color: Colors.grey.shade400,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(width: 4),
                  ToggleButtons(
                    isSelected: [!_isChartTypeCandlestick, _isChartTypeCandlestick],
                    onPressed: (index) {
                      setState(() {
                        _isChartTypeCandlestick = index == 1;
                      });
                    },
                    borderRadius: BorderRadius.circular(4),
                    selectedColor: Colors.white,
                    fillColor: Colors.grey.shade800,
                    color: Colors.grey,
                    constraints: const BoxConstraints(minWidth: 50, minHeight: 24),
                    children: const [
                      Text('Линия', style: TextStyle(fontSize: 12)),
                      Text('Свечи', style: TextStyle(fontSize: 12)),
                    ],
                  ),
                ],
              ),
            ),

            // График
            Container(
              height: 250, // Уменьшаем высоту графика
              margin: const EdgeInsets.symmetric(vertical: 8.0),
              padding: const EdgeInsets.all(8.0),
              child: FutureBuilder<List<List<dynamic>>>(
                future: _klinesFuture,
                builder: (context, snapshot) {
                  if (snapshot.connectionState == ConnectionState.waiting && _isLoading) {
                    return const Center(child: CircularProgressIndicator());
                  }

                  if (snapshot.hasError || !snapshot.hasData) {
                    return _buildFallbackChart(crypto);
                  }

                  return _isChartTypeCandlestick
                      ? CandlestickChart(
                          klines: snapshot.data!,
                          upColor: Colors.green,
                          downColor: Colors.red,
                          showGrid: true,
                          showLabels: true,
                          timeframe: _selectedTimeframe, // Передаем текущий таймфрейм
                          currentPrice: crypto.price, // Передаем текущую цену
                        )
                      : _buildLineChart(snapshot.data!, crypto);
                },
              ),
            ),

            // Данные о рынке
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
              child: Column(
                children: [
                  // Таблица с данными
                  Table(
                    columnWidths: const {
                      0: FlexColumnWidth(1),
                      1: FlexColumnWidth(1),
                      2: FlexColumnWidth(1),
                      3: FlexColumnWidth(1),
                    },
                    children: [
                      TableRow(
                        children: [
                          _buildTableCell('Открытие', '\$${_formatPrice(crypto.price * 0.99)}', isHeader: true),
                          _buildTableCell('Объем', _formatVolume(crypto.volume24h), isHeader: true),
                          _buildTableCell('52 нед. макс.', '\$${_formatPrice(crypto.price * 1.2)}', isHeader: true),
                        ],
                      ),
                      TableRow(
                        children: [
                          _buildTableCell('Максимум', '\$${_formatPrice(crypto.price * 1.02)}'),
                          _buildTableCell('Цена/приб.', '—'),
                          _buildTableCell('52 нед. мин.', '\$${_formatPrice(crypto.price * 0.8)}'),
                        ],
                      ),
                      TableRow(
                        children: [
                          _buildTableCell('Минимум', '\$${_formatPrice(crypto.price * 0.98)}'),
                          _buildTableCell('Рын. кап.', _formatMarketCap(crypto.marketCap)),
                          _buildTableCell('Ср. объем', '—'),
                        ],
                      ),
                    ],
                  ),

                  // Ссылка на дополнительные данные
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 16.0),
                    child: Text(
                      'Еще данные (Yahoo Finance)',
                      style: TextStyle(
                        color: Colors.blue,
                        fontSize: 14,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Новостная секция
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Последние новости о ${crypto.name}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    isPositive
                      ? '${crypto.symbol} показывает рост на ${crypto.priceChangePercentage24h.toStringAsFixed(2)}% за последние 24 часа'
                      : '${crypto.symbol} снижается на ${crypto.priceChangePercentage24h.abs().toStringAsFixed(2)}% за последние 24 часа',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Источник: CryptoNews',
                        style: TextStyle(
                          color: Colors.grey.shade400,
                          fontSize: 14,
                        ),
                      ),
                      Text(
                        'Обновлено: ${_getFormattedDate()}',
                        style: TextStyle(
                          color: Colors.grey.shade400,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTableCell(String label, String value, {bool isHeader = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: TextStyle(
              color: Colors.grey.shade400,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: Colors.white,
              fontSize: isHeader ? 14 : 12,
              fontWeight: isHeader ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  // Построение графика на основе данных Binance
  Widget _buildLineChart(List<List<dynamic>> klines, CryptoCurrency crypto) {
    try {
      // Преобразуем данные для линейного графика
      final List<double> prices = [];
      final List<String> dates = [];

      for (var kline in klines) {
        try {
          // Цена закрытия (4-й элемент в массиве)
          prices.add(double.parse(kline[4].toString()));

          // Дата (0-й элемент в массиве)
          final date = DateTime.fromMillisecondsSinceEpoch(kline[0] as int);

          // Форматируем дату в зависимости от выбранного таймфрейма
          String formattedDate;
          switch (_selectedTimeframe) {
            case '30М': // 30 минут
              // Для 30-минутного графика показываем часы и минуты
              formattedDate = '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
              break;
            case '1Ч': // 1 час
              // Для часового графика показываем день и час
              formattedDate = '${date.day}.${date.month} ${date.hour}:00';
              break;
            case '4Ч': // 4 часа
              // Для 4-часового графика показываем день, месяц и час
              formattedDate = '${date.day}.${date.month} ${date.hour}:00';
              break;
            case '1Д': // 1 день
              // Для дневного графика показываем день и месяц
              formattedDate = '${date.day.toString().padLeft(2, '0')}.${date.month.toString().padLeft(2, '0')}';
              break;
            case '1Н': // 1 неделя
              // Для недельного графика показываем месяц и год
              final months = ['Янв', 'Фев', 'Мар', 'Апр', 'Май', 'Июн', 'Июл', 'Авг', 'Сен', 'Окт', 'Ноя', 'Дек'];
              formattedDate = '${months[date.month - 1]} ${date.year}';
              break;
            default:
              formattedDate = '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
          }

          dates.add(formattedDate);
        } catch (e) {
          // Пропускаем некорректные данные
          continue;
        }
      }

      if (prices.isEmpty) {
        return _buildFallbackChart(crypto);
      }

      final maxPrice = prices.reduce((a, b) => a > b ? a : b);
      final minPrice = prices.reduce((a, b) => a < b ? a : b);

      // Добавляем отступ к минимальной и максимальной ценам для лучшей визуализации
      final padding = (maxPrice - minPrice) * 0.1;
      final adjustedMaxPrice = maxPrice + padding;
      final adjustedMinPrice = math.max(0.0, minPrice - padding).toDouble();

      // Создаем улучшенный линейный график с градиентом и сглаживанием
      return CustomPaint(
        size: const Size(double.infinity, double.infinity),
        painter: LineChartPainter(
          prices: prices,
          dates: dates,
          maxPrice: adjustedMaxPrice,
          minPrice: adjustedMinPrice,
          color: crypto.priceChangePercentage24h >= 0 ? Colors.green : Colors.red,
          showGradient: true, // Включаем градиент
          smoothCurve: true, // Включаем сглаживание
          timeframe: _selectedTimeframe, // Передаем текущий таймфрейм
          currentPrice: crypto.price, // Передаем текущую цену для отображения на графике
        ),
      );
    } catch (e) {
      return _buildFallbackChart(crypto);
    }
  }

  // Резервный график, если данные не загрузились
  Widget _buildFallbackChart(CryptoCurrency crypto) {
    final isPositive = crypto.priceChangePercentage24h >= 0;
    final color = isPositive ? Colors.green : Colors.red;

    return CustomPaint(
      size: const Size(double.infinity, double.infinity),
      painter: FallbackChartPainter(
        color: color,
        isPositive: isPositive,
      ),
    );
  }

  // Форматирование цены
  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  // Форматирование объема
  String _formatVolume(double volume) {
    if (volume >= 1000000000) {
      return '${(volume / 1000000000).toStringAsFixed(1)} млрд';
    } else {
      return '${(volume / 1000000).toStringAsFixed(1)} млн';
    }
  }

  // Форматирование рыночной капитализации
  String _formatMarketCap(double marketCap) {
    if (marketCap >= 1000000000000) {
      return '${(marketCap / 1000000000000).toStringAsFixed(2)} трлн';
    } else if (marketCap >= 1000000000) {
      return '${(marketCap / 1000000000).toStringAsFixed(2)} млрд';
    } else {
      return '${(marketCap / 1000000).toStringAsFixed(2)} млн';
    }
  }

  // Получение форматированной текущей даты
  String _getFormattedDate() {
    final now = DateTime.now();
    return '${now.day}.${now.month}.${now.year}';
  }

  // Генерация дефолтной истории цен для заглушки
  List<PricePoint> _generateDefaultPriceHistory(double basePrice) {
    final List<PricePoint> priceHistory = [];
    final random = math.Random(42); // Фиксированное зерно для стабильных результатов
    final now = DateTime.now();

    // Генерируем историю цен за последние 24 часа
    for (int i = 0; i < 24; i++) {
      final time = now.subtract(Duration(hours: 24 - i));
      final randomFactor = 0.05 * (random.nextDouble() - 0.5); // От -2.5% до +2.5%
      final price = basePrice * (1 + randomFactor);
      priceHistory.add(PricePoint(time, price));
    }

    return priceHistory;
  }
}

// Painter для линейного графика
class LineChartPainter extends CustomPainter {
  final List<double> prices;
  final List<String> dates;
  final double maxPrice;
  final double minPrice;
  final Color color;
  final bool showGradient;
  final bool smoothCurve;
  final String timeframe; // Добавляем параметр для таймфрейма
  final double? currentPrice; // Добавляем параметр для текущей цены

  LineChartPainter({
    required this.prices,
    required this.dates,
    required this.maxPrice,
    required this.minPrice,
    required this.color,
    this.showGradient = false,
    this.smoothCurve = false,
    this.timeframe = '1Ч', // По умолчанию часовой таймфрейм
    this.currentPrice, // Текущая цена (опционально)
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (prices.isEmpty) return;

    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..isAntiAlias = true;

    final path = Path();
    final priceRange = maxPrice - minPrice;

    // Создаем путь для линии графика
    if (smoothCurve && prices.length > 2) {
      // Создаем сглаженный путь с кривыми Безье
      final points = <Offset>[];

      for (int i = 0; i < prices.length; i++) {
        final x = size.width * i / (prices.length - 1);
        final normalizedPrice = (prices[i] - minPrice) / priceRange;
        final y = size.height - (normalizedPrice * size.height);
        points.add(Offset(x, y));
      }

      path.moveTo(points[0].dx, points[0].dy);

      for (int i = 0; i < points.length - 1; i++) {
        final p0 = points[i];
        final p1 = points[i + 1];

        // Вычисляем контрольные точки для сглаживания
        final controlX1 = p0.dx + (p1.dx - p0.dx) / 2;
        final controlY1 = p0.dy;
        final controlX2 = p0.dx + (p1.dx - p0.dx) / 2;
        final controlY2 = p1.dy;

        // Добавляем кубическую кривую Безье
        path.cubicTo(controlX1, controlY1, controlX2, controlY2, p1.dx, p1.dy);
      }
    } else {
      // Обычный линейный путь
      for (int i = 0; i < prices.length; i++) {
        final x = size.width * i / (prices.length - 1);
        final normalizedPrice = (prices[i] - minPrice) / priceRange;
        final y = size.height - (normalizedPrice * size.height);

        if (i == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }
    }

    // Если нужен градиент, создаем путь для заливки
    if (showGradient) {
      final gradientPath = Path.from(path);
      gradientPath.lineTo(size.width, size.height);
      gradientPath.lineTo(0, size.height);
      gradientPath.close();

      // Создаем градиентную заливку
      final gradientPaint = Paint()
        ..shader = LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            color.withAlpha(76), // 0.3 * 255 = 76
            color.withAlpha(13), // 0.05 * 255 = 13
          ],
        ).createShader(Rect.fromLTWH(0, 0, size.width, size.height))
        ..style = PaintingStyle.fill;

      // Рисуем градиентную заливку
      canvas.drawPath(gradientPath, gradientPaint);
    }

    // Рисуем линию
    canvas.drawPath(path, paint);

    // Отображаем текущую цену, если она предоставлена
    if (currentPrice != null) {
      // Проверяем, находится ли текущая цена в пределах видимого диапазона
      if (currentPrice! >= minPrice && currentPrice! <= maxPrice) {
        // Вычисляем позицию Y для текущей цены
        final y = size.height - ((currentPrice! - minPrice) / priceRange * size.height);

        // Рисуем горизонтальную линию текущей цены
        final linePaint = Paint()
          ..color = color
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke;

        // Рисуем пунктирную линию
        final dashWidth = 5.0;
        final dashSpace = 3.0;
        double startX = 0;

        while (startX < size.width) {
          canvas.drawLine(
            Offset(startX, y),
            Offset(startX + dashWidth, y),
            linePaint,
          );
          startX += dashWidth + dashSpace;
        }

        // Рисуем метку текущей цены
        final bgPaint = Paint()
          ..color = Colors.black.withOpacity(0.7)
          ..style = PaintingStyle.fill;

        final pricePainter = TextPainter(
          textDirection: ui.TextDirection.ltr,
          textAlign: TextAlign.right,
        );

        pricePainter.text = TextSpan(
          text: currentPrice!.toStringAsFixed(2),
          style: TextStyle(color: color, fontSize: 10, fontWeight: FontWeight.bold),
        );

        pricePainter.layout();

        // Рисуем фон для текста
        final textBgRect = Rect.fromLTWH(
          size.width - pricePainter.width - 8,
          y - pricePainter.height / 2 - 2,
          pricePainter.width + 8,
          pricePainter.height + 4,
        );

        canvas.drawRect(textBgRect, bgPaint);

        // Рисуем текст
        pricePainter.paint(canvas, Offset(size.width - pricePainter.width - 4, y - pricePainter.height / 2));
      }
    }

    // Рисуем горизонтальные линии сетки
    final gridPaint = Paint()
      ..color = Colors.grey.withAlpha(50)
      ..strokeWidth = 1.0;

    for (int i = 1; i < 5; i++) {
      final y = size.height * i / 5;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
    }

    // Рисуем вертикальные линии сетки
    for (int i = 1; i < 6; i++) {
      final x = size.width * i / 6;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), gridPaint);
    }

    // Рисуем метки цен справа
    final textStyle = TextStyle(
      color: Colors.grey.shade400,
      fontSize: 12,
    );
    final textPainter = TextPainter(
      textDirection: ui.TextDirection.ltr,
    );

    // Рисуем метки цен
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      final price = minPrice + (priceRange * (4 - i) / 4);

      textPainter.text = TextSpan(
        text: price.toInt().toString(),
        style: textStyle,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(size.width - textPainter.width - 4, y - textPainter.height / 2),
      );
    }

    // Рисуем метки дат внизу
    if (dates.isNotEmpty) {
      // Определяем количество меток в зависимости от таймфрейма
      int labelCount;
      switch (timeframe) {
        case '30М': // 30 минут
          labelCount = 6; // Показываем 6 меток для 30-минутного графика
          break;
        case '1Ч': // 1 час
          labelCount = 6; // Показываем 6 меток для часового графика
          break;
        case '4Ч': // 4 часа
          labelCount = 7; // Показываем 7 меток для 4-часового графика
          break;
        case '1Д': // 1 день
          labelCount = 8; // Показываем 8 меток для дневного графика
          break;
        case '1Н': // 1 неделя
          labelCount = 6; // Показываем 6 меток для недельного графика
          break;
        default:
          labelCount = 6;
      }

      // Выбираем равномерно распределенные метки
      final visibleDates = dates.length > labelCount ?
          _selectEvenlyDistributedLabels(dates, labelCount) :
          dates;

      for (int i = 0; i < visibleDates.length; i++) {
        final x = i == 0 ? 0 : size.width * i / (visibleDates.length - 1);

        textPainter.text = TextSpan(
          text: visibleDates[i],
          style: textStyle,
        );

        textPainter.layout();
        textPainter.paint(
          canvas,
          Offset(x - textPainter.width / 2, size.height + 4),
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;

  // Вспомогательный метод для выбора равномерно распределенных меток
  List<String> _selectEvenlyDistributedLabels(List<String> allLabels, int count) {
    if (allLabels.length <= count) return allLabels;

    List<String> result = [];
    // Всегда добавляем первую метку
    result.add(allLabels.first);

    // Вычисляем шаг для равномерного распределения
    final step = (allLabels.length - 1) / (count - 1);

    // Добавляем промежуточные метки
    for (int i = 1; i < count - 1; i++) {
      final index = (i * step).round();
      if (index < allLabels.length) {
        result.add(allLabels[index]);
      }
    }

    // Всегда добавляем последнюю метку
    result.add(allLabels.last);

    return result;
  }
}

// Резервный painter для графика
class FallbackChartPainter extends CustomPainter {
  final Color color;
  final bool isPositive;

  FallbackChartPainter({required this.color, required this.isPositive});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    final path = Path();
    final random = math.Random(42); // Фиксированное зерно для стабильных результатов

    // Генерируем случайные точки для графика
    final points = <Offset>[];
    final pointCount = 100;

    for (int i = 0; i < pointCount; i++) {
      final x = size.width * i / (pointCount - 1);

      // Создаем реалистичное движение цены
      double normalizedY;
      if (isPositive) {
        normalizedY = 0.7 - 0.4 * math.sin(i / 10) + random.nextDouble() * 0.1;
      } else {
        normalizedY = 0.3 + 0.4 * math.sin(i / 10) + random.nextDouble() * 0.1;
      }

      final y = size.height * normalizedY;
      points.add(Offset(x, y));
    }

    // Создаем путь
    path.moveTo(points.first.dx, points.first.dy);
    for (int i = 1; i < points.length; i++) {
      path.lineTo(points[i].dx, points[i].dy);
    }

    // Рисуем линию
    canvas.drawPath(path, paint);

    // Рисуем горизонтальные линии сетки
    final gridPaint = Paint()
      ..color = Colors.grey.withAlpha(50)
      ..strokeWidth = 1.0;

    for (int i = 1; i < 5; i++) {
      final y = size.height * i / 5;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
    }

    // Рисуем вертикальные линии сетки
    for (int i = 1; i < 6; i++) {
      final x = size.width * i / 6;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), gridPaint);
    }

    // Рисуем метки цен справа
    final textStyle = TextStyle(
      color: Colors.grey.shade400,
      fontSize: 12,
    );
    final textPainter = TextPainter(
      textDirection: ui.TextDirection.ltr,
    );

    // Базовая цена (середина графика)
    final basePrice = 41000.0;

    for (int i = 0; i < 5; i++) {
      final y = size.height * i / 4;
      final price = basePrice + (2 - i) * 100;

      textPainter.text = TextSpan(
        text: price.toStringAsFixed(0),
        style: textStyle,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(size.width - textPainter.width - 4, y - textPainter.height / 2),
      );
    }

    // Рисуем метки дат внизу
    final dates = ['Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб', 'Вс'];

    for (int i = 0; i < dates.length; i++) {
      final x = size.width * i / (dates.length - 1);

      textPainter.text = TextSpan(
        text: dates[i],
        style: textStyle,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(x - textPainter.width / 2, size.height + 4),
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
