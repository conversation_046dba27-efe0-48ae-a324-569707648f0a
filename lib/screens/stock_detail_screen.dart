import 'package:flutter/material.dart';
import '../models/crypto_currency.dart';
import 'dart:math' as math;

class StockDetailScreen extends StatefulWidget {
  final CryptoCurrency crypto;

  const StockDetailScreen({super.key, required this.crypto});

  @override
  State<StockDetailScreen> createState() => _StockDetailScreenState();
}

class _StockDetailScreenState extends State<StockDetailScreen> {
  String _selectedTimeframe = '1Д';
  bool _isLoading = false;
  List<double> _pricePoints = [];
  double _minPrice = 0;
  double _maxPrice = 0;

  @override
  void initState() {
    super.initState();
    _generateChartData();
  }

  void _generateChartData() {
    setState(() {
      _isLoading = true;
    });

    // Генерируем данные для графика на основе базовой цены
    final basePrice = widget.crypto.price;
    final random = math.Random();
    _pricePoints = [];

    // Количество точек зависит от выбранного таймфрейма
    int pointCount;
    switch (_selectedTimeframe) {
      case '1Д':
        pointCount = 24; // 24 часа
        break;
      case '1Н':
        pointCount = 7; // 7 дней
        break;
      case '1М':
        pointCount = 30; // 30 дней
        break;
      case '3М':
        pointCount = 90; // 90 дней
        break;
      case '6М':
        pointCount = 180; // 180 дней
        break;
      case 'Текущий год':
        pointCount = 365; // 365 дней
        break;
      default:
        pointCount = 24;
    }

    // Генерируем случайные цены с трендом
    double trend = widget.crypto.priceChangePercentage24h >= 0 ? 0.0001 : -0.0001;
    double volatility = 0.005;
    double currentPrice = basePrice * 0.95; // Начинаем с цены немного ниже текущей

    for (int i = 0; i < pointCount; i++) {
      // Добавляем случайное изменение с трендом
      double change = trend + (random.nextDouble() - 0.5) * volatility;
      currentPrice = currentPrice * (1 + change);
      _pricePoints.add(currentPrice);
    }

    // Находим минимальную и максимальную цены для масштабирования графика
    _minPrice = _pricePoints.reduce((a, b) => a < b ? a : b);
    _maxPrice = _pricePoints.reduce((a, b) => a > b ? a : b);

    // Добавляем отступ для лучшего отображения
    double padding = (_maxPrice - _minPrice) * 0.1;
    _minPrice -= padding;
    _maxPrice += padding;

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final isPositive = widget.crypto.priceChangePercentage24h >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;
    final changeSign = isPositive ? '+' : '';

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_horiz, color: Colors.white),
            onPressed: () {},
          ),
          IconButton(
            icon: const Icon(Icons.close, color: Colors.white),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Заголовок с названием и ценой
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.crypto.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  widget.crypto.symbol,
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Text(
                      '\$${_formatPrice(widget.crypto.price)}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 28,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '$changeSign${widget.crypto.priceChangePercentage24h.toStringAsFixed(2)}%',
                      style: TextStyle(
                        color: changeColor,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Text(
                  'При закрытии',
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 14,
                  ),
                ),
                Text(
                  '${widget.crypto.symbol} · USD',
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),

          // Временные интервалы
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 16.0),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  _buildTimeframeButton('1Д'),
                  _buildTimeframeButton('1Н'),
                  _buildTimeframeButton('1М'),
                  _buildTimeframeButton('3М'),
                  _buildTimeframeButton('6М'),
                  _buildTimeframeButton('Текущий год'),
                ],
              ),
            ),
          ),

          // График
          Expanded(
            child: _isLoading
                ? const Center(child: CircularProgressIndicator())
                : _buildChart(),
          ),

          // Информация о торгах
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Открытие',
                      style: TextStyle(color: Colors.grey[500]),
                    ),
                    Text(
                      '\$${_formatPrice(widget.crypto.price * 0.99)}',
                      style: const TextStyle(color: Colors.white),
                    ),
                    Text(
                      'Объем',
                      style: TextStyle(color: Colors.grey[500]),
                    ),
                    Text(
                      '${_formatLargeNumber(widget.crypto.volume24h)}',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Максимум',
                      style: TextStyle(color: Colors.grey[500]),
                    ),
                    Text(
                      '\$${_formatPrice(widget.crypto.price * 1.02)}',
                      style: const TextStyle(color: Colors.white),
                    ),
                    Text(
                      'Цена/приб.',
                      style: TextStyle(color: Colors.grey[500]),
                    ),
                    const Text(
                      '—',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Минимум',
                      style: TextStyle(color: Colors.grey[500]),
                    ),
                    Text(
                      '\$${_formatPrice(widget.crypto.price * 0.98)}',
                      style: const TextStyle(color: Colors.white),
                    ),
                    Text(
                      'Рын. кап.',
                      style: TextStyle(color: Colors.grey[500]),
                    ),
                    Text(
                      '\$${_formatLargeNumber(widget.crypto.marketCap)}',
                      style: const TextStyle(color: Colors.white),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeframeButton(String timeframe) {
    final isSelected = _selectedTimeframe == timeframe;
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: ElevatedButton(
        onPressed: () {
          setState(() {
            _selectedTimeframe = timeframe;
            _generateChartData();
          });
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: isSelected ? Colors.white.withAlpha(30) : Colors.transparent,
          foregroundColor: isSelected ? Colors.white : Colors.grey,
          elevation: 0,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        ),
        child: Text(timeframe),
      ),
    );
  }

  Widget _buildChart() {
    return CustomPaint(
      size: Size.infinite,
      painter: ChartPainter(
        prices: _pricePoints,
        minPrice: _minPrice,
        maxPrice: _maxPrice,
        color: widget.crypto.priceChangePercentage24h >= 0 ? Colors.green : Colors.red,
      ),
    );
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(2);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  String _formatLargeNumber(double number) {
    if (number >= 1000000000) {
      return '${(number / 1000000000).toStringAsFixed(1)} млрд';
    } else if (number >= 1000000) {
      return '${(number / 1000000).toStringAsFixed(1)} млн';
    } else if (number >= 1000) {
      return '${(number / 1000).toStringAsFixed(1)} тыс';
    } else {
      return number.toString();
    }
  }
}

class ChartPainter extends CustomPainter {
  final List<double> prices;
  final double minPrice;
  final double maxPrice;
  final Color color;

  ChartPainter({
    required this.prices,
    required this.minPrice,
    required this.maxPrice,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (prices.isEmpty) return;

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0;

    final fillPaint = Paint()
      ..color = color.withAlpha(50)
      ..style = PaintingStyle.fill;

    final gridPaint = Paint()
      ..color = Colors.grey.withAlpha(50)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    // Рисуем горизонтальные линии сетки
    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
    }

    // Рисуем вертикальные линии сетки
    for (int i = 0; i <= 6; i++) {
      final x = size.width * i / 6;
      canvas.drawLine(Offset(x, 0), Offset(x, size.height), gridPaint);
    }

    // Рисуем метки цен
    final textStyle = TextStyle(color: Colors.grey[400], fontSize: 12);
    final textPainter = TextPainter(
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.right,
    );

    // Рисуем 4 метки цен
    for (int i = 0; i <= 3; i++) {
      final y = size.height * i / 3;
      final price = maxPrice - (maxPrice - minPrice) * i / 3;
      
      textPainter.text = TextSpan(
        text: price.toStringAsFixed(0),
        style: textStyle,
      );
      
      textPainter.layout();
      textPainter.paint(canvas, Offset(size.width - textPainter.width - 4, y - textPainter.height / 2));
    }

    // Рисуем линию графика
    final path = Path();
    final fillPath = Path();
    
    final xStep = size.width / (prices.length - 1);
    final priceRange = maxPrice - minPrice;
    
    // Начальная точка
    final firstPoint = Offset(
      0,
      size.height - ((prices[0] - minPrice) / priceRange * size.height),
    );
    
    path.moveTo(firstPoint.dx, firstPoint.dy);
    fillPath.moveTo(firstPoint.dx, size.height);
    fillPath.lineTo(firstPoint.dx, firstPoint.dy);
    
    // Остальные точки
    for (int i = 1; i < prices.length; i++) {
      final x = xStep * i;
      final y = size.height - ((prices[i] - minPrice) / priceRange * size.height);
      path.lineTo(x, y);
      fillPath.lineTo(x, y);
    }
    
    // Закрываем путь для заливки
    fillPath.lineTo(size.width, size.height);
    fillPath.close();
    
    // Рисуем заливку и линию
    canvas.drawPath(fillPath, fillPaint);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}
