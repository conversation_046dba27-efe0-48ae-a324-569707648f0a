import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/app_bottom_navigation.dart';
import '../providers/crypto_provider.dart';
import '../widgets/mini_chart.dart';
import '../models/crypto_currency.dart';
import '../services/local_crypto_icons_service.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import '../widgets/cosmic_background.dart';
import 'coin_detail_screen.dart';

class ChartsScreen extends StatefulWidget {
  const ChartsScreen({Key? key}) : super(key: key);

  @override
  State<ChartsScreen> createState() => _ChartsScreenState();
}

class _ChartsScreenState extends State<ChartsScreen> with WidgetsBindingObserver {
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();

  // Время последнего обновления данных
  late DateTime _lastUpdateTime;

  // Таймер для автоматического обновления данных
  Timer? _refreshTimer;

  // Таймер для автоматической прокрутки бегущей строки
  Timer? _scrollTimer;

  // Флаг для отслеживания состояния приложения
  bool _isAppActive = true;

  // Текущий выбранный фильтр
  String _selectedFilter = 'All';

  // Список криптовалют для отображения
  List<CryptoCurrency> _displayedCryptos = [];

  // Переменная для переключения количества колонок (2 или 3)
  int _columnsCount = 2;

  String _searchQuery = '';
  List<CryptoCurrency> _searchResults = [];
  bool _isSearching = false;
  OverlayEntry? _overlayEntry;

  @override
  void initState() {
    super.initState();
    _lastUpdateTime = DateTime.now();

    // Регистрируем наблюдатель за жизненным циклом приложения
    WidgetsBinding.instance.addObserver(this);

    // Слушатель для поля поиска
    _searchController.addListener(_onSearchChanged);
    _searchFocusNode.addListener(_onSearchFocusChanged);

    // Загружаем данные о криптовалютах при инициализации
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);
      if (cryptoProvider.allCryptos.isEmpty && !cryptoProvider.isLoading) {
        cryptoProvider.loadAllCryptos();
      }

      // Запускаем автоматическое обновление данных
      _startPeriodicDataRefresh();

      // Запускаем автоматическую прокрутку бегущей строки
      _startAutoScroll();
    });
  }

  @override
  void dispose() {
    // Отменяем таймеры при уничтожении виджета
    _refreshTimer?.cancel();
    _scrollTimer?.cancel();

    // Отписываемся от наблюдения за жизненным циклом
    WidgetsBinding.instance.removeObserver(this);

    // Очищаем ресурсы поиска
    _searchController.dispose();
    _searchFocusNode.dispose();
    _removeOverlay();

    _scrollController.dispose();
    super.dispose();
  }



  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Отслеживаем состояние приложения для оптимизации обновлений
    if (state == AppLifecycleState.resumed) {
      // Приложение стало активным
      _isAppActive = true;

      // Немедленно обновляем данные
      _refreshData();

      // Перезапускаем таймеры
      _startPeriodicDataRefresh();
      _startAutoScroll();
    } else if (state == AppLifecycleState.paused ||
               state == AppLifecycleState.inactive ||
               state == AppLifecycleState.detached) {
      // Приложение стало неактивным
      _isAppActive = false;

      // Останавливаем таймеры для экономии ресурсов
      _refreshTimer?.cancel();
      _scrollTimer?.cancel();
    }
  }

  // Метод для запуска периодического обновления данных
  void _startPeriodicDataRefresh() {
    // Отменяем существующий таймер, если он есть
    _refreshTimer?.cancel();

    // Создаем новый таймер, который будет срабатывать каждую минуту
    _refreshTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      if (_isAppActive) {
        _refreshData();
      }
    });
  }

  // Метод для обновления данных
  void _refreshData() {
    if (!mounted) return;

    final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);
    cryptoProvider.loadAllCryptos();

    // Обновляем время последнего обновления
    setState(() {
      _lastUpdateTime = DateTime.now();
    });
  }

  // Метод для запуска автоматической прокрутки бегущей строки
  void _startAutoScroll() {
    // Отменяем существующий таймер, если он есть
    _scrollTimer?.cancel();

    // Задержка перед началом прокрутки для инициализации
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;

      // Проверяем, что контроллер прокрутки имеет клиентов
      if (_scrollController.hasClients) {
        // Создаем таймер для более частой и плавной прокрутки
        _scrollTimer = Timer.periodic(const Duration(seconds: 3), (timer) {
          if (!mounted || !_isAppActive) {
            timer.cancel();
            return;
          }

          if (_scrollController.hasClients) {
            // Получаем текущую позицию и максимальную позицию
            final currentOffset = _scrollController.offset;
            final maxScrollExtent = _scrollController.position.maxScrollExtent;
            
            // Вычисляем следующую позицию прокрутки
            final nextOffset = currentOffset + 150;
            
            // Проверяем, если осталось меньше двух прокруток до конца
            final remainingScrolls = (maxScrollExtent - currentOffset) / 150;
            
            if (remainingScrolls <= 2) {
              // Предзагружаем новые элементы, плавно перемещаясь в начало списка
              // но продолжая движение в том же направлении
              final newPosition = currentOffset - (maxScrollExtent * 0.3);
              _scrollController.jumpTo(math.max(0, newPosition));
            } else {
              // Плавно прокручиваем к следующей позиции
              _scrollController.animateTo(
                nextOffset,
                duration: const Duration(milliseconds: 1200),
                curve: Curves.easeInOut,
              );
            }
          }
        });

        // Начинаем прокрутку сразу
        _scrollController.animateTo(
          150,
          duration: const Duration(milliseconds: 1200),
          curve: Curves.easeInOut,
        );
      } else {
        // Если контроллер еще не имеет клиентов, пробуем позже
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) _startAutoScroll();
        });
      }
    });
  }

  // Метод для создания фильтра с современным дизайном в стиле iOS
  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFilter = label;
          _updateDisplayedCryptos();
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        margin: const EdgeInsets.only(right: 8.0),
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF007AFF) // iOS blue
              : Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.circular(20.0),
          border: Border.all(
            color: isSelected 
                ? const Color(0xFF007AFF)
                : Colors.white.withOpacity(0.2),
            width: 1.5,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: const Color(0xFF007AFF).withOpacity(0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                  BoxShadow(
                    color: const Color(0xFF007AFF).withOpacity(0.2),
                    blurRadius: 6,
                    offset: const Offset(0, 2),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.white.withOpacity(0.8),
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
            fontSize: 14.0,
            letterSpacing: 0.3,
          ),
        ),
      ),
    );
  }

  // Метод для обновления отображаемых криптовалют в зависимости от выбранного фильтра
  void _updateDisplayedCryptos() {
    final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);

    switch (_selectedFilter) {
      case 'Favorites':
        _displayedCryptos = cryptoProvider.favoritesCryptos;
        break;
      case 'Trending':
        // Логика для трендовых токенов: популярность, объем торгов, новые листинги
        // Сортируем по комбинации объема торгов и рыночной капитализации (показатель популярности)
        _displayedCryptos = List.from(cryptoProvider.allCryptos)
          ..sort((a, b) {
            // Вычисляем "трендовость" как комбинацию объема торгов и рыночной капитализации
            double trendScoreA = (a.volume24h / a.marketCap) * 1000; // Нормализуем
            double trendScoreB = (b.volume24h / b.marketCap) * 1000;

            // Добавляем бонус для AI и Meme токенов
            if (a.categories.contains('AI Agents') || a.categories.contains('Memes')) {
              trendScoreA *= 1.5;
            }
            if (b.categories.contains('AI Agents') || b.categories.contains('Memes')) {
              trendScoreB *= 1.5;
            }

            return trendScoreB.compareTo(trendScoreA);
          });

        // Берем топ-50 самых трендовых
        _displayedCryptos = _displayedCryptos.take(50).toList();
        break;

      case 'Gainers':
        // Логика для Gainers: только токены с положительным ростом, отсортированные по проценту роста
        _displayedCryptos = cryptoProvider.allCryptos
          .where((crypto) => (crypto.priceChangePercentage24h ?? 0) > 0) // Только растущие
          .toList()
          ..sort((a, b) => (b.priceChangePercentage24h ?? 0).compareTo(a.priceChangePercentage24h ?? 0)); // Сортируем по убыванию роста
        break;
      case 'Losers':
        _displayedCryptos = cryptoProvider.allCryptos
          .where((crypto) => (crypto.priceChangePercentage24h ?? 0) < 0) // Только падающие (с проверкой на null)
          .toList()
          ..sort((a, b) => (a.priceChangePercentage24h ?? 0).compareTo(b.priceChangePercentage24h ?? 0)); // Сортируем по возрастанию убытка
        break;
      case 'AI':
        _displayedCryptos = cryptoProvider.aiAgentsCryptos;
        break;
      case 'Memes':
        _displayedCryptos = cryptoProvider.memesCryptos;
        break;
      case 'All':
      default:
        _displayedCryptos = cryptoProvider.allCryptos;
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CryptoProvider>(
      builder: (context, cryptoProvider, _) {
        // Обновляем список отображаемых криптовалют
        if (_displayedCryptos.isEmpty) {
          _updateDisplayedCryptos();
        }

        final cryptos = cryptoProvider.allCryptos;

        // Подсчёт процента растущих и падающих
        int upCount = cryptos.where((c) => c.priceChangePercentage24h > 0).length;
        int downCount = cryptos.where((c) => c.priceChangePercentage24h < 0).length;
        int total = cryptos.length;
        double upPercent = total > 0 ? upCount / total : 0;
        double downPercent = total > 0 ? downCount / total : 0;

        // Определяем цвет настроения для фона
        Color sentimentColor;
        if (upPercent > 0.6) {
          sentimentColor = const Color(0xFF34C759); // Зеленый
        } else if (downPercent > 0.4) {
          sentimentColor = const Color(0xFFFF3B30); // Красный
        } else {
          sentimentColor = const Color(0xFF4A90E2); // Синий
        }

        return Scaffold(
          backgroundColor: const Color(0xFF0A0B0D),
          extendBodyBehindAppBar: true,
          extendBody: true,
          body: Stack(
            children: [
              // Анимированный фон в стиле Mintlify
              _buildAnimatedMintlifyBackground(sentimentColor),

              // Основной контент
              SafeArea(
                child: Column(
                  children: [
                    // Верхняя панель с фильтрами и кнопками управления
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                      child: Column(
                        children: [
                          // Первая строка - фильтры и управляющие кнопки
                          Row(
                            children: [
                              // Переключатель колонок (вместо кнопки назад)
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.3),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: Colors.white.withOpacity(0.2), width: 1.5),
                                ),
                                child: IconButton(
                                  icon: Icon(
                                    _columnsCount == 2 ? Icons.view_column : Icons.view_agenda,
                                    color: Colors.white,
                                    size: 22,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _columnsCount = _columnsCount == 2 ? 3 : 2;
                                    });
                                  },
                                ),
                              ),

                              const SizedBox(width: 12),

                              // Фильтры (перемещены сюда)
                              SingleChildScrollView(
                                scrollDirection: Axis.horizontal,
                                child: Row(
                                  children: [
                                    _buildPremiumFilterChip('All', _selectedFilter == 'All'),
                                    _buildPremiumFilterChip('Trending', _selectedFilter == 'Trending'),
                                    _buildPremiumFilterChip('Gainers', _selectedFilter == 'Gainers'),
                                    _buildPremiumFilterChip('Losers', _selectedFilter == 'Losers'),
                                    _buildPremiumFilterChip('AI', _selectedFilter == 'AI'),
                                    _buildPremiumFilterChip('Memes', _selectedFilter == 'Memes'),
                                    
                                    const SizedBox(width: 12.0), // Такой же отступ как между другими кнопками
                                    
                                    // Поле поиска сразу после Memes
                                    Container(
                                      width: 280,
                                      height: 44,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            Colors.white.withOpacity(0.08),
                                            Colors.white.withOpacity(0.04),
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(25.0),
                                        border: Border.all(
                                          color: Colors.white.withOpacity(0.15),
                                          width: 1.0,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withOpacity(0.15),
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ],
                                      ),
                                      child: TextField(
                                        controller: _searchController,
                                        focusNode: _searchFocusNode,
                                        onChanged: (value) {
                                          // Добавляем setState для обновления UI при изменении текста
                                          setState(() {});
                                          _onSearchChanged();
                                        },
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontSize: 14,
                                          fontWeight: FontWeight.w600, // Увеличил жирность шрифта
                                        ),
                                        decoration: InputDecoration(
                                          hintText: 'Поиск активов...',
                                          hintStyle: TextStyle(
                                            color: Colors.white.withOpacity(0.6),
                                            fontSize: 14,
                                            fontWeight: FontWeight.w500, // Увеличил жирность
                                          ),
                                          prefixIcon: Icon(
                                            Icons.search,
                                            color: Colors.white.withOpacity(0.7),
                                            size: 20,
                                          ),
                                          suffixIcon: _searchController.text.isNotEmpty
                                              ? IconButton(
                                                  icon: Icon(
                                                    Icons.clear,
                                                    color: Colors.white.withOpacity(0.7),
                                                    size: 18,
                                                  ),
                                                  onPressed: () {
                                                    setState(() {
                                                      _searchController.clear();
                                                      _isSearching = false;
                                                      _searchResults.clear();
                                                    });
                                                    _removeOverlay();
                                                    _searchFocusNode.unfocus();
                                                  },
                                                )
                                              : null,
                                          border: InputBorder.none,
                                          contentPadding: const EdgeInsets.symmetric(
                                            horizontal: 16,
                                            vertical: 12,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              const Spacer(),

                              // Время последнего обновления
                              Container(
                                margin: const EdgeInsets.only(right: 8),
                                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.4),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: Colors.white.withOpacity(0.2), width: 1),
                                  boxShadow: [
                                    BoxShadow(
                                      color: sentimentColor.withOpacity(0.2),
                                      blurRadius: 8,
                                      offset: const Offset(0, 2),
                                    ),
                                  ],
                                ),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.access_time,
                                      color: Colors.white.withOpacity(0.8),
                                      size: 14,
                                    ),
                                    const SizedBox(width: 6),
                                    Text(
                                      _getFormattedDateTime(),
                                      style: TextStyle(
                                        color: Colors.white.withOpacity(0.9),
                                        fontSize: 12,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // Избранное
                              Container(
                                margin: const EdgeInsets.only(right: 8),
                                decoration: BoxDecoration(
                                  color: _selectedFilter == 'Favorites' 
                                      ? Colors.amber.withOpacity(0.2)
                                      : Colors.black.withOpacity(0.3),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(
                                    color: _selectedFilter == 'Favorites' 
                                        ? Colors.amber.withOpacity(0.6)
                                        : Colors.white.withOpacity(0.2), 
                                    width: 1.5
                                  ),
                                  boxShadow: _selectedFilter == 'Favorites' 
                                      ? [
                                          BoxShadow(
                                            color: Colors.amber.withOpacity(0.3),
                                            blurRadius: 8,
                                            offset: const Offset(0, 2),
                                          ),
                                        ]
                                      : [],
                                ),
                                child: IconButton(
                                  icon: Icon(
                                    _selectedFilter == 'Favorites' ? Icons.star_rounded : Icons.star_border_rounded,
                                    color: _selectedFilter == 'Favorites' ? Colors.amber : Colors.white,
                                    size: 20,
                                  ),
                                  onPressed: () {
                                    setState(() {
                                      _selectedFilter = _selectedFilter == 'Favorites' ? 'All' : 'Favorites';
                                      _updateDisplayedCryptos();
                                    });
                                  },
                                ),
                              ),

                              // Обновление
                              Container(
                                decoration: BoxDecoration(
                                  color: Colors.black.withOpacity(0.3),
                                  borderRadius: BorderRadius.circular(12),
                                  border: Border.all(color: Colors.white.withOpacity(0.2), width: 1.5),
                                ),
                                child: IconButton(
                                  icon: const Icon(Icons.refresh, color: Colors.white, size: 22),
                                  onPressed: () {
                                    cryptoProvider.loadAllCryptos();
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      const SnackBar(
                                        content: Text('Refreshing data...'),
                                        duration: Duration(seconds: 1),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Карусель криптовалют с улучшенной анимацией
                    Container(
                      height: 120, // Делаем компактнее
                      margin: const EdgeInsets.only(top: 8), // Минимальный отступ
                      child: ListView.builder(
                        controller: _scrollController,
                        scrollDirection: Axis.horizontal,
                        itemCount: cryptos.length * 3, // Больше элементов для бесконечности
                        itemBuilder: (context, index) {
                          final crypto = cryptos[index % cryptos.length];
                          final isPositive = crypto.priceChangePercentage24h >= 0;
                          final changeSign = isPositive ? '+' : '';
                          final changeColor = isPositive ? sentimentColor : const Color(0xFFFF4444);

                          return GestureDetector(
                            onTap: () async {
                              print('🔍 Клик по криптовалюте в списке: ${crypto.name} (${crypto.symbol})');
                              
                              try {
                                await Navigator.of(context).pushNamed(
                                  '/coin_detail',
                                  arguments: crypto,
                                );
                                print('✅ Навигация из списка успешна');
                              } catch (error) {
                                print('❌ Ошибка навигации из списка: $error');
                                ScaffoldMessenger.of(context).showSnackBar(
                                  SnackBar(
                                    content: Text('Ошибка перехода: ${error.toString()}'),
                                    backgroundColor: Colors.red,
                                  ),
                                );
                              }
                            },
                            child: Container(
                              width: 140, // Фиксированная ширина
                              margin: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 8.0),
                              padding: const EdgeInsets.all(12.0),
                              decoration: BoxDecoration(
                                // Эффект морфизма
                                color: Colors.white.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(16.0),
                                border: Border.all(
                                  color: Colors.white.withOpacity(0.2),
                                  width: 1.5,
                                ),
                                boxShadow: [
                                  // Внешняя тень
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.3),
                                    blurRadius: 15,
                                    offset: const Offset(0, 6),
                                    spreadRadius: 1,
                                  ),
                                  // Внутренняя подсветка
                                  BoxShadow(
                                    color: Colors.white.withOpacity(0.1),
                                    blurRadius: 1,
                                    offset: const Offset(0, 1),
                                  ),
                                  // Боковые тени для глубины
                                  BoxShadow(
                                    color: sentimentColor.withOpacity(0.1),
                                    blurRadius: 10,
                                    offset: const Offset(0, 3),
                                  ),
                                ],
                                // Градиентный эффект
                                gradient: LinearGradient(
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                  colors: [
                                    Colors.white.withOpacity(0.15),
                                    Colors.white.withOpacity(0.05),
                                  ],
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  // Верхняя строка: иконка + символ
                                  Row(
                                    children: [
                                      Container(
                                        width: 24,
                                        height: 24,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Colors.grey.shade800,
                                          boxShadow: [
                                            BoxShadow(
                                              color: changeColor.withOpacity(0.3),
                                              blurRadius: 4,
                                              offset: const Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.circular(12),
                                          child: CachedNetworkImage(
                                            imageUrl: crypto.imageUrl,
                                            fit: BoxFit.cover,
                                            placeholder: (context, url) => Container(
                                              color: Colors.grey.shade800,
                                              child: const Center(
                                                child: SizedBox(
                                                  width: 10,
                                                  height: 10,
                                                  child: CircularProgressIndicator(strokeWidth: 1),
                                                ),
                                              ),
                                            ),
                                            errorWidget: (context, url, error) {
                                              return Container(
                                                color: Colors.grey.shade800,
                                                child: Center(
                                                  child: Text(
                                                    crypto.symbol.substring(0, 1),
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontWeight: FontWeight.bold,
                                                      fontSize: 10,
                                                    ),
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          crypto.symbol,
                                          style: const TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12,
                                          ),
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                      // Процент изменения справа
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                                        decoration: BoxDecoration(
                                          color: changeColor.withOpacity(0.2),
                                          borderRadius: BorderRadius.circular(4),
                                        ),
                                        child: Text(
                                          '$changeSign${crypto.priceChangePercentage24h.toStringAsFixed(1)}%',
                                          style: TextStyle(
                                            color: changeColor,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 8,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 4),

                                  // Цена
                                  Text(
                                    '\$${_formatPrice(crypto.price)}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.bold,
                                      fontSize: 11,
                                    ),
                                  ),

                                  const SizedBox(height: 6),

                                  // Мини-график внизу
                                  Expanded(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(6),
                                        color: changeColor.withOpacity(0.05),
                                      ),
                                      child: MiniChart(
                                        crypto: crypto,
                                        color: changeColor.withOpacity(0.8),
                                        strokeWidth: 1.5,
                                        showArea: true,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),

                    // Основной список криптовалют
                    Expanded(
                      child: Consumer<CryptoProvider>(
                        builder: (context, cryptoProvider, child) {
                          if (cryptoProvider.isLoading) {
                            return Center(
                              child: CircularProgressIndicator(
                                valueColor: AlwaysStoppedAnimation<Color>(sentimentColor),
                              ),
                            );
                          }

                          if (_displayedCryptos.isEmpty) {
                            return const Center(
                              child: Text(
                                'No data available',
                                style: TextStyle(color: Colors.white),
                              ),
                            );
                          }

                          return ListView.builder(
                            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                            itemCount: (_displayedCryptos.length / _columnsCount).ceil(),
                            itemBuilder: (context, index) {
                              List<Widget> rowItems = [];
                              
                              for (int i = 0; i < _columnsCount; i++) {
                                final itemIndex = index * _columnsCount + i;
                                if (itemIndex < _displayedCryptos.length) {
                                  rowItems.add(
                                    Expanded(
                                      child: _buildCryptoListItem(_displayedCryptos[itemIndex], cryptoProvider),
                                    ),
                                  );
                                  if (i < _columnsCount - 1) {
                                    rowItems.add(const SizedBox(width: 8.0));
                                  }
                                } else {
                                  rowItems.add(const Expanded(child: SizedBox.shrink()));
                                  if (i < _columnsCount - 1) {
                                    rowItems.add(const SizedBox(width: 8.0));
                                  }
                                }
                              }

                              return Padding(
                                padding: const EdgeInsets.only(bottom: 8.0),
                                child: Row(children: rowItems),
                              );
                            },
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          bottomNavigationBar: AppBottomNavigation(
            currentIndex: 1,
            backgroundDecoration: BoxDecoration(
              color: const Color(0xFF0A0B0D).withOpacity(0.95),
              border: Border(
                top: BorderSide(color: Colors.white.withOpacity(0.1), width: 1),
              ),
            ),
            onTap: (index) {
              if (index != 1) {
                switch (index) {
                  case 0:
                    Navigator.pushReplacementNamed(context, '/news');
                    break;
                  case 2:
                    Navigator.pushReplacementNamed(context, '/sinusoid');
                    break;
                  case 3:
                    Navigator.pushReplacementNamed(context, '/courses');
                    break;
                  case 4:
                    Navigator.pushReplacementNamed(context, '/profile');
                    break;
                }
              }
            },
          ),
        );
      },
    );
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  // Форматирование рыночной капитализации
  String _formatMarketCap(double marketCap) {
    if (marketCap >= 1000000000000) {
      return '\$${(marketCap / 1000000000000).toStringAsFixed(1)}T';
    } else if (marketCap >= 1000000000) {
      return '\$${(marketCap / 1000000000).toStringAsFixed(1)}B';
    } else if (marketCap >= 1000000) {
      return '\$${(marketCap / 1000000).toStringAsFixed(1)}M';
    } else {
      return '\$${marketCap.toStringAsFixed(0)}';
    }
  }

  // Получение форматированного времени последнего обновления (только часы и минуты)
  String _getFormattedDateTime() {
    return '${_lastUpdateTime.hour.toString().padLeft(2, '0')}:${_lastUpdateTime.minute.toString().padLeft(2, '0')}';
  }

  Widget _buildCryptoListItem(CryptoCurrency crypto, CryptoProvider cryptoProvider) {
    final isPositive = crypto.priceChangePercentage24h >= 0;
    final changeSign = isPositive ? '+' : '';
    final changeColor = isPositive ? const Color(0xFF00FF88) : const Color(0xFFFF4444);

    return InkWell(
      onTap: () async {
        print('🔍 Клик по криптовалюте в списке: ${crypto.name} (${crypto.symbol})');
        
        try {
          await Navigator.of(context).pushNamed(
            '/coin_detail',
            arguments: crypto,
          );
          print('✅ Навигация из списка успешна');
        } catch (error) {
          print('❌ Ошибка навигации из списка: $error');
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Ошибка перехода: ${error.toString()}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      },
      child: Container(
        padding: const EdgeInsets.all(10.0),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.4),
          borderRadius: BorderRadius.circular(12.0),
          border: Border.all(color: Colors.white.withOpacity(0.1), width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
            BoxShadow(
              color: Colors.white.withOpacity(0.05),
              blurRadius: 1,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Row(
          children: [
            // Coin icon
            Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.grey.shade800,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.3),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(15),
                child: CachedNetworkImage(
                  imageUrl: crypto.imageUrl,
                  fit: BoxFit.cover,
                  placeholder: (context, url) => Container(
                    color: Colors.grey.shade800,
                    child: const Center(
                      child: SizedBox(
                        width: 12,
                        height: 12,
                        child: CircularProgressIndicator(strokeWidth: 1),
                      ),
                    ),
                  ),
                  errorWidget: (context, url, error) {
                    final upperSymbol = crypto.symbol.trim().toUpperCase();
                    if (LocalCryptoIconsService.availableIcons.contains(upperSymbol)) {
                      return LocalCryptoIconsService.getIcon(
                        crypto.symbol,
                        size: 30,
                        borderRadius: BorderRadius.circular(15),
                      );
                    } else {
                      return Container(
                        color: Colors.grey.shade800,
                        child: Center(
                          child: Text(
                            crypto.symbol.substring(0, crypto.symbol.length > 2 ? 2 : crypto.symbol.length),
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: 11,
                            ),
                          ),
                        ),
                      );
                    }
                  },
                  cacheManager: DefaultCacheManager(),
                  maxWidthDiskCache: 60,
                  maxHeightDiskCache: 60,
                  memCacheWidth: 30,
                  memCacheHeight: 30,
                ),
              ),
            ),
            const SizedBox(width: 12),

            // Name and symbol
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    crypto.name,
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                      shadows: [
                        Shadow(
                          color: Colors.black54,
                          blurRadius: 2,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    crypto.symbol.toUpperCase(),
                    style: TextStyle(
                      color: Colors.white.withOpacity(0.7),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

            // Mini chart (moved left)
            Container(
              width: 70,
              height: 28,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: changeColor.withOpacity(0.3), width: 1),
                color: changeColor.withOpacity(0.05),
              ),
              child: MiniChart(
                crypto: crypto,
                color: changeColor.withOpacity(0.8),
                strokeWidth: 2.0,
                showArea: false,
              ),
            ),
            const SizedBox(width: 10),
            
            // Price and change percentage (moved right)
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Text(
                  '\$${_formatPrice(crypto.price)}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                    shadows: [
                      Shadow(
                        color: Colors.black54,
                        blurRadius: 2,
                        offset: Offset(0, 1),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 2),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: changeColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: changeColor.withOpacity(0.6),
                      width: 1,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: changeColor.withOpacity(0.3),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Text(
                    '$changeSign${crypto.priceChangePercentage24h.toStringAsFixed(2)}%',
                    style: TextStyle(
                      color: changeColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 11,
                      shadows: const [
                        Shadow(
                          color: Colors.black54,
                          blurRadius: 1,
                          offset: Offset(0, 1),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(width: 8),

            // Favorite button (минималистичная звездочка)
            GestureDetector(
              onTap: () async {
                // Получаем актуальное состояние из провайдера
                final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);
                final currentlyFavorite = crypto.isFavorite;
                
                // Переключаем состояние избранного в провайдере
                await cryptoProvider.toggleFavorite(crypto.id);
                
                // Обновляем отображаемые списки если нужно
                setState(() {
                  if (_selectedFilter == 'Favorites') {
                    _updateDisplayedCryptos();
                  }
                });
                
                // Показываем уведомление с правильным сообщением
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(
                      !currentlyFavorite 
                          ? '${crypto.symbol} добавлен в избранное'
                          : '${crypto.symbol} удален из избранного',
                    ),
                    duration: const Duration(seconds: 1),
                    behavior: SnackBarBehavior.floating,
                    backgroundColor: !currentlyFavorite ? Colors.amber.withOpacity(0.9) : Colors.grey.withOpacity(0.9),
                  ),
                );
              },
              child: Container(
                width: 28,
                height: 28,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.transparent,
                ),
                child: Icon(
                  crypto.isFavorite ? Icons.star_rounded : Icons.star_border_rounded,
                  color: crypto.isFavorite ? Colors.amber : Colors.white.withOpacity(0.4),
                  size: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCryptoItem(CryptoCurrency crypto, CryptoProvider cryptoProvider) {
    final isPositive = crypto.priceChangePercentage24h >= 0;
    final changeSign = isPositive ? '+' : '';
    final changeColor = isPositive ? Color(0xFF00FF00) : Color(0xFFFF0000);

    return InkWell(
      onTap: () {
        Navigator.pushNamed(
          context,
          '/coin_detail',
          arguments: crypto,
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 3.0),
        decoration: BoxDecoration(
          color: const Color(0xFF1A1D23).withOpacity(0.5),
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(color: Colors.black.withOpacity(0.5), width: 1),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Верхняя часть с иконкой и названием
            Row(
              children: [
                // Coin icon
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey.shade800,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.network(
                      crypto.imageUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey.shade800,
                          child: Center(
                            child: Text(
                              crypto.symbol.substring(0, crypto.symbol.length > 2 ? 2 : crypto.symbol.length),
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 6),

                // Название и символ
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        crypto.name,
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          shadows: [Shadow(color: Colors.black54, blurRadius: 2, offset: Offset(1,1))],
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      Row(
                        children: [
                          Text(
                            crypto.symbol,
                            style: TextStyle(
                              color: Colors.grey.shade400,
                              fontSize: 10,
                            ),
                          ),
                          const SizedBox(width: 4),
                          // Индикатор рыночной капитализации
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 1),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.5),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              '${_formatMarketCap(crypto.marketCap)}',
                              style: TextStyle(
                                color: Colors.grey.shade400,
                                fontSize: 9,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Favorite button
                GestureDetector(
                  onTap: () {
                    cryptoProvider.toggleFavorite(crypto.id);
                  },
                  child: Icon(
                    crypto.isFavorite ? Icons.star : Icons.star_border,
                    color: crypto.isFavorite ? Colors.amber : Colors.grey.shade600,
                    size: 16,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 4),

            // Нижняя часть с ценой и изменением
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Цена
                Text(
                  '\$${_formatPrice(crypto.price)}',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                    shadows: [Shadow(color: Colors.black54, blurRadius: 2, offset: Offset(1,1))],
                  ),
                ),

                // Изменение цены
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: isPositive ? Colors.green.withOpacity(0.8) : Colors.red.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isPositive ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                        color: Colors.white,
                        size: 14,
                      ),
                      Text(
                        '$changeSign${crypto.priceChangePercentage24h.toStringAsFixed(1)}%',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 11,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Анимированный фон в стиле Mintlify
  Widget _buildAnimatedMintlifyBackground(Color sentimentColor) {
    return Container(
      decoration: const BoxDecoration(
        color: Color(0xFF0A0B0D), // Темный базовый цвет как в Mintlify
      ),
      child: Stack(
        children: [
          // Первый orb (главный)
          Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: const Alignment(-0.3, -0.4),
                radius: 1.8,
                colors: [
                  sentimentColor.withOpacity(0.25),
                  sentimentColor.withOpacity(0.20),
                  sentimentColor.withOpacity(0.15),
                  sentimentColor.withOpacity(0.10),
                  sentimentColor.withOpacity(0.06),
                  sentimentColor.withOpacity(0.03),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.1, 0.2, 0.35, 0.5, 0.7, 1.0],
              ),
            ),
          ),
          // Второй orb (меньший)
          Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: const Alignment(0.3, -0.5),
                radius: 1.2,
                colors: [
                  sentimentColor.withOpacity(0.18),
                  sentimentColor.withOpacity(0.14),
                  sentimentColor.withOpacity(0.10),
                  sentimentColor.withOpacity(0.07),
                  sentimentColor.withOpacity(0.04),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.15, 0.3, 0.5, 0.7, 1.0],
              ),
            ),
          ),
          // Третий orb (внизу справа)
          Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: const Alignment(0.4, 0.2),
                radius: 0.9,
                colors: [
                  sentimentColor.withOpacity(0.15),
                  sentimentColor.withOpacity(0.12),
                  sentimentColor.withOpacity(0.09),
                  sentimentColor.withOpacity(0.06),
                  sentimentColor.withOpacity(0.03),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.2, 0.35, 0.55, 0.75, 1.0],
              ),
            ),
          ),
          // Четвёртый orb (внизу слева)
          Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: const Alignment(-0.7, 0.7),
                radius: 0.8,
                colors: [
                  sentimentColor.withOpacity(0.12),
                  sentimentColor.withOpacity(0.09),
                  sentimentColor.withOpacity(0.07),
                  sentimentColor.withOpacity(0.05),
                  sentimentColor.withOpacity(0.03),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.25, 0.4, 0.6, 0.8, 1.0],
              ),
            ),
          ),
          // Тонкий градиентный overlay для дополнительной глубины
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  sentimentColor.withOpacity(0.04),
                  Colors.transparent,
                  sentimentColor.withOpacity(0.03),
                  Colors.transparent,
                ],
                stops: const [0.0, 0.3, 0.7, 1.0],
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Премиальные кнопки фильтров
  Widget _buildPremiumFilterChip(String label, bool isSelected) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFilter = label;
          _updateDisplayedCryptos();
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 350),
        curve: Curves.easeInOut,
        margin: const EdgeInsets.only(right: 12.0),
        padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 12.0),
        decoration: BoxDecoration(
          gradient: isSelected
              ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    const Color(0xFF1A1A2E), // Темно-синий
                    const Color(0xFF16213E), // Еще темнее
                  ],
                )
              : LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white.withOpacity(0.08),
                    Colors.white.withOpacity(0.04),
                  ],
                ),
          borderRadius: BorderRadius.circular(25.0),
          border: Border.all(
            color: isSelected 
                ? const Color(0xFF4A90E2) // Менее яркий синий цвет вместо циана
                : Colors.white.withOpacity(0.15),
            width: isSelected ? 2.0 : 1.0, // Немного уменьшил толщину
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: const Color(0xFF4A90E2).withOpacity(0.3), // Обновленный цвет для тени
                    blurRadius: 15,
                    offset: const Offset(0, 6),
                    spreadRadius: 1,
                  ),
                  BoxShadow(
                    color: const Color(0xFF1A1A2E).withOpacity(0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ]
              : [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.15),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                  BoxShadow(
                    color: Colors.black.withOpacity(0.05),
                    blurRadius: 1,
                    offset: const Offset(0, 1),
                  ),
                ],
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.white.withOpacity(0.9),
            fontWeight: isSelected ? FontWeight.w700 : FontWeight.w600,
            fontSize: 15.0,
            letterSpacing: 0.4,
            shadows: isSelected 
                ? [
                    const Shadow(
                      color: Colors.black26,
                      blurRadius: 4,
                      offset: Offset(0, 1),
                    ),
                  ]
                : null,
          ),
        ),
      ),
    );
  }

  // Обработчик изменения текста поиска
  void _onSearchChanged() {
    final query = _searchController.text.trim().toLowerCase();
    print('Поиск: "$query"'); // Отладка
    
    if (query.isEmpty) {
      print('Запрос пустой, очищаем результаты'); // Отладка
      setState(() {
        _isSearching = false;
        _searchResults.clear();
      });
      _removeOverlay();
      return;
    }

    // Получаем все криптовалюты для поиска
    final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);
    final allCryptos = cryptoProvider.allCryptos;
    print('Всего криптовалют для поиска: ${allCryptos.length}'); // Отладка

    // Фильтруем по названию и символу
    final results = allCryptos.where((crypto) {
      return crypto.name.toLowerCase().contains(query) ||
             crypto.symbol.toLowerCase().contains(query);
    }).take(8).toList(); // Ограничиваем до 8 результатов

    print('Найдено результатов: ${results.length}'); // Отладка
    
    setState(() {
      _isSearching = true;
      _searchResults = results;
    });

    // Показываем overlay только если есть результаты
    if (_searchResults.isNotEmpty) {
      print('Показываем overlay с ${_searchResults.length} результатами'); // Отладка
      _showSearchOverlay();
    } else {
      print('Нет результатов, скрываем overlay'); // Отладка
      _removeOverlay();
    }
  }

  // Обработчик изменения фокуса поля поиска
  void _onSearchFocusChanged() {
    if (_searchFocusNode.hasFocus && _searchController.text.isNotEmpty) {
      _showSearchOverlay();
    } else {
      _removeOverlay();
    }
  }

  // Показ overlay с результатами поиска
  void _showSearchOverlay() {
    _removeOverlay(); // Убираем предыдущий overlay

    if (_searchResults.isEmpty) return;

    // Более точное позиционирование
    final double topPosition = kToolbarHeight + MediaQuery.of(context).padding.top + 16; // Под AppBar + отступ
    final double rightPosition = 16.0; // Отступ справа

    _overlayEntry = OverlayEntry(
      builder: (context) => Stack(
        children: [
          // Прозрачный фон для закрытия overlay при клике вне его
          Positioned.fill(
            child: GestureDetector(
              onTap: () => _removeOverlay(),
              child: Container(color: Colors.transparent),
            ),
          ),
          // Сам overlay
          Positioned(
            top: topPosition,
            right: rightPosition,
            child: Material(
              elevation: 20, // Увеличиваем elevation
              color: Colors.transparent,
              child: Container(
                width: 280, // Точно такая же ширина как у поискового поля
                constraints: const BoxConstraints(maxHeight: 320),
                decoration: BoxDecoration(
                  color: const Color(0xFF1A1A1A).withOpacity(0.98),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.3),
                    width: 1.5
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.8),
                      blurRadius: 30,
                      offset: const Offset(0, 10),
                      spreadRadius: 3,
                    ),
                    BoxShadow(
                      color: Colors.blue.withOpacity(0.2),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(16),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Заголовок
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          border: Border(
                            bottom: BorderSide(
                              color: Colors.white.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.search,
                              color: Colors.white.withOpacity(0.8),
                              size: 16,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              'Результаты поиска (${_searchResults.length})',
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.9),
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Список результатов
                      Flexible(
                        child: ListView.builder(
                          shrinkWrap: true,
                          padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                          itemCount: _searchResults.length,
                          itemBuilder: (context, index) {
                            final crypto = _searchResults[index];
                            final isPositive = crypto.priceChangePercentage24h >= 0;
                            final changeColor = isPositive ? const Color(0xFF00FF88) : const Color(0xFFFF4444);

                            return MouseRegion(
                              cursor: SystemMouseCursors.click,
                              onEnter: (_) {
                                print('🖱️ Mouse enter на результат поиска: ${crypto.name}');
                                setState(() {});
                              },
                              onExit: (_) {
                                print('🖱️ Mouse exit с результата поиска: ${crypto.name}');
                                setState(() {});
                              },
                              child: GestureDetector(
                                onTap: () {
                                  print('🔍 КЛИК ПО РЕЗУЛЬТАТУ ПОИСКА: ${crypto.name} (${crypto.symbol})');
                                  print('🔍 Состояние перед навигацией: mounted=$mounted');

                                  // Убираем overlay
                                  _removeOverlay();
                                  _searchFocusNode.unfocus();

                                  // Очищаем состояние поиска
                                  setState(() {
                                    _searchController.clear();
                                    _isSearching = false;
                                    _searchResults.clear();
                                  });

                                  print('🚀 НАЧИНАЕМ НАВИГАЦИЮ К ${crypto.name}...');

                                  // Навигация к детальной странице (синхронно, как в остальных частях)
                                  try {
                                    print('🚀 Вызываем Navigator.pushNamed...');
                                    Navigator.of(context).pushNamed(
                                      '/coin_detail',
                                      arguments: crypto,
                                    );
                                    print('✅ НАВИГАЦИЯ К ГРАФИКУ УСПЕШНА ДЛЯ ${crypto.name}');
                                  } catch (error) {
                                    print('❌ ОШИБКА НАВИГАЦИИ: $error');
                                    print('❌ Stack trace: ${StackTrace.current}');
                                    // Показываем ошибку пользователю
                                    if (mounted) {
                                      ScaffoldMessenger.of(context).showSnackBar(
                                        SnackBar(
                                          content: Text('Ошибка перехода к графику: ${error.toString()}'),
                                          backgroundColor: Colors.red,
                                        ),
                                      );
                                    }
                                  }
                                },
                                child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 200),
                                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 10),
                                  margin: const EdgeInsets.only(bottom: 6),
                                  decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.12),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: changeColor.withOpacity(0.3),
                                      width: 1,
                                    ),
                                    boxShadow: [
                                      BoxShadow(
                                        color: changeColor.withOpacity(0.2),
                                        blurRadius: 8,
                                        offset: const Offset(0, 2),
                                      ),
                                    ],
                                  ),
                                  child: Row(
                                    children: [
                                      // Иконка
                                      Container(
                                        width: 32,
                                        height: 32,
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                          color: Colors.grey.shade800,
                                          boxShadow: [
                                            BoxShadow(
                                              color: changeColor.withOpacity(0.3),
                                              blurRadius: 4,
                                              offset: const Offset(0, 2),
                                            ),
                                          ],
                                        ),
                                        child: ClipRRect(
                                          borderRadius: BorderRadius.circular(16),
                                          child: CachedNetworkImage(
                                            imageUrl: crypto.imageUrl,
                                            fit: BoxFit.cover,
                                            placeholder: (context, url) => Container(
                                              color: Colors.grey.shade800,
                                              child: const Center(
                                                child: SizedBox(
                                                  width: 16,
                                                  height: 16,
                                                  child: CircularProgressIndicator(strokeWidth: 2),
                                                ),
                                              ),
                                            ),
                                            errorWidget: (context, url, error) {
                                              return Container(
                                                color: Colors.grey.shade800,
                                                child: Center(
                                                  child: Text(
                                                    crypto.symbol.substring(0, 1),
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontWeight: FontWeight.bold,
                                                      fontSize: 14,
                                                    ),
                                                  ),
                                                ),
                                              );
                                            },
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 12),

                                      // Название и символ
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              crypto.name,
                                              style: const TextStyle(
                                                color: Colors.white,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 14,
                                              ),
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            Text(
                                              crypto.symbol.toUpperCase(),
                                              style: TextStyle(
                                                color: Colors.white.withOpacity(0.7),
                                                fontSize: 12,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),

                                      // Цена и изменение
                                      Column(
                                        crossAxisAlignment: CrossAxisAlignment.end,
                                        children: [
                                          Text(
                                            '\$${_formatPrice(crypto.price)}',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontWeight: FontWeight.bold,
                                              fontSize: 13,
                                            ),
                                          ),
                                          Container(
                                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                            decoration: BoxDecoration(
                                              color: changeColor.withOpacity(0.2),
                                              borderRadius: BorderRadius.circular(8),
                                              border: Border.all(
                                                color: changeColor.withOpacity(0.4),
                                                width: 1,
                                              ),
                                            ),
                                            child: Text(
                                              '${isPositive ? '+' : ''}${crypto.priceChangePercentage24h.toStringAsFixed(1)}%',
                                              style: TextStyle(
                                                color: changeColor,
                                                fontWeight: FontWeight.bold,
                                                fontSize: 11,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );

    Overlay.of(context).insert(_overlayEntry!);
  }

  // Удаление overlay
  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
}
