import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../widgets/app_bottom_navigation.dart';
import '../providers/crypto_provider.dart';
import '../widgets/mini_chart.dart';
import '../models/crypto_currency.dart';
import '../services/local_crypto_icons_service.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class ChartsScreen extends StatefulWidget {
  const ChartsScreen({Key? key}) : super(key: key);

  @override
  State<ChartsScreen> createState() => _ChartsScreenState();
}

class _ChartsScreenState extends State<ChartsScreen> with WidgetsBindingObserver {
  final ScrollController _scrollController = ScrollController();

  // Время последнего обновления данных
  late DateTime _lastUpdateTime;

  // Таймер для автоматического обновления данных
  Timer? _refreshTimer;

  // Таймер для автоматической прокрутки бегущей строки
  Timer? _scrollTimer;

  // Флаг для отслеживания состояния приложения
  bool _isAppActive = true;

  // Текущий выбранный фильтр
  String _selectedFilter = 'All';

  // Список криптовалют для отображения
  List<CryptoCurrency> _displayedCryptos = [];

  @override
  void initState() {
    super.initState();
    _lastUpdateTime = DateTime.now();

    // Регистрируем наблюдатель за жизненным циклом приложения
    WidgetsBinding.instance.addObserver(this);

    // Загружаем данные о криптовалютах при инициализации
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);
      if (cryptoProvider.allCryptos.isEmpty && !cryptoProvider.isLoading) {
        cryptoProvider.loadAllCryptos();
      }

      // Запускаем автоматическое обновление данных
      _startPeriodicDataRefresh();

      // Запускаем автоматическую прокрутку бегущей строки
      _startAutoScroll();
    });
  }

  @override
  void dispose() {
    // Отменяем таймеры при уничтожении виджета
    _refreshTimer?.cancel();
    _scrollTimer?.cancel();

    // Отписываемся от наблюдения за жизненным циклом
    WidgetsBinding.instance.removeObserver(this);

    _scrollController.dispose();
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    // Отслеживаем состояние приложения для оптимизации обновлений
    if (state == AppLifecycleState.resumed) {
      // Приложение стало активным
      _isAppActive = true;

      // Немедленно обновляем данные
      _refreshData();

      // Перезапускаем таймеры
      _startPeriodicDataRefresh();
      _startAutoScroll();
    } else if (state == AppLifecycleState.paused ||
               state == AppLifecycleState.inactive ||
               state == AppLifecycleState.detached) {
      // Приложение стало неактивным
      _isAppActive = false;

      // Останавливаем таймеры для экономии ресурсов
      _refreshTimer?.cancel();
      _scrollTimer?.cancel();
    }
  }

  // Метод для запуска периодического обновления данных
  void _startPeriodicDataRefresh() {
    // Отменяем существующий таймер, если он есть
    _refreshTimer?.cancel();

    // Создаем новый таймер, который будет срабатывать каждую минуту
    _refreshTimer = Timer.periodic(const Duration(minutes: 1), (timer) {
      if (_isAppActive) {
        _refreshData();
      }
    });
  }

  // Метод для обновления данных
  void _refreshData() {
    if (!mounted) return;

    final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);
    cryptoProvider.loadAllCryptos();

    // Обновляем время последнего обновления
    setState(() {
      _lastUpdateTime = DateTime.now();
    });
  }

  // Метод для запуска автоматической прокрутки бегущей строки
  void _startAutoScroll() {
    // Отменяем существующий таймер, если он есть
    _scrollTimer?.cancel();

    // Задержка перед началом прокрутки для инициализации
    Future.delayed(const Duration(milliseconds: 500), () {
      if (!mounted) return;

      // Проверяем, что контроллер прокрутки имеет клиентов
      if (_scrollController.hasClients) {
        // Получаем максимальную позицию прокрутки
        final maxScrollExtent = _scrollController.position.maxScrollExtent;

        // Если нет возможности прокрутки, пробуем позже
        if (maxScrollExtent <= 0) {
          Future.delayed(const Duration(seconds: 1), () {
            if (mounted) _startAutoScroll();
          });
          return;
        }

        // Создаем таймер для периодической прокрутки
        _scrollTimer = Timer.periodic(const Duration(seconds: 15), (timer) {
          if (!mounted || !_isAppActive) {
            timer.cancel();
            return;
          }

          if (_scrollController.hasClients) {
            // Если мы в конце списка, прыгаем в начало
            if (_scrollController.offset >= maxScrollExtent * 0.9) {
              _scrollController.jumpTo(0);
            }

            // Вычисляем следующую позицию прокрутки
            final nextOffset = _scrollController.offset + 300; // Прокручиваем на 300 пикселей
            final targetOffset = nextOffset.clamp(0.0, maxScrollExtent);

            // Плавно прокручиваем к следующей позиции
            _scrollController.animateTo(
              targetOffset,
              duration: const Duration(seconds: 2),
              curve: Curves.easeInOut,
            );
          }
        });

        // Начинаем прокрутку сразу
        _scrollController.animateTo(
          300, // Начинаем с прокрутки на 300 пикселей
          duration: const Duration(seconds: 2),
          curve: Curves.easeInOut,
        );
      } else {
        // Если контроллер еще не имеет клиентов, пробуем позже
        Future.delayed(const Duration(seconds: 1), () {
          if (mounted) _startAutoScroll();
        });
      }
    });
  }

  // Метод для создания фильтра с современным дизайном
  Widget _buildFilterChip(String label, bool isSelected, VoidCallback onTap) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedFilter = label;
          _updateDisplayedCryptos();
        });
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        margin: const EdgeInsets.only(right: 8.0),
        padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 6.0),
        decoration: BoxDecoration(
          color: isSelected
              ? const Color(0xFF3D6AF6)
              : Colors.black.withOpacity(0.3),
          borderRadius: BorderRadius.circular(20.0),
          boxShadow: isSelected
              ? [BoxShadow(color: const Color(0xFF3D6AF6).withOpacity(0.5), blurRadius: 8, offset: const Offset(0, 2))]
              : [],
          gradient: isSelected
              ? const LinearGradient(
                  colors: [Color(0xFF3D6AF6), Color(0xFF5B86FF)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                )
              : null,
        ),
        child: Text(
          label,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.white.withOpacity(0.8),
            fontWeight: isSelected ? FontWeight.bold : FontWeight.w500,
            fontSize: 12.0,
          ),
        ),
      ),
    );
  }

  // Метод для обновления отображаемых криптовалют в зависимости от выбранного фильтра
  void _updateDisplayedCryptos() {
    final cryptoProvider = Provider.of<CryptoProvider>(context, listen: false);

    switch (_selectedFilter) {
      case 'Favorites':
        _displayedCryptos = cryptoProvider.favoritesCryptos;
        break;
      case 'Trending':
        // Логика для трендовых токенов: популярность, объем торгов, новые листинги
        // Сортируем по комбинации объема торгов и рыночной капитализации (показатель популярности)
        _displayedCryptos = List.from(cryptoProvider.allCryptos)
          ..sort((a, b) {
            // Вычисляем "трендовость" как комбинацию объема торгов и рыночной капитализации
            double trendScoreA = (a.volume24h / a.marketCap) * 1000; // Нормализуем
            double trendScoreB = (b.volume24h / b.marketCap) * 1000;

            // Добавляем бонус для AI и Meme токенов
            if (a.categories.contains('AI Agents') || a.categories.contains('Memes')) {
              trendScoreA *= 1.5;
            }
            if (b.categories.contains('AI Agents') || b.categories.contains('Memes')) {
              trendScoreB *= 1.5;
            }

            return trendScoreB.compareTo(trendScoreA);
          });

        // Берем топ-50 самых трендовых
        _displayedCryptos = _displayedCryptos.take(50).toList();
        break;

      case 'Gainers':
        // Логика для Gainers: только токены с положительным ростом, отсортированные по проценту роста
        _displayedCryptos = cryptoProvider.allCryptos
          .where((crypto) => (crypto.priceChangePercentage24h ?? 0) > 0) // Только растущие
          .toList()
          ..sort((a, b) => (b.priceChangePercentage24h ?? 0).compareTo(a.priceChangePercentage24h ?? 0)); // Сортируем по убыванию роста
        break;
      case 'Losers':
        _displayedCryptos = cryptoProvider.allCryptos
          .where((crypto) => (crypto.priceChangePercentage24h ?? 0) < 0) // Только падающие (с проверкой на null)
          .toList()
          ..sort((a, b) => (a.priceChangePercentage24h ?? 0).compareTo(b.priceChangePercentage24h ?? 0)); // Сортируем по возрастанию убытка
        break;
      case 'AI':
        _displayedCryptos = cryptoProvider.aiAgentsCryptos;
        break;
      case 'Memes':
        _displayedCryptos = cryptoProvider.memesCryptos;
        break;
      case 'All':
      default:
        _displayedCryptos = cryptoProvider.allCryptos;
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CryptoProvider>(
      builder: (context, cryptoProvider, _) {
        // Обновляем список отображаемых криптовалют
        if (_displayedCryptos.isEmpty) {
          _updateDisplayedCryptos();
        }

        final cryptos = cryptoProvider.allCryptos;

        // Подсчёт процента растущих и падающих
        int upCount = cryptos.where((c) => c.priceChangePercentage24h > 0).length;
        int downCount = cryptos.where((c) => c.priceChangePercentage24h < 0).length;
        int total = cryptos.length;
        double upPercent = total > 0 ? upCount / total : 0;
        double downPercent = total > 0 ? downCount / total : 0;

        // Выбор фона в зависимости от настроения рынка
        BoxDecoration background;

        if (upPercent > 0.6) {
          // Зеленый фон для бычьего рынка (более 60% токенов в плюсе)
          background = const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF0D1B1A), Color(0xFF1A3630)], // Darker green gradient
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          );
        } else if (downPercent > 0.4) {
          // Красный фон для медвежьего рынка (более 40% токенов в минусе) - понижен порог для отражения текущей ситуации
          background = const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF1B0D0D), Color(0xFF361A1A)], // Darker red gradient
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          );
        } else {
          // Серый фон для нейтрального рынка
          background = const BoxDecoration(
            gradient: LinearGradient(
              colors: [Color(0xFF1A1A1A), Color(0xFF2A2A2A)], // Darker gray gradient
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
            ),
          );
        }

        return Scaffold(
          appBar: AppBar(
            title: const Text('Crypto Market'),
            backgroundColor: Colors.transparent,
            elevation: 0,
            actions: [

              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () {
                  cryptoProvider.loadAllCryptos();
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Refreshing data...'),
                      duration: Duration(seconds: 1),
                    ),
                  );
                },
              ),
            ],
          ),
          body: Container(
            decoration: background,
            child: Column(
              children: [
                // Информация об обновлении
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade900,
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(color: Colors.grey.shade800, width: 0.5),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              color: Colors.grey.shade400,
                              size: 12,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Updated: ${_getFormattedDateTime()}',
                              style: TextStyle(
                                color: Colors.grey.shade400,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // Top coins carousel - улучшенная версия
                Container(
                  height: 100, // Увеличим высоту карусели
                  padding: const EdgeInsets.symmetric(vertical: 2.0),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: upPercent > 0.6
                        ? [Color(0xFF0D221D), Color(0xFF204C3A)] // Зеленый градиент
                        : downPercent > 0.4
                          ? [Color(0xFF220D0D), Color(0xFF4C2020)] // Красный градиент
                          : [Color(0xFF1D1D1D), Color(0xFF303030)], // Серый градиент
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                  ),
                  child: ListView.builder(
                    controller: _scrollController, // Используем контроллер для автоматической прокрутки
                    scrollDirection: Axis.horizontal,
                    itemCount: cryptos.length * 2, // Дублируем элементы для бесконечной прокрутки
                    itemBuilder: (context, index) {
                      final crypto = cryptos[index % cryptos.length]; // Используем остаток от деления для зацикливания
                      final isPositive = crypto.priceChangePercentage24h >= 0;
                      final changeSign = isPositive ? '+' : '';
                      final changeColor = isPositive ? Color(0xFF00FF00) : Color(0xFFFF0000);

                      return GestureDetector(
                        onTap: () {
                          // Переход на страницу детальной информации о криптовалюте
                          Navigator.pushNamed(
                            context,
                            '/coin_detail',
                            arguments: crypto,
                          );
                        },
                        child: Container(
                          width: 150, // Увеличиваем ширину для предотвращения переполнения
                          margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0),
                          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0), // Уменьшим вертикальные отступы внутри элемента
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: upPercent > 0.6
                                ? [Color(0xFF0F2A25), Color(0xFF2A5C4A)] // Зеленый градиент
                                : downPercent > 0.4
                                  ? [Color(0xFF2A1515), Color(0xFF5C2A2A)] // Красный градиент
                                  : [Color(0xFF1A1A1A), Color(0xFF2D2D2D)], // Серый градиент
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                            borderRadius: BorderRadius.circular(10.0),
                            border: Border.all(color: Colors.black, width: 1),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(51),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: SizedBox(
                            height: 150, // Устанавливаем явно заданную высоту
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                // Иконка, символ и название криптовалюты
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    // Иконка криптовалюты
                                    Container(
                                      width: 24,
                                      height: 24,
                                      margin: const EdgeInsets.only(right: 6),
                                      decoration: BoxDecoration(
                                        shape: BoxShape.circle,
                                        color: Colors.grey.shade800,
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withOpacity(0.2),
                                            blurRadius: 2,
                                            offset: const Offset(0, 1),
                                          ),
                                        ],
                                      ),
                                      child: ClipRRect(
                                        borderRadius: BorderRadius.circular(12),
                                        child: CachedNetworkImage(
                                          imageUrl: crypto.imageUrl,
                                          fit: BoxFit.cover,
                                          placeholder: (context, url) => Container(
                                            color: Colors.grey.shade800,
                                            child: const Center(
                                              child: SizedBox(
                                                width: 12,
                                                height: 12,
                                                child: CircularProgressIndicator(
                                                  strokeWidth: 2,
                                                ),
                                              ),
                                            ),
                                          ),
                                          errorWidget: (context, url, error) {
                                            // В случае ошибки загрузки изображения сначала пробуем использовать локальную иконку
                                            final upperSymbol = crypto.symbol.trim().toUpperCase();
                                            if (LocalCryptoIconsService.availableIcons.contains(upperSymbol)) {
                                              return LocalCryptoIconsService.getIcon(
                                                crypto.symbol,
                                                size: 24,
                                                borderRadius: BorderRadius.circular(12),
                                              );
                                            } else {
                                              return Container(
                                                color: Colors.grey.shade800,
                                                child: Center(
                                                  child: Text(
                                                    crypto.symbol.substring(0, crypto.symbol.length > 1 ? 1 : crypto.symbol.length),
                                                    style: const TextStyle(
                                                      color: Colors.white,
                                                      fontWeight: FontWeight.bold,
                                                      fontSize: 10,
                                                    ),
                                                  ),
                                                ),
                                              );
                                            }
                                          },
                                          // Включаем кэширование изображений
                                          cacheManager: DefaultCacheManager(),
                                          // Устанавливаем максимальный возраст кэша (7 дней)
                                          maxWidthDiskCache: 100,
                                          maxHeightDiskCache: 100,
                                          memCacheWidth: 50,
                                          memCacheHeight: 50,
                                        ),
                                      ),
                                    ),
                                    Text(
                                      crypto.symbol,
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                        shadows: [Shadow(color: Colors.black54, blurRadius: 2, offset: Offset(1,1))],
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 0),

                                // Цена и процент изменения
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      '\$${_formatPrice(crypto.price)}',
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                        fontSize: 14,
                                        shadows: [Shadow(color: Colors.black54, blurRadius: 2, offset: Offset(1,1))],
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    Container(
                                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                                      decoration: BoxDecoration(
                                        color: isPositive ? Colors.green.withOpacity(0.8) : Colors.red.withOpacity(0.8),
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                      child: Text(
                                        '$changeSign${crypto.priceChangePercentage24h.toStringAsFixed(1)}%',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),

                                // Мини-график (улучшенный)
                                Container(
                                  height: 25, // Уменьшаем высоту мини-графика
                                  width: 120, // Увеличиваем ширину для лучшей детализации
                                  margin: const EdgeInsets.only(top: 1),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(6),
                                    border: Border.all(color: Colors.grey.shade800, width: 0.5),
                                  ),
                                  clipBehavior: Clip.antiAlias,
                                  child: MiniChart(
                                    crypto: crypto,
                                    color: changeColor.withAlpha(204), // Немного приглушаем цвет (alpha 204 = 80% непрозрачности)
                                    strokeWidth: 2.5, // Увеличиваем толщину линии
                                    showArea: true,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ),

                // Tabs for different cryptocurrency lists
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 4.0), // Уменьшим вертикальный отступ
                  child: Column(
                    children: [
                      // Убрана надпись "All Cryptocurrencies"
                      const SizedBox(height: 4), // Уменьшим отступ сверху
                      // Tabs for different lists
                      Container(
                        height: 36, // Вернем высоту фильтров к предыдущей, если 40 было слишком много
                        margin: const EdgeInsets.symmetric(vertical: 4.0), // Уменьшим вертикальный отступ
                        child: SingleChildScrollView(
                          scrollDirection: Axis.horizontal,
                          child: Row(
                            children: [
                              _buildFilterChip('All', _selectedFilter == 'All', () {}),
                              _buildFilterChip('Favorites', _selectedFilter == 'Favorites', () {}),
                              _buildFilterChip('Trending', _selectedFilter == 'Trending', () {}),
                              _buildFilterChip('Gainers', _selectedFilter == 'Gainers', () {}),
                              _buildFilterChip('Losers', _selectedFilter == 'Losers', () {}),
                              _buildFilterChip('AI', _selectedFilter == 'AI', () {}),
                              _buildFilterChip('Memes', _selectedFilter == 'Memes', () {}),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // Основной контент
                Expanded(
                  child: Consumer<CryptoProvider>(
                    builder: (context, cryptoProvider, child) {
                      if (cryptoProvider.isLoading) {
                        return const Center(
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF3D6AF6)),
                          ),
                        );
                      }

                      if (_displayedCryptos.isEmpty) {
                        return const Center(
                          child: Text(
                            'No data available',
                            style: TextStyle(color: Colors.white),
                          ),
                        );
                      }

                      return ListView.builder(
                        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                        itemCount: (_displayedCryptos.length / 2).ceil(),
                        itemBuilder: (context, index) {
                          final leftIndex = index * 2;
                          final rightIndex = leftIndex + 1;

                          final leftCrypto = _displayedCryptos[leftIndex];
                          final rightCrypto = rightIndex < _displayedCryptos.length
                              ? _displayedCryptos[rightIndex]
                              : null;

                          return Padding(
                            padding: const EdgeInsets.only(bottom: 8.0),
                            child: Row(
                              children: [
                                // Левый столбец
                                Expanded(
                                  child: _buildCryptoListItem(leftCrypto, cryptoProvider),
                                ),
                                const SizedBox(width: 8.0),
                                // Правый столбец
                                Expanded(
                                  child: rightCrypto != null
                                      ? _buildCryptoListItem(rightCrypto, cryptoProvider)
                                      : const SizedBox.shrink(),
                                ),
                              ],
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
          bottomNavigationBar: AppBottomNavigation(
            currentIndex: 1,
            backgroundDecoration: BoxDecoration(
              gradient: LinearGradient(
                colors: upPercent > 0.6
                  ? [Color(0xFF1A3630), Color(0xFF3D6C57)] // Зеленый градиент для нижней панели
                  : downPercent > 0.4
                    ? [Color(0xFF361A1A), Color(0xFF6C3D3D)] // Красный градиент для нижней панели
                    : [Color(0xFF2A2A2A), Color(0xFF3D3D3D)], // Серый градиент для нижней панели
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
              ),
              border: Border(
                top: BorderSide(color: Colors.black, width: 1),
              ),
            ),
            onTap: (index) {
              if (index != 1) {
                switch (index) {
                  case 0:
                    Navigator.pushReplacementNamed(context, '/news');
                    break;
                  case 2:
                    Navigator.pushReplacementNamed(context, '/sinusoid');
                    break;
                  case 3:
                    Navigator.pushReplacementNamed(context, '/courses');
                    break;
                  case 4:
                    Navigator.pushReplacementNamed(context, '/profile');
                    break;
                }
              }
            },
          ),
        );
      },
    );
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  // Форматирование рыночной капитализации
  String _formatMarketCap(double marketCap) {
    if (marketCap >= 1000000000000) {
      return '\$${(marketCap / 1000000000000).toStringAsFixed(1)}T';
    } else if (marketCap >= 1000000000) {
      return '\$${(marketCap / 1000000000).toStringAsFixed(1)}B';
    } else if (marketCap >= 1000000) {
      return '\$${(marketCap / 1000000).toStringAsFixed(1)}M';
    } else {
      return '\$${marketCap.toStringAsFixed(0)}';
    }
  }

  // Получение форматированного времени последнего обновления (только часы и минуты)
  String _getFormattedDateTime() {
    return '${_lastUpdateTime.hour.toString().padLeft(2, '0')}:${_lastUpdateTime.minute.toString().padLeft(2, '0')}';
  }

  Widget _buildCryptoListItem(CryptoCurrency crypto, CryptoProvider cryptoProvider) {
    final isPositive = crypto.priceChangePercentage24h >= 0;
    final changeSign = isPositive ? '+' : '';
    final changeColor = isPositive ? Color(0xFF00FF00) : Color(0xFFFF0000);

    return InkWell(
      onTap: () {
        Navigator.pushNamed(
          context,
          '/coin_detail',
          arguments: crypto,
        );
      },
      child: Container(
        padding: const EdgeInsets.all(8.0),
        decoration: BoxDecoration(
          color: const Color(0xFF1A1D23).withOpacity(0.6),
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(color: Colors.black.withOpacity(0.3), width: 1),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Верхняя строка: иконка, название и избранное
            Row(
              children: [
                // Coin icon
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey.shade800,
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: CachedNetworkImage(
                      imageUrl: crypto.imageUrl,
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: Colors.grey.shade800,
                        child: const Center(
                          child: SizedBox(
                            width: 12,
                            height: 12,
                            child: CircularProgressIndicator(strokeWidth: 1),
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) {
                        final upperSymbol = crypto.symbol.trim().toUpperCase();
                        if (LocalCryptoIconsService.availableIcons.contains(upperSymbol)) {
                          return LocalCryptoIconsService.getIcon(
                            crypto.symbol,
                            size: 24,
                            borderRadius: BorderRadius.circular(12),
                          );
                        } else {
                          return Container(
                            color: Colors.grey.shade800,
                            child: Center(
                              child: Text(
                                crypto.symbol.substring(0, crypto.symbol.length > 2 ? 2 : crypto.symbol.length),
                                style: const TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 10,
                                ),
                              ),
                            ),
                          );
                        }
                      },
                      cacheManager: DefaultCacheManager(),
                      maxWidthDiskCache: 50,
                      maxHeightDiskCache: 50,
                      memCacheWidth: 25,
                      memCacheHeight: 25,
                    ),
                  ),
                ),
                const SizedBox(width: 8),

                // Name and symbol
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        crypto.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      Text(
                        crypto.symbol.toUpperCase(),
                        style: TextStyle(
                          color: Colors.grey.shade400,
                          fontSize: 10,
                        ),
                      ),
                    ],
                  ),
                ),

                // Favorite button
                GestureDetector(
                  onTap: () {
                    cryptoProvider.toggleFavorite(crypto.id);
                  },
                  child: Icon(
                    crypto.isFavorite ? Icons.star : Icons.star_border,
                    color: crypto.isFavorite ? Colors.amber : Colors.grey.shade600,
                    size: 16,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            // Мини-график
            Container(
              height: 30,
              child: MiniChart(
                crypto: crypto,
                color: changeColor.withAlpha(180),
                strokeWidth: 1.5,
                showArea: false,
              ),
            ),

            const SizedBox(height: 8),

            // Нижняя строка: цена и изменение
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Price
                Expanded(
                  child: Text(
                    '\$${_formatPrice(crypto.price)}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),

                // Change percentage
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 3),
                  decoration: BoxDecoration(
                    color: isPositive ? Colors.green.withOpacity(0.8) : Colors.red.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    '$changeSign${crypto.priceChangePercentage24h.toStringAsFixed(1)}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 11,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCryptoItem(CryptoCurrency crypto, CryptoProvider cryptoProvider) {
    final isPositive = crypto.priceChangePercentage24h >= 0;
    final changeSign = isPositive ? '+' : '';
    final changeColor = isPositive ? Color(0xFF00FF00) : Color(0xFFFF0000);

    return InkWell(
      onTap: () {
        Navigator.pushNamed(
          context,
          '/coin_detail',
          arguments: crypto,
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 3.0),
        decoration: BoxDecoration(
          color: const Color(0xFF1A1D23).withOpacity(0.5),
          borderRadius: BorderRadius.circular(8.0),
          border: Border.all(color: Colors.black.withOpacity(0.5), width: 1),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Верхняя часть с иконкой и названием
            Row(
              children: [
                // Coin icon
                Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Colors.grey.shade800,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.network(
                      crypto.imageUrl,
                      fit: BoxFit.cover,
                      errorBuilder: (context, error, stackTrace) {
                        return Container(
                          color: Colors.grey.shade800,
                          child: Center(
                            child: Text(
                              crypto.symbol.substring(0, crypto.symbol.length > 2 ? 2 : crypto.symbol.length),
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(width: 6),

                // Название и символ
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        crypto.name,
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                          shadows: [Shadow(color: Colors.black54, blurRadius: 2, offset: Offset(1,1))],
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                      Row(
                        children: [
                          Text(
                            crypto.symbol,
                            style: TextStyle(
                              color: Colors.grey.shade400,
                              fontSize: 10,
                            ),
                          ),
                          const SizedBox(width: 4),
                          // Индикатор рыночной капитализации
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 3, vertical: 1),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.5),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              '${_formatMarketCap(crypto.marketCap)}',
                              style: TextStyle(
                                color: Colors.grey.shade400,
                                fontSize: 9,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // Favorite button
                GestureDetector(
                  onTap: () {
                    cryptoProvider.toggleFavorite(crypto.id);
                  },
                  child: Icon(
                    crypto.isFavorite ? Icons.star : Icons.star_border,
                    color: crypto.isFavorite ? Colors.amber : Colors.grey.shade600,
                    size: 16,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 4),

            // Нижняя часть с ценой и изменением
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                // Цена
                Text(
                  '\$${_formatPrice(crypto.price)}',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                    shadows: [Shadow(color: Colors.black54, blurRadius: 2, offset: Offset(1,1))],
                  ),
                ),

                // Изменение цены
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: isPositive ? Colors.green.withOpacity(0.8) : Colors.red.withOpacity(0.8),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isPositive ? Icons.arrow_drop_up : Icons.arrow_drop_down,
                        color: Colors.white,
                        size: 14,
                      ),
                      Text(
                        '$changeSign${crypto.priceChangePercentage24h.toStringAsFixed(1)}%',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 11,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
