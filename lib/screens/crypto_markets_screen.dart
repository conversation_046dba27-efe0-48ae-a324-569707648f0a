import 'package:flutter/material.dart';
import '../widgets/app_bottom_navigation.dart';
import '../models/crypto_currency.dart';
import '../widgets/sparkline_chart.dart';
import '../services/binance_service.dart';

class CryptoMarketsScreen extends StatefulWidget {
  const CryptoMarketsScreen({super.key});

  @override
  State<CryptoMarketsScreen> createState() => _CryptoMarketsScreenState();
}

class _CryptoMarketsScreenState extends State<CryptoMarketsScreen> {
  final BinanceService _binanceService = BinanceService();
  late Future<List<CryptoCurrency>> _cryptosFuture;
  
  @override
  void initState() {
    super.initState();
    _cryptosFuture = _binanceService.getTopCryptos();
  }

  Widget _buildTopCryptoCard(CryptoCurrency crypto) {
    final isPositive = crypto.priceChangePercentage24h >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;
    final changeSign = isPositive ? '+' : '';

    return Container(
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(8),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showCoinDetails(crypto),
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Symbol and change
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      crypto.symbol,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      '$changeSign${crypto.priceChangePercentage24h.toStringAsFixed(2)}%',
                      style: TextStyle(
                        color: changeColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 4),
                
                // Name
                Text(
                  crypto.name,
                  style: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 8),
                
                // Price
                Text(
                  '\$${_formatPrice(crypto.price)}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                ),
                
                const SizedBox(height: 8),
                
                // Chart
                Expanded(
                  child: _buildSparklineChart(crypto, changeColor),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildCryptoListItem(CryptoCurrency crypto) {
    final isPositive = crypto.priceChangePercentage24h >= 0;
    final changeColor = isPositive ? Colors.green : Colors.red;
    final changeSign = isPositive ? '+' : '';
    final coinColor = _getCoinColor(crypto.symbol);

    return Container(
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        color: Colors.grey[850],
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showCoinDetails(crypto),
          borderRadius: BorderRadius.circular(8.0),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
            child: Row(
              children: [
                // Coin logo with colored background
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: Colors.grey[800],
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: Center(
                    child: Text(
                      crypto.symbol.substring(0, 1),
                      style: TextStyle(
                        color: coinColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),

                // Name and symbol
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        crypto.name,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      Text(
                        crypto.symbol,
                        style: TextStyle(
                          color: Colors.grey[400],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),

                // Mini chart
                SizedBox(
                  width: 80,
                  height: 30,
                  child: _buildSparklineChart(crypto, changeColor),
                ),
                const SizedBox(width: 16),

                // Price and change
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      '\$${_formatPrice(crypto.price)}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                    Text(
                      '$changeSign${crypto.priceChangePercentage24h.toStringAsFixed(2)}%',
                      style: TextStyle(
                        color: changeColor,
                        fontWeight: FontWeight.bold,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSparklineChart(CryptoCurrency crypto, Color color) {
    final List<double> data = _generateSparklineData(crypto);
    return SparklineChart(
      data: data,
      color: color,
      strokeWidth: 2.0,
    );
  }

  List<double> _generateSparklineData(CryptoCurrency crypto) {
    // Если у нас есть история цен, используем ее
    if (crypto.priceHistory.isNotEmpty) {
      return crypto.priceHistory.map((point) => point.price).toList();
    }
    
    // Иначе генерируем моковые данные
    final points = <double>[];
    final basePrice = crypto.price;
    final isPositive = crypto.priceChangePercentage24h >= 0;
    
    if (isPositive) {
      points.add(basePrice * 0.94);
      points.add(basePrice * 0.92);
      points.add(basePrice * 0.95);
      points.add(basePrice * 0.93);
      points.add(basePrice * 0.96);
      points.add(basePrice * 0.98);
      points.add(basePrice * 0.97);
      points.add(basePrice * 0.99);
      points.add(basePrice * 1.01);
      points.add(basePrice * 1.00);
    } else {
      points.add(basePrice * 1.06);
      points.add(basePrice * 1.04);
      points.add(basePrice * 1.05);
      points.add(basePrice * 1.03);
      points.add(basePrice * 1.02);
      points.add(basePrice * 1.01);
      points.add(basePrice * 1.02);
      points.add(basePrice * 0.99);
      points.add(basePrice * 0.98);
      points.add(basePrice * 1.00);
    }
    
    return points;
  }

  void _showCoinDetails(CryptoCurrency crypto) {
    // Navigate to coin details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Showing details for ${crypto.name}')),
    );
  }

  String _formatPrice(double price) {
    if (price >= 1000) {
      return price.toStringAsFixed(0);
    } else if (price >= 1) {
      return price.toStringAsFixed(2);
    } else {
      return price.toStringAsFixed(price < 0.001 ? 6 : 4);
    }
  }

  Color _getCoinColor(String symbol) {
    switch (symbol) {
      case 'BTC':
        return Colors.orange;
      case 'ETH':
        return Colors.blue;
      case 'SOL':
        return Colors.green;
      case 'XRP':
        return Colors.grey;
      case 'BNB':
        return Colors.amber;
      case 'ADA':
        return Colors.blue[300]!;
      default:
        return Colors.purple;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.black,
        title: const Text(
          'Crypto Market',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 22,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () {
              // Show search functionality
            },
          ),
        ],
      ),
      body: FutureBuilder<List<CryptoCurrency>>(
        future: _cryptosFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          } else if (snapshot.hasError) {
            return Center(child: Text('Error: ${snapshot.error}'));
          } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('No data available'));
          }
          
          final cryptos = snapshot.data!;
          
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Top row of crypto cards
              SizedBox(
                height: 140,
                child: ListView.builder(
                  padding: const EdgeInsets.all(8),
                  scrollDirection: Axis.horizontal,
                  itemCount: cryptos.length > 4 ? 4 : cryptos.length,
                  itemBuilder: (context, index) {
                    return Container(
                      width: 120,
                      margin: const EdgeInsets.only(right: 8),
                      child: _buildTopCryptoCard(cryptos[index]),
                    );
                  },
                ),
              ),

              // All Cryptocurrencies section
              Padding(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'All Cryptocurrencies',
                      style: TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                        fontSize: 18,
                      ),
                    ),
                    Text(
                      'Market Cap ▼',
                      style: TextStyle(
                        color: Colors.grey[400],
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),

              // List of cryptocurrencies
              Expanded(
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: cryptos.length,
                  itemBuilder: (context, index) {
                    return _buildCryptoListItem(cryptos[index]);
                  },
                ),
              ),
            ],
          );
        },
      ),
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 1,
        onTap: (index) {
          if (index != 1) {
            switch (index) {
              case 0:
                Navigator.pushReplacementNamed(context, '/news');
                break;
              case 2:
                Navigator.pushReplacementNamed(context, '/courses');
                break;
              case 3:
                Navigator.pushReplacementNamed(context, '/saved_analyses');
                break;
              case 4:
                Navigator.pushReplacementNamed(context, '/profile');
                break;
            }
          }
        },
      ),
    );
  }
}
