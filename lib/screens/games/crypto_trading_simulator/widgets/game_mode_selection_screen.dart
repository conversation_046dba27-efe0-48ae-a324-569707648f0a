import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import '../providers/trading_simulator_provider.dart';

class GameModeSelectionScreen extends StatelessWidget {
  const GameModeSelectionScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black,
            Colors.blueGrey.shade900,
          ],
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Select Game Mode',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ).animate().fadeIn(duration: 500.ms),

              const SizedBox(height: 8),

              const Text(
                'Choose how you want to practice trading',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 16,
                ),
              ).animate().fadeIn(duration: 500.ms, delay: 200.ms),

              const SizedBox(height: 32),

              Expanded(
                child: ListView(
                  children: [
                    _buildModeCard(
                      context,
                      title: 'Custom Mode',
                      description: 'Select specific cryptocurrencies, timeframes, and leverage to practice trading in a controlled environment.',
                      icon: Icons.tune,
                      color: Colors.blue,
                      onTap: () => _selectMode(context, GameMode.custom),
                      delay: 400,
                    ),

                    const SizedBox(height: 16),

                    _buildModeCard(
                      context,
                      title: 'Infinite Patterns',
                      description: 'Trade with random coins and timeframes. Test your skills across different market conditions and track your performance.',
                      icon: Icons.all_inclusive,
                      color: Colors.purple,
                      onTap: () => _selectMode(context, GameMode.infinitePatterns),
                      delay: 600,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModeCard(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required int delay,
  }) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      color: Colors.grey.shade900,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 28,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      title,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    color: Colors.white.withOpacity(0.5),
                    size: 16,
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Text(
                description,
                style: TextStyle(
                  color: Colors.white.withOpacity(0.7),
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    ).animate().fadeIn(duration: 500.ms, delay: delay.ms).slideX(begin: 0.2, end: 0);
  }

  void _selectMode(BuildContext context, GameMode mode) {
    Provider.of<TradingSimulatorProvider>(context, listen: false).selectGameMode(mode);
  }
}
