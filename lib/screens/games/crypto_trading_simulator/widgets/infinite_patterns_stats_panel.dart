import 'package:flutter/material.dart';

class InfinitePatternsStatsPanel extends StatelessWidget {
  final int roundsPlayed;
  final int winPercentage;
  final int deaths;
  final int currentWinStreak;
  final int bestWinStreak;

  const InfinitePatternsStatsPanel({
    Key? key,
    required this.roundsPlayed,
    required this.winPercentage,
    required this.deaths,
    required this.currentWinStreak,
    required this.bestWinStreak,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 8.0),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A2E),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Colors.grey.shade800,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildStatItem(
            label: 'Rounds',
            value: roundsPlayed.toString(),
          ),
          _buildStatItem(
            label: 'Win Rate',
            value: '$winPercentage%',
            color: _getWinRateColor(winPercentage),
          ),
          _buildStatItem(
            label: 'Streak',
            value: currentWinStreak.toString(),
            color: currentWinStreak > 2 ? Colors.orange : Colors.white,
          ),
          _buildStatItem(
            label: 'Deaths',
            value: deaths.toString(),
            color: deaths > 0 ? Colors.red : Colors.white,
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem({
    required String label,
    required String value,
    Color? color,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Text(
          value,
          style: TextStyle(
            color: color ?? Colors.white,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 6),
        Text(
          label,
          style: const TextStyle(
            color: Colors.grey,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Color _getWinRateColor(int percentage) {
    if (percentage >= 70) {
      return Colors.green;
    } else if (percentage >= 50) {
      return Colors.yellow;
    } else if (percentage > 0) {
      return Colors.orange;
    } else {
      return Colors.grey;
    }
  }
}
