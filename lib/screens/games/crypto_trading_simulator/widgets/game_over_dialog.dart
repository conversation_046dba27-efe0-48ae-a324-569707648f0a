import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

class GameOverDialog extends StatelessWidget {
  final VoidCallback onTopUp;
  final VoidCallback onRestart;
  final VoidCallback onBackToMenu;

  const GameOverDialog({
    Key? key,
    required this.onTopUp,
    required this.onRestart,
    required this.onBackToMenu,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.grey.shade900,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Colors.red.withOpacity(0.5),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.red.withOpacity(0.2),
              blurRadius: 20,
              spreadRadius: 5,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.warning_amber_rounded,
              color: Colors.red,
              size: 48,
            ).animate().scale(duration: 300.ms, curve: Curves.elasticOut),
            
            const SizedBox(height: 16),
            
            const Text(
              'GAME OVER',
              style: TextStyle(
                color: Colors.red,
                fontSize: 24,
                fontWeight: FontWeight.bold,
              ),
            ).animate().fadeIn(duration: 500.ms),
            
            const SizedBox(height: 8),
            
            Text(
              'You lost all your funds!',
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 16,
              ),
            ).animate().fadeIn(duration: 500.ms, delay: 200.ms),
            
            const SizedBox(height: 24),
            
            _buildActionButton(
              label: 'Top Up (\$1000)',
              icon: Icons.attach_money,
              color: Colors.green,
              onTap: onTopUp,
              delay: 400,
            ),
            
            const SizedBox(height: 12),
            
            _buildActionButton(
              label: 'Restart Game',
              icon: Icons.refresh,
              color: Colors.blue,
              onTap: onRestart,
              delay: 600,
            ),
            
            const SizedBox(height: 12),
            
            _buildActionButton(
              label: 'Back to Menu',
              icon: Icons.menu,
              color: Colors.purple,
              onTap: onBackToMenu,
              delay: 800,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
    required int delay,
  }) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: onTap,
        style: ElevatedButton.styleFrom(
          backgroundColor: color.withOpacity(0.2),
          foregroundColor: color,
          padding: const EdgeInsets.symmetric(vertical: 16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(
              color: color.withOpacity(0.5),
              width: 1,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon),
            const SizedBox(width: 8),
            Text(
              label,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 500.ms, delay: delay.ms).slideY(begin: 0.2, end: 0);
  }
}
