import 'package:flutter/material.dart';
import '../models/material_item.dart';
import '../widgets/material_card.dart';
import '../widgets/app_bottom_navigation.dart';

class MaterialsScreen extends StatefulWidget {
  const MaterialsScreen({super.key});

  @override
  State<MaterialsScreen> createState() => _MaterialsScreenState();
}

class _MaterialsScreenState extends State<MaterialsScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<MaterialItem> _materials = MaterialItem.getMockItems();
  MaterialLevel? _selectedLevel;
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.index = 1; // Start on Materials tab
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  List<MaterialItem> get _filteredMaterials {
    return _materials.where((material) {
      // Filter by search query
      if (_searchController.text.isNotEmpty) {
        final query = _searchController.text.toLowerCase();
        if (!material.title.toLowerCase().contains(query) &&
            !material.description.toLowerCase().contains(query) &&
            !material.tags.any((tag) => tag.toLowerCase().contains(query))) {
          return false;
        }
      }
      
      // Filter by level
      if (_selectedLevel != null && material.level != _selectedLevel) {
        return false;
      }
      
      return true;
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Learn'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.book),
              text: 'Courses',
            ),
            Tab(
              icon: Icon(Icons.article),
              text: 'Materials',
            ),
            Tab(
              icon: Icon(Icons.games),
              text: 'Games',
            ),
          ],
          onTap: (index) {
            if (index == 0) {
              // Navigate to Courses screen
              Navigator.pushReplacementNamed(context, '/courses');
            } else if (index == 2) {
              // Games screen временно недоступен
            }
          },
        ),
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              controller: _searchController,
              decoration: InputDecoration(
                hintText: 'Search materials...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(30.0),
                ),
                contentPadding: const EdgeInsets.symmetric(vertical: 0.0),
              ),
              onChanged: (_) {
                setState(() {});
              },
            ),
          ),
          
          // Level filter
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Row(
              children: [
                const Text(
                  'Filter by level:',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(width: 8.0),
                _buildLevelChip(null, 'All'),
                const SizedBox(width: 8.0),
                _buildLevelChip(MaterialLevel.beginner, 'Beginner'),
                const SizedBox(width: 8.0),
                _buildLevelChip(MaterialLevel.intermediate, 'Intermediate'),
                const SizedBox(width: 8.0),
                _buildLevelChip(MaterialLevel.advanced, 'Advanced'),
              ],
            ),
          ),
          
          // Materials list
          Expanded(
            child: _filteredMaterials.isEmpty
                ? const Center(
                    child: Text('No materials found matching your filters'),
                  )
                : ListView.builder(
                    itemCount: _filteredMaterials.length,
                    itemBuilder: (context, index) {
                      final material = _filteredMaterials[index];
                      return MaterialCard(
                        materialItem: material,
                        onTap: () {
                          // In a real app, this would navigate to a material detail screen
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(
                              content: Text('Opening ${material.title}'),
                              duration: const Duration(seconds: 1),
                            ),
                          );
                        },
                      );
                    },
                  ),
          ),
        ],
      ),
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 2,
        onTap: (index) {
          if (index != 2) {
            switch (index) {
              case 0:
                Navigator.pushReplacementNamed(context, '/news');
                break;
              case 1:
                Navigator.pushReplacementNamed(context, '/crypto_markets');
                break;
              case 3:
                Navigator.pushReplacementNamed(context, '/profile');
                break;
            }
          }
        },
      ),
    );
  }

  Widget _buildLevelChip(MaterialLevel? level, String label) {
    final isSelected = _selectedLevel == level;
    
    return FilterChip(
      label: Text(label),
      selected: isSelected,
      onSelected: (selected) {
        setState(() {
          _selectedLevel = selected ? level : null;
        });
      },
      backgroundColor: Colors.grey[200],
      selectedColor: _getLevelColor(level).withOpacity(0.2),
      checkmarkColor: _getLevelColor(level),
      labelStyle: TextStyle(
        color: isSelected ? _getLevelColor(level) : Colors.black,
        fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
      ),
    );
  }

  Color _getLevelColor(MaterialLevel? level) {
    switch (level) {
      case MaterialLevel.beginner:
        return Colors.green;
      case MaterialLevel.intermediate:
        return Colors.orange;
      case MaterialLevel.advanced:
        return Colors.red;
      default:
        return Theme.of(context).primaryColor;
    }
  }
}
