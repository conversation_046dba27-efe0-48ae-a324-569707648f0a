import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../models/trading_simulator_controller.dart';
import '../widgets/platform_improved_tradingview_chart.dart';

/// Новый экран симулятора торговли криптовалютой
class NewCryptoTradingSimulatorScreen extends StatefulWidget {
  const NewCryptoTradingSimulatorScreen({super.key});

  @override
  State<NewCryptoTradingSimulatorScreen> createState() => _NewCryptoTradingSimulatorScreenState();
}

class _NewCryptoTradingSimulatorScreenState extends State<NewCryptoTradingSimulatorScreen> {
  // Ключ для доступа к графику
  final GlobalKey _chartKey = GlobalKey();
  
  // Контроллер для управления симулятором
  late TradingSimulatorController _controller;
  
  @override
  void initState() {
    super.initState();
    // Создаем контроллер
    _controller = TradingSimulatorController();
  }
  
  @override
  void dispose() {
    // Освобождаем ресурсы
    _controller.dispose();
    super.dispose();
  }
  
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider.value(
      value: _controller,
      child: Scaffold(
        backgroundColor: const Color(0xFF131722),
        appBar: AppBar(
          backgroundColor: const Color(0xFF131722),
          title: const Text('Crypto Trading Simulator'),
          actions: [
            // Кнопка сброса игры
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: () {
                _controller.resetGame();
              },
            ),
            // Кнопка настроек
            IconButton(
              icon: const Icon(Icons.settings),
              onPressed: () {
                _showSettingsDialog(context);
              },
            ),
          ],
        ),
        body: Consumer<TradingSimulatorController>(
          builder: (context, controller, child) {
            // Если свечи еще не загружены, показываем индикатор загрузки
            if (controller.allCandles.isEmpty) {
              return const Center(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(),
                    SizedBox(height: 16),
                    Text(
                      'Loading candles...',
                      style: TextStyle(color: Colors.white70),
                    ),
                  ],
                ),
              );
            }
            
            return Column(
              children: [
                // Информация о текущей игре
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Информация о символе и таймфрейме
                      Text(
                        '${controller.currentSymbol} (${controller.timeframe})',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      // Информация о балансе
                      Text(
                        'Balance: \$${controller.balance.toStringAsFixed(2)}',
                        style: TextStyle(
                          color: controller.balance >= TradingSimulatorController.initialBalance
                              ? Colors.green
                              : Colors.red,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // График
                Expanded(
                  child: PlatformImprovedTradingViewChart(
                    key: _chartKey,
                    allCandles: controller.getAllVisibleCandles(),
                    onEntryPointSet: (price, time) {
                      // Обработка установки точки входа
                      debugPrint('Entry point set at price: $price, time: $time');
                    },
                    onTradeResult: (isUp, percentChange, finalPrice) {
                      // Обработка результата сделки
                      debugPrint('Trade result: isUp: $isUp, percentChange: $percentChange%, finalPrice: $finalPrice');
                    },
                  ),
                ),
                
                // Кнопки управления
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: _buildControlButtons(controller),
                ),
              ],
            );
          },
        ),
      ),
    );
  }
  
  // Построение кнопок управления
  Widget _buildControlButtons(TradingSimulatorController controller) {
    // Если показываются будущие свечи, показываем кнопку "Next Pattern"
    if (controller.showingFutureCandles) {
      return Center(
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          ),
          onPressed: () {
            // Очищаем элементы графика перед загрузкой следующего уровня
            PlatformImprovedTradingViewChart.clearChartElements(_chartKey);
            // Переходим к следующему уровню
            controller.goToNextLevel();
          },
          child: const Text('Next Pattern'),
        ),
      );
    }
    
    // Иначе показываем кнопки UP и DOWN
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        // Кнопка UP
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          ),
          onPressed: controller.gameStatus == 'playing'
              ? () {
                  // Сначала устанавливаем точку входа
                  PlatformImprovedTradingViewChart.setEntryPoint(_chartKey);
                  // Затем показываем все свечи (включая будущие 7 свечей)
                  PlatformImprovedTradingViewChart.showAllCandles(_chartKey);
                  // Делаем ставку на повышение
                  controller.makeTrade('buy');
                }
              : null,
          child: const Text('UP'),
        ),
        
        // Кнопка DOWN
        ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.red,
            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          ),
          onPressed: controller.gameStatus == 'playing'
              ? () {
                  // Сначала устанавливаем точку входа
                  PlatformImprovedTradingViewChart.setEntryPoint(_chartKey);
                  // Затем показываем все свечи (включая будущие 7 свечей)
                  PlatformImprovedTradingViewChart.showAllCandles(_chartKey);
                  // Делаем ставку на понижение
                  controller.makeTrade('sell');
                }
              : null,
          child: const Text('DOWN'),
        ),
      ],
    );
  }
  
  // Диалог настроек
  void _showSettingsDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: const Text('Settings'),
          content: Consumer<TradingSimulatorController>(
            builder: (context, controller, child) {
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Выбор режима игры
                  ListTile(
                    title: const Text('Game Mode'),
                    trailing: DropdownButton<String>(
                      value: controller.gameMode,
                      items: const [
                        DropdownMenuItem(
                          value: 'custom',
                          child: Text('Custom'),
                        ),
                        DropdownMenuItem(
                          value: 'infinite',
                          child: Text('Infinite Patterns'),
                        ),
                      ],
                      onChanged: (value) {
                        if (value != null) {
                          controller.setGameMode(value);
                          Navigator.pop(context);
                        }
                      },
                    ),
                  ),
                  
                  // Выбор символа (только для режима Custom)
                  if (controller.gameMode == 'custom')
                    ListTile(
                      title: const Text('Symbol'),
                      trailing: DropdownButton<String>(
                        value: controller.currentSymbol,
                        items: controller.popularSymbols
                            .map((symbol) => DropdownMenuItem(
                                  value: symbol,
                                  child: Text(symbol),
                                ))
                            .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            controller.setSymbol(value);
                            Navigator.pop(context);
                          }
                        },
                      ),
                    ),
                  
                  // Выбор таймфрейма (только для режима Custom)
                  if (controller.gameMode == 'custom')
                    ListTile(
                      title: const Text('Timeframe'),
                      trailing: DropdownButton<String>(
                        value: controller.timeframe,
                        items: controller.timeframes
                            .map((tf) => DropdownMenuItem(
                                  value: tf,
                                  child: Text(tf),
                                ))
                            .toList(),
                        onChanged: (value) {
                          if (value != null) {
                            controller.setTimeframe(value);
                            Navigator.pop(context);
                          }
                        },
                      ),
                    ),
                  
                  // Выбор кредитного плеча
                  ListTile(
                    title: const Text('Leverage'),
                    trailing: DropdownButton<int>(
                      value: controller.leverage,
                      items: [1, 2, 3, 5, 10, 20, 50, 100]
                          .map((lev) => DropdownMenuItem(
                                value: lev,
                                child: Text('${lev}x'),
                              ))
                          .toList(),
                      onChanged: (value) {
                        if (value != null) {
                          controller.setLeverage(value);
                        }
                      },
                    ),
                  ),
                ],
              );
            },
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.pop(context);
              },
              child: const Text('Close'),
            ),
          ],
        );
      },
    );
  }
}
