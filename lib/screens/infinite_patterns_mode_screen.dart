import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';

class InfinitePatternsModeScreen extends StatelessWidget {
  const InfinitePatternsModeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Infinite Patterns Mode'),
        backgroundColor: Colors.black,
      ),
      body: WebView(
        initialUrl: 'https://www.tradingview.com/chart/',
        javascriptMode: JavascriptMode.unrestricted,
      ),
    );
  }
}
