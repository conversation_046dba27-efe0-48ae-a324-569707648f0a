import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For SystemUiOverlayStyle and HapticFeedback
import 'package:shared_preferences/shared_preferences.dart';
import '../services/market_analytics.dart'; // New market analytics service
import '../services/prediction_engine.dart'; // New prediction engine
import '../services/stable_sentiment_service.dart'; // Stable sentiment service
import '../widgets/app_bottom_navigation.dart';
import '../config/design_system.dart';
import '../utils/test_data_generator.dart'; // For generating test data
import 'dart:math' as math;
import 'dart:async'; // For Timer
import 'dart:math' show min, max, sqrt, pow;
import 'dart:convert'; // For JSON parsing
import 'package:http/http.dart' as http; // For API calls

// Helper class to maintain compatibility with existing code
class SentimentHistoryEntry extends HistoricalEntry {
  SentimentHistoryEntry({
    required DateTime date,
    required double value,
    required Map<String, double> metrics,
  }) : super(date, value, metrics);
}

// Helper class to maintain compatibility with existing code
class SentimentHistory {
  final List<SentimentHistoryEntry> entries;

  SentimentHistory({required this.entries});

  SentimentHistoryEntry? getYesterdayEntry() {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return getEntryForDate(yesterday);
  }

  SentimentHistoryEntry? getLastWeekEntry() {
    final lastWeek = DateTime.now().subtract(const Duration(days: 7));
    return getEntryForDate(lastWeek);
  }

  SentimentHistoryEntry? getEntryForDate(DateTime date) {
    final targetDate = DateTime(date.year, date.month, date.day);
    return entries.firstWhere(
      (entry) => DateTime(entry.date.year, entry.date.month, entry.date.day)
          .isAtSameMomentAs(targetDate),
      orElse: () => SentimentHistoryEntry(
        date: targetDate,
        value: 50.0, // Default neutral value
        metrics: {
          'Fear & Greed Index': 50.0,
          'News Sentiment': 50.0,
          'Holders Score': 50.0,
          'Volume Score': 50.0,
          'Social Engagement': 50.0,
          'Price Volatility': 50.0,
          'Bitcoin Dominance': 50.0,
        },
      ),
    );
  }
}

/// Класс для продвинутых частиц анимации в стиле iOS
class Particle {
  Offset position;
  Offset velocity;
  double size;
  double opacity;
  Color color;
  double rotation; // Угол вращения частицы
  double rotationSpeed; // Скорость вращения
  double scale; // Масштаб частицы (может меняться со временем)
  double scaleSpeed; // Скорость изменения масштаба
  ParticleShape shape; // Форма частицы
  List<Color> gradientColors; // Цвета для градиента
  bool useGradient; // Использовать ли градиент
  bool useGlow; // Использовать ли свечение
  double glowIntensity; // Интенсивность свечения
  double lifespan; // Продолжительность жизни частицы
  double decay; // Скорость затухания

  Particle({
    required this.position,
    required this.velocity,
    required this.size,
    required this.opacity,
    required this.color,
    this.rotation = 0.0,
    this.rotationSpeed = 0.0,
    this.scale = 1.0,
    this.scaleSpeed = 0.0,
    this.shape = ParticleShape.circle,
    this.gradientColors = const [],
    this.useGradient = false,
    this.useGlow = false,
    this.glowIntensity = 0.5,
    this.lifespan = 100.0,
    this.decay = 0.01,
  });

  void update() {
    // Обновляем позицию
    position = position + velocity;

    // Обновляем вращение
    rotation += rotationSpeed;

    // Обновляем масштаб
    scale += scaleSpeed;
    if (scale < 0.1) scale = 0.1;

    // Обновляем прозрачность с учетом decay
    opacity = (opacity - decay).clamp(0.0, 1.0);

    // Уменьшаем оставшееся время жизни
    lifespan -= 1.0;
  }

  bool isDead() {
    return opacity <= 0 || lifespan <= 0;
  }
}

/// Перечисление возможных форм частиц
enum ParticleShape {
  circle,
  square,
  triangle,
  diamond,
  star,
  line,
  custom,
}

/// A redesigned Sinusoid screen with a reactor-style indicator
class ReactorSinusoidScreen extends StatefulWidget {
  const ReactorSinusoidScreen({Key? key}) : super(key: key);

  @override
  State<ReactorSinusoidScreen> createState() => _ReactorSinusoidScreenState();
}

class _ReactorSinusoidScreenState extends State<ReactorSinusoidScreen>
    with TickerProviderStateMixin {
  // Using the new stable services instead of the old ones
  final ScrollController _scrollController = ScrollController();
  final StableSentimentService _stableSentimentService = StableSentimentService();

  double _indicatorValue = 50.0;
  Map<String, double> _metricValues = {};
  bool _isLoading = true;
  bool _isLoadingHistory = true;
  bool _showDownArrow = false; // Disabled down arrow // Control visibility of down arrow
  bool _isEnhancedVersion = false; // Toggle for enhanced version

  // Animation controllers and values for page appearance
  double _pageOpacity = 1.0; // Start visible by default

  // Background animation controllers
  late AnimationController _backgroundAnimationController;
  late AnimationController _colorAnimationController;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _colorAnimation;

  // Enhanced version toggle state

  // Timer for periodic data refresh (every 3 minutes)
  Timer? _dataRefreshTimer;
  DateTime _lastRefreshTime = DateTime.now();

  // Переменные для анимации реактора
  Timer? _animationTimer;
  double _animationValue = 0.0;
  List<Particle> _particles = [];
  SentimentHistoryEntry? _yesterdayEntry;
  SentimentHistoryEntry? _lastWeekEntry;
  List<SentimentHistoryEntry> _predictions = [];

  // Cache for prediction metrics to avoid debug messages on every build
  double _cachedTrendStrength = 0;
  double _cachedVolatility = 0;
  double _cachedMomentum = 0;
  double _cachedMarketEfficiency = 0;
  double _cachedSupportLevel = 0;
  double _cachedResistanceLevel = 0;
  double _cachedRSI = 0;
  double _cachedMACD = 0;
  bool _metricsLogged = false;

  // Last update time for enhanced metrics to prevent frequent changes
  DateTime _lastMetricsUpdateTime = DateTime.now().subtract(const Duration(hours: 1));
  // Last time we logged a "skipping update" message to prevent log spam
  DateTime _lastSkipLogTime = DateTime.now().subtract(const Duration(minutes: 1));

  // Constants for enhanced metrics stability
  static const double _metricChangeThreshold = 0.5; // Only update if change is greater than this
  static const Duration _minUpdateInterval = Duration(minutes: 30); // Minimum time between updates
  static const Duration _minLogInterval = Duration(minutes: 1); // Minimum time between logging "skipping" messages

  // Crypto volume data
  double _btcVolume = 0.0;
  double _ethVolume = 0.0;
  bool _isLoadingVolumes = true;

  @override
  void initState() {
    super.initState();

    // Initialize background animation controllers - slower animations
    _backgroundAnimationController = AnimationController(
      duration: const Duration(seconds: 15),
      vsync: this,
    );
    
    _colorAnimationController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    );

    _backgroundAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _backgroundAnimationController,
      curve: Curves.easeInOut,
    ));

    _colorAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _colorAnimationController,
      curve: Curves.easeInOut,
    ));

    // Start animations
    _backgroundAnimationController.repeat(reverse: true);
    _colorAnimationController.repeat(reverse: true);

    // Load cached enhanced metrics from SharedPreferences
    _loadEnhancedMetricsFromPrefs();

    _loadAllData();
    
    // Load crypto volume data
    _fetchCryptoVolumes();

    // Add scroll listener to show arrow when user scrolls to top
    _scrollController.addListener(() {
      if (_scrollController.offset <= 50 && !_showDownArrow) {
        // Show arrow when user scrolls to top
        setState(() {
          _showDownArrow = true;
        });
      }
    });

    // Запускаем таймер для анимации
    _startAnimation();

    // Enable auto-refresh timer to update data periodically
    _startDataRefreshTimer();
  }

  /// Load enhanced metrics from SharedPreferences
  Future<void> _loadEnhancedMetricsFromPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Load basic metrics with default values if not found
      final trendStrength = prefs.getDouble('enhanced_trend_strength') ?? 2.5;
      final volatility = prefs.getDouble('enhanced_volatility') ?? 12.8;

      // Load advanced metrics with default values if not found
      final momentum = prefs.getDouble('enhanced_momentum') ?? 1.2;
      final marketEfficiency = prefs.getDouble('enhanced_market_efficiency') ?? 68.5;
      final supportLevel = prefs.getDouble('enhanced_support_level') ?? 42.0;
      final resistanceLevel = prefs.getDouble('enhanced_resistance_level') ?? 58.0;
      final rsi = prefs.getDouble('enhanced_rsi') ?? 54.0;
      final macd = prefs.getDouble('enhanced_macd') ?? 0.8;

      // Load last update time with default value of 1 hour ago if not found
      final lastUpdateTimeMillis = prefs.getInt('enhanced_metrics_last_update');
      final lastUpdateTime = lastUpdateTimeMillis != null
          ? DateTime.fromMillisecondsSinceEpoch(lastUpdateTimeMillis)
          : DateTime.now().subtract(const Duration(hours: 1));

      setState(() {
        _cachedTrendStrength = trendStrength;
        _cachedVolatility = volatility;
        _cachedMomentum = momentum;
        _cachedMarketEfficiency = marketEfficiency;
        _cachedSupportLevel = supportLevel;
        _cachedResistanceLevel = resistanceLevel;
        _cachedRSI = rsi;
        _cachedMACD = macd;
        _lastMetricsUpdateTime = lastUpdateTime;
      });

      debugPrint('Loaded enhanced metrics from prefs: trend=$trendStrength, volatility=$volatility, momentum=$momentum, efficiency=$marketEfficiency, support=$supportLevel, resistance=$resistanceLevel, RSI=$rsi, MACD=$macd, lastUpdate=$lastUpdateTime');
    } catch (e) {
      debugPrint('Error loading enhanced metrics from prefs: $e');
    }
  }

  /// Save enhanced metrics to SharedPreferences
  Future<void> _saveEnhancedMetricsToPrefs() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Save all metrics
      await prefs.setDouble('enhanced_trend_strength', _cachedTrendStrength);
      await prefs.setDouble('enhanced_volatility', _cachedVolatility);
      await prefs.setDouble('enhanced_momentum', _cachedMomentum);
      await prefs.setDouble('enhanced_market_efficiency', _cachedMarketEfficiency);
      await prefs.setDouble('enhanced_support_level', _cachedSupportLevel);
      await prefs.setDouble('enhanced_resistance_level', _cachedResistanceLevel);
      await prefs.setDouble('enhanced_rsi', _cachedRSI);
      await prefs.setDouble('enhanced_macd', _cachedMACD);
      await prefs.setInt('enhanced_metrics_last_update', _lastMetricsUpdateTime.millisecondsSinceEpoch);

      debugPrint('Saved enhanced metrics to prefs: trend=$_cachedTrendStrength, volatility=$_cachedVolatility, momentum=$_cachedMomentum, efficiency=$_cachedMarketEfficiency, support=$_cachedSupportLevel, resistance=$_cachedResistanceLevel, RSI=$_cachedRSI, MACD=$_cachedMACD, lastUpdate=$_lastMetricsUpdateTime');
    } catch (e) {
      debugPrint('Error saving enhanced metrics to prefs: $e');
    }
  }

  /// Update enhanced metrics from stable data for enhanced mode
  void _updateEnhancedMetricsFromStableData(double indicator, Map<String, double> rawMetrics, {bool forceUpdate = false}) {
    try {
      // Get enhanced technical indicators from stable service
      final enhancedMetrics = _stableSentimentService.getEnhancedMetrics(indicator, rawMetrics);
      
      // Check if we should update metrics based on time interval (skip if forced update)
      final now = DateTime.now();
      final timeSinceLastUpdate = now.difference(_lastMetricsUpdateTime);

      if (!forceUpdate && timeSinceLastUpdate < _minUpdateInterval) {
        // Check if enough time has passed since last log to prevent spam
        final timeSinceLastLog = now.difference(_lastSkipLogTime);
        if (timeSinceLastLog >= _minLogInterval) {
          debugPrint('Skipping enhanced metrics update: last update was ${timeSinceLastUpdate.inMinutes} minutes ago');
          _lastSkipLogTime = now;
        }
        return;
      }

      // Extract enhanced metrics
      final newTrendStrength = enhancedMetrics['trend_strength'] ?? _cachedTrendStrength;
      final newVolatility = enhancedMetrics['volatility'] ?? _cachedVolatility;
      final newMomentum = enhancedMetrics['momentum'] ?? _cachedMomentum;
      final newMarketEfficiency = enhancedMetrics['market_efficiency'] ?? _cachedMarketEfficiency;
      final newSupportLevel = enhancedMetrics['support_level'] ?? _cachedSupportLevel;
      final newResistanceLevel = enhancedMetrics['resistance_level'] ?? _cachedResistanceLevel;
      final newRSI = enhancedMetrics['rsi'] ?? _cachedRSI;
      final newMACD = enhancedMetrics['macd'] ?? _cachedMACD;

      // Check if metrics have changed significantly
      final trendStrengthChanged = (_cachedTrendStrength - newTrendStrength).abs() > _metricChangeThreshold;
      final volatilityChanged = (_cachedVolatility - newVolatility).abs() > _metricChangeThreshold;
      final momentumChanged = (_cachedMomentum - newMomentum).abs() > _metricChangeThreshold;
      final efficiencyChanged = (_cachedMarketEfficiency - newMarketEfficiency).abs() > _metricChangeThreshold * 2;
      final supportChanged = (_cachedSupportLevel - newSupportLevel).abs() > _metricChangeThreshold * 2;
      final resistanceChanged = (_cachedResistanceLevel - newResistanceLevel).abs() > _metricChangeThreshold * 2;
      final rsiChanged = (_cachedRSI - newRSI).abs() > _metricChangeThreshold * 2;
      final macdChanged = (_cachedMACD - newMACD).abs() > _metricChangeThreshold;

      final anySignificantChange = trendStrengthChanged || volatilityChanged || momentumChanged ||
                                  efficiencyChanged || supportChanged || resistanceChanged ||
                                  rsiChanged || macdChanged;

      if (forceUpdate || anySignificantChange || timeSinceLastUpdate > const Duration(hours: 2)) {
        debugPrint('Updating enhanced metrics from stable data due to ${forceUpdate ? 'forced update' : 'significant changes or time interval'}');
        debugPrint('Enhanced metrics before update: trend=$_cachedTrendStrength, volatility=$_cachedVolatility, momentum=$_cachedMomentum, efficiency=$_cachedMarketEfficiency, support=$_cachedSupportLevel, resistance=$_cachedResistanceLevel, RSI=$_cachedRSI, MACD=$_cachedMACD');

        setState(() {
          _cachedTrendStrength = newTrendStrength;
          _cachedVolatility = newVolatility;
          _cachedMomentum = newMomentum;
          _cachedMarketEfficiency = newMarketEfficiency;
          _cachedSupportLevel = newSupportLevel;
          _cachedResistanceLevel = newResistanceLevel;
          _cachedRSI = newRSI;
          _cachedMACD = newMACD;
          _lastMetricsUpdateTime = now;
        });

        // Save updated metrics to preferences
        _saveEnhancedMetricsToPrefs();

        debugPrint('Enhanced metrics updated from stable data: trend=$newTrendStrength, volatility=$newVolatility, momentum=$newMomentum, efficiency=$newMarketEfficiency, support=$newSupportLevel, resistance=$newResistanceLevel, RSI=$newRSI, MACD=$newMACD');
      } else {
        // Check if enough time has passed since last log to prevent spam
        final timeSinceLastLog = now.difference(_lastSkipLogTime);
        if (timeSinceLastLog >= _minLogInterval) {
          debugPrint('No significant changes in enhanced metrics from stable data, keeping current values');
          _lastSkipLogTime = now;
        }
      }
    } catch (e) {
      debugPrint('Error updating enhanced metrics from stable data: $e');
    }
  }

  /// Calculate advanced metrics based on current market data and predictions
  void _calculateAdvancedMetrics() {
    if (_metricValues.isEmpty || _predictions.isEmpty) {
      debugPrint('Cannot calculate advanced metrics: no data available');
      return;
    }

    // Check if we should update metrics based on time interval
    final now = DateTime.now();
    final timeSinceLastUpdate = now.difference(_lastMetricsUpdateTime);

    if (timeSinceLastUpdate < _minUpdateInterval) {
      // Проверяем, прошло ли достаточно времени с момента последнего лога
      final timeSinceLastLog = now.difference(_lastSkipLogTime);

      // Выводим сообщение только если прошло достаточно времени с момента последнего лога
      if (timeSinceLastLog >= _minLogInterval) {
        debugPrint('Skipping advanced metrics update: last update was ${timeSinceLastUpdate.inMinutes} minutes ago');
        _lastSkipLogTime = now; // Обновляем время последнего лога
      }
      return;
    }

    // Determine which calculation method to use based on mode
    if (_isEnhancedVersion) {
      debugPrint('Calculating enhanced risky metrics for Enhanced Mode...');
      _calculateRiskyAdvancedMetrics(); // Use more aggressive calculations for Enhanced Mode
    } else {
      debugPrint('Calculating standard metrics for Regular Mode...');
      _calculateStandardAdvancedMetrics(); // Use standard calculations for Regular Mode
    }
  }

  /// Calculate standard advanced metrics for regular mode
  void _calculateStandardAdvancedMetrics() {
    final now = DateTime.now();
    final timeSinceLastUpdate = now.difference(_lastMetricsUpdateTime);

    try {
      // Get current sentiment value
      final currentSentiment = _indicatorValue;

      // Get historical data points if available
      final yesterday = _yesterdayEntry?.value ?? currentSentiment;
      final lastWeek = _lastWeekEntry?.value ?? currentSentiment;

      // Get predictions
      final tomorrowPrediction = _predictions.isNotEmpty ? _predictions[0].value : currentSentiment;
      final nextWeekPrediction = _predictions.length > 6 ? _predictions[6].value : tomorrowPrediction;

      // Calculate trend strength (rate of change)
      final shortTermChange = tomorrowPrediction - currentSentiment;
      final longTermChange = nextWeekPrediction - currentSentiment;
      final newTrendStrength = (shortTermChange * 0.7) + (longTermChange * 0.3);

      // Calculate volatility (standard deviation of recent values)
      final recentValues = [lastWeek, yesterday, currentSentiment, tomorrowPrediction];
      double sum = 0;
      for (final value in recentValues) {
        sum += value;
      }
      final mean = sum / recentValues.length;

      double sumSquaredDiff = 0;
      for (final value in recentValues) {
        sumSquaredDiff += (value - mean) * (value - mean);
      }
      final newVolatility = sqrt(sumSquaredDiff / recentValues.length);

      // Calculate momentum (acceleration of trend)
      final yesterdayChange = currentSentiment - yesterday;
      final weekChange = currentSentiment - lastWeek;
      final dailyRate = yesterdayChange;
      final weeklyRate = weekChange / 7; // Average daily change over a week
      final newMomentum = dailyRate - weeklyRate; // Acceleration

      // Calculate market efficiency (how directional the market is)
      final absWeekChange = weekChange.abs();
      final sumDailyChanges = yesterdayChange.abs() + shortTermChange.abs();
      final newMarketEfficiency = sumDailyChanges > 0
          ? (absWeekChange / sumDailyChanges * 100).clamp(0.0, 100.0)
          : 50.0;

      // Calculate support and resistance levels
      final range = 10.0; // Range to consider around current value
      final newSupportLevel = max(0, currentSentiment - range * (1 + newVolatility / 20)).toDouble();
      final newResistanceLevel = min(100, currentSentiment + range * (1 + newVolatility / 20)).toDouble();

      // Calculate RSI (Relative Strength Index)
      double gains = 0;
      double losses = 0;
      for (int i = 1; i < _predictions.length; i++) {
        final change = _predictions[i].value - _predictions[i-1].value;
        if (change > 0) {
          gains += change;
        } else {
          losses -= change; // Make losses positive
        }
      }

      double newRSI = 50.0; // Default neutral value
      if (gains + losses > 0) {
        final rs = gains > 0 && losses > 0 ? gains / losses : (gains > 0 ? 2.0 : 0.5);
        newRSI = (100 - (100 / (1 + rs))).clamp(0.0, 100.0);
      }

      // Calculate MACD (Moving Average Convergence Divergence)
      // Simplified version using predictions as "moving averages"
      double shortTermAvg = 0;
      double longTermAvg = 0;

      // Short term (first 3 predictions)
      for (int i = 0; i < min(3, _predictions.length); i++) {
        shortTermAvg += _predictions[i].value;
      }
      shortTermAvg /= min(3, _predictions.length);

      // Long term (all predictions)
      for (final prediction in _predictions) {
        longTermAvg += prediction.value;
      }
      longTermAvg /= _predictions.length;

      final newMACD = shortTermAvg - longTermAvg;

      // Check if metrics have changed significantly
      final trendStrengthChanged = (_cachedTrendStrength - newTrendStrength).abs() > _metricChangeThreshold;
      final volatilityChanged = (_cachedVolatility - newVolatility).abs() > _metricChangeThreshold;
      final momentumChanged = (_cachedMomentum - newMomentum).abs() > _metricChangeThreshold;
      final efficiencyChanged = (_cachedMarketEfficiency - newMarketEfficiency).abs() > _metricChangeThreshold * 2;
      final supportChanged = (_cachedSupportLevel - newSupportLevel).abs() > _metricChangeThreshold * 2;
      final resistanceChanged = (_cachedResistanceLevel - newResistanceLevel).abs() > _metricChangeThreshold * 2;
      final rsiChanged = (_cachedRSI - newRSI).abs() > _metricChangeThreshold * 2;
      final macdChanged = (_cachedMACD - newMACD).abs() > _metricChangeThreshold;

      final anySignificantChange = trendStrengthChanged || volatilityChanged || momentumChanged ||
                                  efficiencyChanged || supportChanged || resistanceChanged ||
                                  rsiChanged || macdChanged;

      if (anySignificantChange || timeSinceLastUpdate > const Duration(hours: 2)) {
        debugPrint('Updating advanced metrics due to significant changes or time interval');

        setState(() {
          _cachedTrendStrength = newTrendStrength;
          _cachedVolatility = newVolatility;
          _cachedMomentum = newMomentum;
          _cachedMarketEfficiency = newMarketEfficiency;
          _cachedSupportLevel = newSupportLevel;
          _cachedResistanceLevel = newResistanceLevel;
          _cachedRSI = newRSI;
          _cachedMACD = newMACD;
          _lastMetricsUpdateTime = now;
        });

        // Save updated metrics to preferences
        _saveEnhancedMetricsToPrefs();

        debugPrint('Advanced metrics updated: trend=$newTrendStrength, volatility=$newVolatility, momentum=$newMomentum, efficiency=$newMarketEfficiency, support=$newSupportLevel, resistance=$newResistanceLevel, RSI=$newRSI, MACD=$newMACD');
      } else {
        // Проверяем, прошло ли достаточно времени с момента последнего лога
        final timeSinceLastLog = now.difference(_lastSkipLogTime);

        // Выводим сообщение только если прошло достаточно времени с момента последнего лога
        if (timeSinceLastLog >= _minLogInterval) {
          debugPrint('No significant changes in advanced metrics, keeping current values');
          _lastSkipLogTime = now; // Обновляем время последнего лога
        }
      }
    } catch (e) {
      debugPrint('Error calculating advanced metrics: $e');
    }
  }

  /// Calculate more aggressive and risky advanced metrics for enhanced mode
  void _calculateRiskyAdvancedMetrics() {
    final now = DateTime.now();
    final timeSinceLastUpdate = now.difference(_lastMetricsUpdateTime);

    try {
      // Get current sentiment value
      final currentSentiment = _indicatorValue;

      // Get historical data points if available
      final yesterday = _yesterdayEntry?.value ?? currentSentiment;
      final lastWeek = _lastWeekEntry?.value ?? currentSentiment;

      // Get predictions with more weight on future predictions
      final tomorrowPrediction = _predictions.isNotEmpty ? _predictions[0].value : currentSentiment;
      final nextWeekPrediction = _predictions.length > 6 ? _predictions[6].value : tomorrowPrediction;

      // Enhanced mode: Calculate trend strength with more weight on long-term changes
      // This makes the trend strength more pronounced and potentially riskier
      final shortTermChange = tomorrowPrediction - currentSentiment;
      final longTermChange = nextWeekPrediction - currentSentiment;

      // Amplify trend strength by 1.5x for enhanced mode
      final newTrendStrength = ((shortTermChange * 0.3) + (longTermChange * 0.7)) * 1.5;

      // Enhanced mode: Calculate volatility with more emphasis on extreme values
      // This makes volatility more sensitive to outliers
      final recentValues = [lastWeek, yesterday, currentSentiment, tomorrowPrediction];

      // Calculate mean
      double sum = 0;
      for (final value in recentValues) {
        sum += value;
      }
      final mean = sum / recentValues.length;

      // Calculate standard deviation with more weight on outliers
      double sumSquaredDiff = 0;
      for (final value in recentValues) {
        // Square the difference and apply a power function to amplify outliers
        final diff = (value - mean);
        sumSquaredDiff += diff * diff * (1 + (diff.abs() / 10)); // Amplify larger differences
      }

      // Amplify volatility by 1.3x for enhanced mode
      final newVolatility = sqrt(sumSquaredDiff / recentValues.length) * 1.3;

      // Enhanced mode: Calculate momentum using the new PredictionEngine.calculateMomentum function
      // This provides a more accurate momentum calculation based on linear regression
      List<HistoricalEntry> historyForMomentum = [];

      // Add historical entries if available
      if (_lastWeekEntry != null) {
        historyForMomentum.add(HistoricalEntry(
          _lastWeekEntry!.date,
          _lastWeekEntry!.value,
          _lastWeekEntry!.metrics,
        ));
      }

      if (_yesterdayEntry != null) {
        historyForMomentum.add(HistoricalEntry(
          _yesterdayEntry!.date,
          _yesterdayEntry!.value,
          _yesterdayEntry!.metrics,
        ));
      }

      // Add current sentiment
      historyForMomentum.add(HistoricalEntry(
        DateTime.now(),
        currentSentiment,
        _metricValues.map((key, value) => MapEntry(key, value)),
      ));

      // Add predictions for future momentum
      for (var prediction in _predictions.take(3)) {
        historyForMomentum.add(HistoricalEntry(
          prediction.date,
          prediction.value,
          prediction.metrics,
        ));
      }

      // Calculate momentum using the new function if we have enough data
      double newMomentum;
      if (historyForMomentum.length >= 5) {
        newMomentum = PredictionEngine.calculateMomentum(historyForMomentum) * 1.5; // Amplify for enhanced mode
      } else {
        // Fallback to old calculation if not enough data
        final yesterdayChange = currentSentiment - yesterday;
        final weekChange = currentSentiment - lastWeek;
        final dailyRate = yesterdayChange * 1.5; // Amplify daily rate
        final weeklyRate = weekChange / 7; // Average daily change over a week
        newMomentum = dailyRate - weeklyRate; // Acceleration with amplified daily rate
      }

      // Enhanced mode: Calculate market efficiency with more emphasis on directional movement
      // This makes efficiency more sensitive to trend changes
      final weekChange = currentSentiment - lastWeek;
      final yesterdayChange = currentSentiment - yesterday;
      // Используем уже определенную переменную shortTermChange
      final absWeekChange = weekChange.abs() * 1.2; // Amplify week change
      final sumDailyChanges = yesterdayChange.abs() + shortTermChange.abs();
      final newMarketEfficiency = sumDailyChanges > 0
          ? (absWeekChange / sumDailyChanges * 100).clamp(0.0, 100.0)
          : 50.0;

      // Enhanced mode: Calculate support and resistance levels with wider range
      // This creates more aggressive support/resistance levels
      final range = 15.0; // Increased range (was 10.0)
      final newSupportLevel = max(0, currentSentiment - range * (1 + newVolatility / 15)).toDouble();
      final newResistanceLevel = min(100, currentSentiment + range * (1 + newVolatility / 15)).toDouble();

      // Enhanced mode: Calculate RSI with more emphasis on recent price movements
      // This makes RSI more responsive to recent changes
      double gains = 0;
      double losses = 0;

      // Apply more weight to recent predictions
      final weights = <double>[];
      for (int i = 0; i < _predictions.length - 1; i++) {
        // Exponential decay weights (more recent = higher weight)
        weights.add(pow(0.85, i).toDouble());
      }

      // Normalize weights
      final weightSum = weights.fold(0.0, (sum, weight) => sum + weight);
      final normalizedWeights = weights.map((w) => w / weightSum).toList();

      // Calculate weighted gains and losses
      for (int i = 1; i < _predictions.length; i++) {
        final change = _predictions[i].value - _predictions[i-1].value;
        final weight = normalizedWeights[i-1];

        if (change > 0) {
          gains += change * weight;
        } else {
          losses -= change * weight; // Make losses positive
        }
      }

      double newRSI = 50.0; // Default neutral value
      if (gains + losses > 0) {
        // More aggressive RS calculation
        final rs = gains > 0 && losses > 0 ? (gains / losses) * 1.2 : (gains > 0 ? 2.5 : 0.4);
        newRSI = (100 - (100 / (1 + rs))).clamp(0.0, 100.0);
      }

      // Enhanced mode: Calculate MACD with shorter periods
      // This makes MACD more responsive to recent price movements
      double shortTermAvg = 0;
      double longTermAvg = 0;

      // Shorter short term (first 2 predictions instead of 3)
      for (int i = 0; i < min(2, _predictions.length); i++) {
        shortTermAvg += _predictions[i].value;
      }
      shortTermAvg /= min(2, _predictions.length);

      // Shorter long term (first 5 predictions instead of all)
      for (int i = 0; i < min(5, _predictions.length); i++) {
        longTermAvg += _predictions[i].value;
      }
      longTermAvg /= min(5, _predictions.length);

      // Amplify MACD signal
      final newMACD = (shortTermAvg - longTermAvg) * 1.4;

      // Enhanced mode: Lower threshold for metric changes
      // This makes metrics update more frequently
      final lowerThreshold = _metricChangeThreshold * 0.7;

      // Check if metrics have changed significantly
      final trendStrengthChanged = (_cachedTrendStrength - newTrendStrength).abs() > lowerThreshold;
      final volatilityChanged = (_cachedVolatility - newVolatility).abs() > lowerThreshold;
      final momentumChanged = (_cachedMomentum - newMomentum).abs() > lowerThreshold;
      final efficiencyChanged = (_cachedMarketEfficiency - newMarketEfficiency).abs() > lowerThreshold * 2;
      final supportChanged = (_cachedSupportLevel - newSupportLevel).abs() > lowerThreshold * 2;
      final resistanceChanged = (_cachedResistanceLevel - newResistanceLevel).abs() > lowerThreshold * 2;
      final rsiChanged = (_cachedRSI - newRSI).abs() > lowerThreshold * 2;
      final macdChanged = (_cachedMACD - newMACD).abs() > lowerThreshold;

      final anySignificantChange = trendStrengthChanged || volatilityChanged || momentumChanged ||
                                  efficiencyChanged || supportChanged || resistanceChanged ||
                                  rsiChanged || macdChanged;

      // Enhanced mode: Update more frequently (1 hour instead of 2)
      if (anySignificantChange || timeSinceLastUpdate > const Duration(hours: 1)) {
        debugPrint('Updating enhanced risky metrics due to significant changes or time interval');

        setState(() {
          _cachedTrendStrength = newTrendStrength;
          _cachedVolatility = newVolatility;
          _cachedMomentum = newMomentum;
          _cachedMarketEfficiency = newMarketEfficiency;
          _cachedSupportLevel = newSupportLevel;
          _cachedResistanceLevel = newResistanceLevel;
          _cachedRSI = newRSI;
          _cachedMACD = newMACD;
          _lastMetricsUpdateTime = now;
        });

        // Save updated metrics to preferences
        _saveEnhancedMetricsToPrefs();

        debugPrint('Enhanced risky metrics updated: trend=$newTrendStrength, volatility=$newVolatility, momentum=$newMomentum, efficiency=$newMarketEfficiency, support=$newSupportLevel, resistance=$newResistanceLevel, RSI=$newRSI, MACD=$newMACD');
      } else {
        // Проверяем, прошло ли достаточно времени с момента последнего лога
        final timeSinceLastLog = now.difference(_lastSkipLogTime);

        // Выводим сообщение только если прошло достаточно времени с момента последнего лога
        if (timeSinceLastLog >= _minLogInterval) {
          debugPrint('No significant changes in enhanced risky metrics, keeping current values');
          _lastSkipLogTime = now; // Обновляем время последнего лога
        }
      }
    } catch (e) {
      debugPrint('Error calculating enhanced risky metrics: $e');
    }
  }

  /// Start timer for periodic data refresh (every 3 minutes)
  void _startDataRefreshTimer() {
    // Cancel existing timer if it exists
    _dataRefreshTimer?.cancel();

    // Create a new timer that refreshes data every 3 minutes
    _dataRefreshTimer = Timer.periodic(const Duration(minutes: 3), (timer) {
      debugPrint('Auto-refresh timer triggered, refreshing data...');

      // Check if enough time has passed since last manual refresh
      final now = DateTime.now();
      final timeSinceLastRefresh = now.difference(_lastRefreshTime);

      // Only refresh if at least 1 minute has passed since last refresh
      // This prevents multiple refreshes if user manually refreshed recently
      if (timeSinceLastRefresh.inMinutes >= 1) {
        debugPrint('Refreshing data (last refresh was ${timeSinceLastRefresh.inMinutes} minutes ago)');

        // Load fresh data
        _loadAllData();
      } else {
        debugPrint('Skipping auto-refresh as manual refresh was done ${timeSinceLastRefresh.inSeconds} seconds ago');
      }
    });

    debugPrint('Started data refresh timer (every 3 minutes)');
  }

  /// Запускает анимацию реактора с оптимизированной производительностью
  void _startAnimation() {
    // Очищаем предыдущий таймер, если он существует
    _animationTimer?.cancel();

    // Создаем новый таймер с более высокой частотой обновления (50 мс вместо 100 мс)
    // для более плавной и быстрой анимации
    _animationTimer = Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (mounted) {
        setState(() {
          // Обновляем значение анимации с увеличенной скоростью
          _animationValue += 0.1; // Увеличено с 0.05 до 0.1 для ускорения анимации
          if (_animationValue > 2 * math.pi) {
            _animationValue = 0;
          }

          // Обновляем существующие частицы с продвинутыми эффектами
          for (int i = _particles.length - 1; i >= 0; i--) {
            _particles[i].update();
            // Удаляем мертвые частицы (с нулевой прозрачностью или истекшим временем жизни)
            if (_particles[i].isDead()) {
              _particles.removeAt(i);
            }
          }

          // Ограничиваем количество частиц для повышения производительности
          // Увеличиваем лимит для более насыщенной анимации
          if (_particles.length > 200) {
            _particles.removeRange(0, _particles.length - 200);
          }

          // Добавляем новые частицы в зависимости от уровня индикатора
          _addParticlesBasedOnLevel();
        });
      }
    });
  }

  /// Добавляет продвинутые частицы в зависимости от уровня индикатора - детерминированная версия
  void _addParticlesBasedOnLevel() {
    // Центр реактора - точно в центре перекрестия, смещен на 5px вверх
    final center = Offset(247, 242); // Точный центр перекрестия на реакторе, смещен на 5px вверх (с 247 до 242)

    // Максимальный радиус для частиц, увеличен в 2 раза
    final maxRadius = 70.0; // Увеличен в 2 раза (с 35 до 70)

    // Используем значение анимации и значение индикатора для детерминированного поведения
    final animationFactor = math.sin(_animationValue) * 0.5 + 0.5; // от 0.0 до 1.0
    final timeFactor = DateTime.now().millisecondsSinceEpoch % 1000 / 1000; // от 0.0 до 1.0

    // Разные анимации для разных уровней
    if (_indicatorValue < 20) {
      // Crash (0-20): Красные вспышки с эффектом пульсации
      if (_particles.length < 50) { // Ограничиваем количество частиц для производительности
        // Создаем основные частицы
        final angle = _animationValue * 0.5;

        _particles.add(Particle(
          position: center,
          velocity: Offset(
            math.cos(angle) * 4.0,
            math.sin(angle) * 4.0,
          ),
          size: 7.0,
          opacity: 1.0,
          color: Color.lerp(Colors.red, Colors.white, 0.3)!,
          shape: timeFactor < 0.7 ? ParticleShape.circle : ParticleShape.line,
          useGlow: true,
          glowIntensity: 0.9,
          lifespan: 50.0,
          decay: 0.02,
          rotationSpeed: 0.2,
          useGradient: true,
          gradientColors: [
            Colors.red.withAlpha(204),
            Colors.orange.withAlpha(153),
            Colors.red.withAlpha(204),
          ],
        ));

        // Добавляем эффект пульсации (дополнительные частицы)
        if (timeFactor < 0.2) {
          for (int i = 0; i < 3; i++) {
            final pulseAngle = angle + (i * math.pi * 2 / 3);
            _particles.add(Particle(
              position: center,
              velocity: Offset(
                math.cos(pulseAngle) * 5.0,
                math.sin(pulseAngle) * 5.0,
              ),
              size: 3.0,
              opacity: 1.0,
              color: Colors.red.withAlpha(204),
              shape: ParticleShape.circle,
              useGlow: true,
              glowIntensity: 0.6,
              lifespan: 25.0,
              decay: 0.04,
            ));
          }
        }
      }
    } else if (_indicatorValue < 40) {
      // Anxiety (21-40): Оранжевые вихри и вращающиеся шестеренки
      if (_particles.length < 60) {
        final angle = _animationValue;
        final distance = maxRadius * 0.6 * animationFactor;

        // Основные частицы - шестеренки
        _particles.add(Particle(
          position: center + Offset(
            math.cos(angle) * distance,
            math.sin(angle) * distance,
          ),
          velocity: Offset(
            math.cos(angle + math.pi/2) * 0.8,
            math.sin(angle + math.pi/2) * 0.8,
          ),
          size: 8.0,
          opacity: 1.0,
          color: Color.lerp(Colors.orange, Colors.amber, animationFactor)!,
          shape: timeFactor < 0.6 ? ParticleShape.circle : ParticleShape.diamond,
          rotation: angle * 0.5,
          rotationSpeed: 0.05 * (timeFactor < 0.5 ? 1 : -1),
          useGlow: true,
          glowIntensity: 0.5,
          lifespan: 80.0,
          decay: 0.01,
          useGradient: true,
          gradientColors: [
            Colors.orange.withAlpha(178),
            Colors.amber.withAlpha(127),
          ],
        ));

        // Добавляем вихревые следы
        if (timeFactor < 0.15) {
          for (int i = 0; i < 2; i++) {
            final trailAngle = angle + (i * 0.2 - 0.1);
            final trailDistance = distance * 0.9;
            _particles.add(Particle(
              position: center + Offset(
                math.cos(trailAngle) * trailDistance,
                math.sin(trailAngle) * trailDistance,
              ),
              velocity: Offset(
                math.cos(trailAngle + math.pi/2) * 0.4,
                math.sin(trailAngle + math.pi/2) * 0.4,
              ),
              size: 2.0,
              opacity: 1.0,
              color: Colors.amber.withAlpha(178),
              shape: ParticleShape.circle,
              lifespan: 50.0,
              decay: 0.02,
            ));
          }
        }
      }
    } else if (_indicatorValue < 60) {
      // Stasis (41-60): Желтые горизонтальные линии с пульсацией
      if (_particles.length < 40) {
        // Основные горизонтальные линии
        final offsetX = math.cos(_animationValue) * maxRadius * 0.5;
        final offsetY = math.sin(_animationValue * 3) * 3.0;

        _particles.add(Particle(
          position: center + Offset(offsetX, offsetY),
          velocity: Offset(
            math.sin(_animationValue) * 1.5,
            math.cos(_animationValue * 2) * 0.1,
          ),
          size: 4.0 + animationFactor * 3.0,
          opacity: 1.0,
          color: Color.lerp(Colors.yellow, Colors.white, 0.3)!,
          shape: ParticleShape.line,
          rotation: 0,
          useGlow: true,
          glowIntensity: 0.4,
          lifespan: 80.0,
          decay: 0.015,
          scale: 1.3,
          scaleSpeed: 0.005 * (timeFactor < 0.5 ? 1 : -1),
        ));

        // Добавляем пульсирующие точки
        if (timeFactor < 0.1) {
          _particles.add(Particle(
            position: center + Offset(
              math.cos(_animationValue * 2) * maxRadius * 0.6,
              math.sin(_animationValue * 3) * 5.0,
            ),
            velocity: Offset(
              math.sin(_animationValue) * 0.3,
              math.cos(_animationValue) * 0.3,
            ),
            size: 3.0,
            opacity: 1.0,
            color: Colors.yellow.withAlpha(204),
            shape: ParticleShape.circle,
            useGlow: true,
            glowIntensity: 0.5,
            lifespan: 65.0,
            decay: 0.015,
            scale: 1.0,
            scaleSpeed: 0.01 * (timeFactor < 0.5 ? 1 : -1),
          ));
        }
      }
    } else if (_indicatorValue < 80) {
      // Lift (61-80): Зеленые вращающиеся шестеренки с эффектом ускорения
      if (_particles.length < 70) {
        final angle = _animationValue * 1.5;
        final distance = maxRadius * 0.7 * animationFactor;

        // Основные частицы - шестеренки
        _particles.add(Particle(
          position: center + Offset(
            math.cos(angle) * distance,
            math.sin(angle) * distance,
          ),
          velocity: Offset(
            math.cos(angle + math.pi/4) * 1.2,
            math.sin(angle + math.pi/4) * 1.2,
          ),
          size: 6.0,
          opacity: 1.0,
          color: Color.lerp(Colors.lightGreen, Colors.green, animationFactor)!,
          shape: timeFactor < 0.7 ? ParticleShape.circle :
                 timeFactor < 0.85 ? ParticleShape.diamond : ParticleShape.star,
          rotation: angle,
          rotationSpeed: 0.1,
          useGlow: true,
          glowIntensity: 0.7,
          lifespan: 100.0,
          decay: 0.01,
          useGradient: true,
          gradientColors: [
            Colors.lightGreen.withAlpha(204),
            Colors.green.withAlpha(153),
          ],
        ));

        // Добавляем следы ускорения
        if (timeFactor < 0.2) {
          for (int i = 0; i < 3; i++) {
            final trailAngle = angle - (0.2 - i * 0.05);
            final trailDistance = distance * 0.85;
            _particles.add(Particle(
              position: center + Offset(
                math.cos(trailAngle) * trailDistance,
                math.sin(trailAngle) * trailDistance,
              ),
              velocity: Offset(
                math.cos(trailAngle + math.pi/4) * 0.6,
                math.sin(trailAngle + math.pi/4) * 0.6,
              ),
              size: 2.0,
              opacity: 1.0,
              color: Colors.lightGreen.withAlpha(153),
              shape: ParticleShape.circle,
              lifespan: 40.0,
              decay: 0.03,
            ));
          }
        }
      }
    } else {
      // Surge (81-100): Яркие зеленые искры с эффектом взрыва
      if (_particles.length < 80) {
        final angle = _animationValue * 2;
        final distance = maxRadius * animationFactor;

        // Основные частицы - искры
        _particles.add(Particle(
          position: center + Offset(
            math.cos(angle) * distance,
            math.sin(angle) * distance,
          ),
          velocity: Offset(
            math.cos(angle) * 3.0,
            math.sin(angle) * 3.0,
          ),
          size: 5.0,
          opacity: 1.0,
          color: Color.lerp(Colors.green, Colors.white, 0.3)!,
          shape: timeFactor < 0.6 ? ParticleShape.star :
                 timeFactor < 0.8 ? ParticleShape.diamond : ParticleShape.circle,
          rotation: angle * 2,
          rotationSpeed: 0.15,
          useGlow: true,
          glowIntensity: 0.8,
          lifespan: 90.0,
          decay: 0.01,
          useGradient: true,
          gradientColors: [
            Colors.green.withAlpha(229),
            Colors.lightGreen.withAlpha(178),
            Colors.white.withAlpha(127),
          ],
        ));

        // Добавляем эффект взрыва (дополнительные частицы)
        if (timeFactor < 0.15) {
          final burstCenter = center + Offset(
            math.cos(angle) * distance * 0.7,
            math.sin(angle) * distance * 0.7,
          );

          for (int i = 0; i < 5; i++) {
            final burstAngle = angle + (i * math.pi * 2 / 5);
            final burstSpeed = 2.5;

            _particles.add(Particle(
              position: burstCenter,
              velocity: Offset(
                math.cos(burstAngle) * burstSpeed,
                math.sin(burstAngle) * burstSpeed,
              ),
              size: 2.0,
              opacity: 1.0,
              color: Color.lerp(Colors.green, Colors.white, 0.4)!,
              shape: ParticleShape.circle,
              useGlow: true,
              glowIntensity: 0.6,
              lifespan: 50.0,
              decay: 0.03,
            ));
          }
        }
      }
    }

    // Удаляем мертвые частицы
    _particles.removeWhere((particle) => particle.isDead());

    // Ограничиваем количество частиц для повышения производительности
    if (_particles.length > 150) { // Увеличиваем лимит для более насыщенной анимации
      _particles.removeRange(0, _particles.length - 150);
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _animationTimer?.cancel(); // Останавливаем таймер анимации при уничтожении виджета
    _dataRefreshTimer?.cancel(); // Останавливаем таймер обновления данных
    _backgroundAnimationController.dispose();
    _colorAnimationController.dispose();
    super.dispose();
  }

  // Static flag to prevent multiple simultaneous data loads
  static bool isDataLoading = false;

  /// Load all data for the screen using stable services
  Future<void> _loadAllData() async {
    try {
      // Check if data is already loading
      if (isDataLoading) {
        debugPrint('Data loading already in progress, skipping duplicate request');
        return;
      }

      isDataLoading = true;

      try {
        debugPrint('Loading all data using stable services...');

        setState(() {
          _isLoading = true;
          _isLoadingHistory = true;
        });

        // Get current sentiment data using stable service
        final sentimentData = await _stableSentimentService.getCurrentSentimentData();
        final indicator = sentimentData['indicator'] as double;
        final uiMetrics = sentimentData['metrics'] as Map<String, double>;
        final rawMetrics = sentimentData['raw_metrics'] as Map<String, double>;

        // Save to history
        await _stableSentimentService.saveToHistory(indicator, rawMetrics);

                 // Get historical entries for comparison
         final stableYesterdayEntry = await _stableSentimentService.getYesterdayEntry();
         final stableLastWeekEntry = await _stableSentimentService.getLastWeekEntry();

         // Convert to local SentimentHistoryEntry type
         final yesterdayEntry = stableYesterdayEntry != null 
           ? SentimentHistoryEntry(
               date: stableYesterdayEntry.date,
               value: stableYesterdayEntry.value,
               metrics: stableYesterdayEntry.metrics,
             )
           : null;

         final lastWeekEntry = stableLastWeekEntry != null 
           ? SentimentHistoryEntry(
               date: stableLastWeekEntry.date,
               value: stableLastWeekEntry.value,
               metrics: stableLastWeekEntry.metrics,
             )
           : null;

         // Get predictions based on current mode
         final stablePredictions = await _stableSentimentService.getPredictions(7, isEnhanced: _isEnhancedVersion);
         
         // Convert to local SentimentHistoryEntry type
         final predictions = stablePredictions.map((entry) => SentimentHistoryEntry(
           date: entry.date,
           value: entry.value,
           metrics: entry.metrics,
         )).toList();

        // Update state
        if (mounted) {
          setState(() {
            _indicatorValue = indicator;
            _metricValues = uiMetrics;
            _yesterdayEntry = yesterdayEntry;
            _lastWeekEntry = lastWeekEntry;
            _predictions = predictions;
            _isLoading = false;
            _isLoadingHistory = false;
          });
        }

                 // Calculate enhanced metrics if in enhanced mode
         if (_isEnhancedVersion) {
           _updateEnhancedMetricsFromStableData(indicator, rawMetrics, forceUpdate: true);
         }

        // Set last refresh time to prevent frequent refreshes
        _lastRefreshTime = DateTime.now();
        debugPrint('Stable data refresh completed at: $_lastRefreshTime');
        debugPrint('Indicator: $indicator, Predictions: ${predictions.length}');

      } finally {
        isDataLoading = false;
      }
    } catch (e) {
      debugPrint('Error loading stable data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isLoadingHistory = false;
        });
      }
    }
  }

  /// Fetch crypto volume data from Coinpaprika API
  Future<void> _fetchCryptoVolumes() async {
    if (!mounted) return;

    try {
      debugPrint('Fetching crypto volume data from Coinpaprika API...');
      
      // Fetch BTC volume
      final btcResponse = await http.get(
        Uri.parse('https://api.coinpaprika.com/v1/tickers/btc-bitcoin'),
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'FinanceAI/1.0',
        },
      ).timeout(const Duration(seconds: 10));
      
      // Fetch ETH volume
      final ethResponse = await http.get(
        Uri.parse('https://api.coinpaprika.com/v1/tickers/eth-ethereum'),
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'FinanceAI/1.0',
        },
      ).timeout(const Duration(seconds: 10));
      
      if (btcResponse.statusCode == 200 && ethResponse.statusCode == 200) {
        final btcData = json.decode(btcResponse.body);
        final ethData = json.decode(ethResponse.body);
        
        debugPrint('BTC API Response: $btcData');
        debugPrint('ETH API Response: $ethData');
        
        // Extract volume data from Coinpaprika API response
        final btcVol = (btcData['quotes']?['USD']?['volume_24h'] ?? 0).toDouble();
        final ethVol = (ethData['quotes']?['USD']?['volume_24h'] ?? 0).toDouble();
        
        setState(() {
          _btcVolume = btcVol;
          _ethVolume = ethVol;
          _isLoadingVolumes = false;
        });
        
        debugPrint('BTC Volume: \$${_btcVolume.toStringAsFixed(2)}');
        debugPrint('ETH Volume: \$${_ethVolume.toStringAsFixed(2)}');
      } else {
        debugPrint('Failed to fetch crypto volumes - BTC: ${btcResponse.statusCode}, ETH: ${ethResponse.statusCode}');
        _setFallbackVolumeData();
      }
    } catch (e) {
      debugPrint('Error fetching crypto volumes: $e');
      _setFallbackVolumeData();
    }
  }

  /// Set fallback volume data when API fails
  void _setFallbackVolumeData() {
    setState(() {
      // Используем реалистичные значения как fallback
      _btcVolume = 28500000000.0; // ~28.5B USD (типичный объем BTC)
      _ethVolume = 15200000000.0; // ~15.2B USD (типичный объем ETH)
      _isLoadingVolumes = false;
    });
    
    debugPrint('Using fallback volume data - BTC: \$${_btcVolume.toStringAsFixed(2)}, ETH: \$${_ethVolume.toStringAsFixed(2)}');
    
    // Попробуем снова через 30 секунд
    Future.delayed(const Duration(seconds: 30), () {
      if (mounted) {
        _fetchCryptoVolumes();
      }
    });
  }

  /// Fetch current market sentiment data using the new MarketAnalytics class
  Future<void> _fetchMarketSentiment() async {
    try {
      setState(() {
        _isLoading = true;
      });

      debugPrint('Fetching market sentiment data using MarketAnalytics...');

      // Fetch all metrics using the new MarketAnalytics class
      final metrics = await MarketAnalytics.fetchMetrics();

      // Calculate the indicator value using the new MarketAnalytics class
      final indicator = MarketAnalytics.calculateIndicator(metrics);

      // Convert metrics to the format expected by the UI
      final uiMetrics = {
        'Fear & Greed Index': metrics['fearGreedIndex'] ?? 50.0,
        'News Sentiment': metrics['newsSentiment'] ?? 50.0,
        'Holders Score': metrics['holdersScore'] ?? 50.0,
        'Volume Score': metrics['volumeScore'] ?? 50.0,
        'Social Engagement': metrics['socialEngagement'] ?? 50.0,
        'Price Volatility': metrics['priceVolatility'] ?? 50.0,
        'Bitcoin Dominance': metrics['bitcoinDominance'] ?? 50.0,
      };

      // Log metrics for debugging
      debugPrint('=== METRICS RECEIVED ===');
      uiMetrics.forEach((key, value) {
        debugPrint('$key: $value');
      });
      debugPrint('Indicator value: $indicator');
      debugPrint('========================');

      // Save the current data to history
      final historyEntry = HistoricalEntry(
        DateTime.now(),
        indicator,
        metrics,
      );

      // Save to history using the new MarketAnalytics class
      await MarketAnalytics.saveHistoricalData(historyEntry);

      // Reload historical data to ensure consistency
      final updatedHistoryEntries = await MarketAnalytics.loadHistoricalData();
      debugPrint('After saving, now have ${updatedHistoryEntries.length} historical entries');

      // Update state
      if (mounted) {
        setState(() {
          _metricValues = uiMetrics;
          _indicatorValue = indicator;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          // Use default values if everything fails
          _indicatorValue = 50.0;
          _metricValues = {
            'Fear & Greed Index': 50.0,
            'News Sentiment': 50.0,
            'Holders Score': 50.0,
            'Volume Score': 50.0,
            'Social Engagement': 50.0,
            'Price Volatility': 50.0,
            'Bitcoin Dominance': 50.0,
          };
          _isLoading = false;
        });
      }
      debugPrint('Error fetching market sentiment: $e');
    }
  }

  /// Fetch historical data for comparison using the new MarketAnalytics class
  Future<void> _fetchHistoricalData() async {
    try {
      // Load historical data using the new MarketAnalytics class
      final historyEntries = await MarketAnalytics.loadHistoricalData();

      // Convert HistoricalEntry to SentimentHistoryEntry
      final sentimentEntries = historyEntries.map((entry) =>
        SentimentHistoryEntry(
          date: entry.date,
          value: entry.value,
          metrics: entry.metrics,
        )
      ).toList();

      // Create a SentimentHistory object from the entries
      final history = SentimentHistory(entries: sentimentEntries);

      // Check if we have enough historical data
      if (history.entries.length < 7) {
        debugPrint('Not enough historical data (${history.entries.length} entries), generating test data');

        // Generate 14 days of test data with a slight upward trend
        await TestDataGenerator.generateTestData(
          daysToGenerate: 14,
          baseValue: 50.0,
          trend: 0.3,  // Slight upward trend
          volatility: 2.5,
        );

        debugPrint('Test data generation complete');

        // Reload historical data after generating test data
        final updatedHistoryEntries = await MarketAnalytics.loadHistoricalData();

        // Convert HistoricalEntry to SentimentHistoryEntry
        final updatedSentimentEntries = updatedHistoryEntries.map((entry) =>
          SentimentHistoryEntry(
            date: entry.date,
            value: entry.value,
            metrics: entry.metrics,
          )
        ).toList();

        final updatedHistory = SentimentHistory(entries: updatedSentimentEntries);
        debugPrint('Now have ${updatedHistory.entries.length} historical data points');

        // Get yesterday's entry from updated history
        final yesterdayEntry = updatedHistory.getYesterdayEntry();

        // Get last week's entry from updated history
        final lastWeekEntry = updatedHistory.getLastWeekEntry();

        setState(() {
          _yesterdayEntry = yesterdayEntry;
          _lastWeekEntry = lastWeekEntry;
        });
      } else {
        debugPrint('Sufficient historical data available (${history.entries.length} entries)');

        // Get yesterday's entry
        final yesterdayEntry = history.getYesterdayEntry();

        // Get last week's entry
        final lastWeekEntry = history.getLastWeekEntry();

        setState(() {
          _yesterdayEntry = yesterdayEntry;
          _lastWeekEntry = lastWeekEntry;
        });
      }
    } catch (e) {
      debugPrint('Error fetching historical data: $e');
    }
  }

  /// Fetch prediction data using the new PredictionEngine class
  Future<void> _fetchPredictions() async {
    try {
      debugPrint('Fetching predictions using PredictionEngine...');

      // Load historical data for predictions - reload to ensure we have the latest data
      final historyEntries = await MarketAnalytics.loadHistoricalData();

      debugPrint('PredictionEngine will use ${historyEntries.length} historical entries: ${historyEntries.map((e) => e.value).join(", ")}');

      if (historyEntries.isEmpty) {
        debugPrint('No historical data available yet, skipping prediction fetch');
        return;
      }

      // Use the PredictionEngine to predict future values
      final predictedValues = await PredictionEngine.predictFutureValues(historyEntries, 7);

      debugPrint('Received ${predictedValues.length} predictions: ${predictedValues.join(", ")}');

      // Create SentimentHistoryEntry objects from the predicted values
      final predictions = <SentimentHistoryEntry>[];
      final now = DateTime.now();

      for (int i = 0; i < predictedValues.length; i++) {
        final futureDate = now.add(Duration(days: i + 1));
        final predictedValue = predictedValues[i];

        // Create a metrics map with prediction metadata
        final metrics = <String, double>{
          'confidence': 70.0, // Default confidence
          'volatility': PredictionEngine.calculateVolatility(historyEntries),
          'predicted': 1.0,
        };

        predictions.add(SentimentHistoryEntry(
          date: futureDate,
          value: predictedValue,
          metrics: metrics,
        ));

        debugPrint('Prediction for day ${i+1} ($futureDate): $predictedValue');
      }

      if (mounted) {
        setState(() {
          _predictions = predictions;
          _metricsLogged = false; // Reset to ensure metrics are logged once after new predictions
        });
      }
    } catch (e) {
      debugPrint('Error fetching predictions: $e');
    }
  }

  /// Get the current sentiment level description with new phase names
  String _getCurrentLevel(double value) {
    if (value >= 80) return 'Surge';
    if (value >= 60) return 'Lift';
    if (value >= 40) return 'Stasis';
    if (value >= 20) return 'Anxiety';
    return 'Crash';
  }

  /// Get the detailed description for the current sentiment level
  String _getCurrentLevelDescription(double value) {
    if (value >= 80) return 'Euphoric momentum driving a strong rally.';
    if (value >= 60) return 'Optimism fueling steady upward movement.';
    if (value >= 40) return 'Balanced market with no clear direction.';
    if (value >= 20) return 'Caution and moderate downward trend.';
    return 'Extreme panic and sharp market sell-off.';
  }

  /// Show FAQ dialog with information about the Crypto Pulse page
  // ignore: unused_element
  void _showFAQDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: const Color(0xFF1A2639), // Solid dark background
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: DesignSystem.accentBlue.withAlpha(51), // 0.2 opacity
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.lightbulb_outline,
                  color: DesignSystem.accentBlue,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'About Crypto Pulse',
                style: DesignSystem.headingM.copyWith(
                  color: DesignSystem.textPrimary,
                  letterSpacing: DesignSystem.letterSpacingTight,
                ),
              ),
            ],
          ),
          content: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildFAQSection(
                  'What is Crypto Pulse?',
                  'Crypto Pulse is our proprietary market sentiment indicator that provides real-time insights into the cryptocurrency market\'s emotional state. It combines advanced analytics with visual representation to help you make more informed trading decisions.',
                ),
                const SizedBox(height: 16),
                _buildFAQSection(
                  'The Reactor',
                  'The pulsating reactor at the center of the screen is our signature visualization of market energy. Its color and animation patterns reflect the current market sentiment, from deep red (extreme fear) to vibrant green (extreme greed). The reactor\'s core is a sophisticated fusion of technical and fundamental market signals.',
                ),
                const SizedBox(height: 16),
                _buildFAQSection(
                  'The Speedometer',
                  'The sentiment speedometer provides a numerical reading (0-100) of current market sentiment. Lower values indicate fear and higher values indicate greed. The position of the needle and the color gradient offer an intuitive understanding of where the market stands emotionally.',
                ),
                const SizedBox(height: 16),
                _buildFAQSection(
                  'Trading Signals',
                  'Our AI-powered trading signal provides actionable recommendations based on current sentiment and predicted future trends. These signals are designed to complement your trading strategy, not replace it. Always conduct your own research before making investment decisions.',
                ),
                const SizedBox(height: 16),
                _buildFAQSection(
                  'Future Prognosis',
                  'Using advanced predictive modeling, we forecast how market sentiment may evolve in the coming days. These projections are based on pattern recognition, trend analysis, and our proprietary algorithms that have been refined through extensive backtesting.',
                ),
                const SizedBox(height: 16),
                _buildFAQSection(
                  'The Science Behind It',
                  'Crypto Pulse is built on a proprietary blend of technical, fundamental, and sentiment analysis. Our algorithms process vast amounts of market data in real-time to deliver insights that would take hours of manual analysis. While we don\'t disclose our exact formula, rest assured it\'s the result of years of research and continuous refinement by our team of financial analysts and data scientists.',
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: Text(
                'Close',
                style: DesignSystem.labelL.copyWith(
                  color: DesignSystem.accentBlue,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Helper method to build FAQ section
  Widget _buildFAQSection(String title, String content) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: DesignSystem.headingS.copyWith(
            color: DesignSystem.accentBlue,
            letterSpacing: DesignSystem.letterSpacingTight,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          content,
          style: DesignSystem.bodyM.copyWith(
            color: DesignSystem.textSecondary,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  /// Get the detailed explanation for a specific market level
  String _getLevelDetailedExplanation(String level) {
    switch (level) {
      case 'Surge':
        return 'Surge (81-100): A period of extreme bullish sentiment and rapid price appreciation. '
            'Market participants are euphoric, with FOMO (Fear Of Missing Out) driving new investors '
            'to enter the market. Trading volumes are typically very high, and media coverage is overwhelmingly '
            'positive. This phase often precedes a market correction, as prices may become detached from '
            'fundamental values. Historically, this phase represents a good time to consider taking profits.';
      case 'Lift':
        return 'Lift (61-80): A healthy uptrend with strong positive momentum. '
            'Market sentiment is optimistic but not yet euphoric. Institutional investors are actively '
            'participating, and technical indicators confirm the strength of the trend. Price pullbacks '
            'are typically shallow and short-lived. This phase often represents a good balance between '
            'risk and reward for both short and medium-term investors.';
      case 'Stasis':
        return 'Stasis (41-60): A period of market equilibrium with no clear directional bias. '
            'Buying and selling pressures are relatively balanced, resulting in sideways price action '
            'or modest, directionless volatility. Trading volumes may be lower than average, and '
            'market participants are often waiting for a catalyst to determine the next major move. '
            'This phase is typically characterized by range-bound trading opportunities.';
      case 'Anxiety':
        return 'Anxiety (21-40): A downtrend with increasing selling pressure. '
            'Market sentiment is cautious to fearful, with concerns about further price declines. '
            'Technical indicators show weakness, and negative news tends to have a stronger impact '
            'than positive developments. Some long-term investors may see this as an accumulation phase, '
            'while short-term traders often look for shorting opportunities or remain on the sidelines.';
      case 'Crash':
        return 'Crash (0-20): A severe market downturn with panic selling and capitulation. '
            'Sentiment is extremely negative, with fear dominating market psychology. Trading volumes '
            'are typically very high as investors rush to exit positions. Technical indicators show '
            'extreme oversold conditions, and media coverage is overwhelmingly negative. Historically, '
            'this phase often represents a good time for contrarian investors to begin accumulating positions, '
            'though timing the exact bottom is notoriously difficult.';
      default:
        return 'No detailed explanation available for this level.';
    }
  }



  /// Show a dialog with detailed explanation of the market level
  void _showLevelDescriptionDialog(BuildContext context, String level) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            level,
            style: TextStyle(
              color: _getLevelColor(level),
              fontWeight: FontWeight.bold,
            ),
          ),
          content: SingleChildScrollView(
            child: Text(
              _getLevelDetailedExplanation(level),
              style: const TextStyle(fontSize: 16),
            ),
          ),
          backgroundColor: const Color(0xFF1A2639),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text(
                'Close',
                style: TextStyle(
                  color: Colors.blue,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// Get color for a specific market level
  Color _getLevelColor(String level) {
    switch (level) {
      case 'Surge': return const Color(0xFF34C759); // Green
      case 'Lift': return const Color(0xFFAED581); // Lime
      case 'Stasis': return const Color(0xFFFFCC00); // Yellow
      case 'Anxiety': return const Color(0xFFFF9500); // Orange
      case 'Crash': return const Color(0xFFFF3B30); // Red
      default: return Colors.grey;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      // Full screen background container
      decoration: BoxDecoration(
        color: const Color(0xFF0A0B0D), // Base dark color
      ),
      child: Stack(
        children: [
          // Full screen background positioned absolutely
          Positioned.fill(
            child: _buildAnimatedMintlifyBackground(),
          ),
          // Main Scaffold
          Scaffold(
            // Premium gradient background
            backgroundColor: Colors.transparent,
            extendBodyBehindAppBar: true,
            extendBody: true, // Allow body to extend behind navigation bar
            appBar: AppBar(
              backgroundColor: Colors.transparent,
              elevation: 0,
              systemOverlayStyle: SystemUiOverlayStyle.light,
              automaticallyImplyLeading: false,
              title: Row(
                children: [
                  // FAQ button
                  IconButton(
                    icon: const Icon(
                      Icons.question_mark_rounded,
                      color: DesignSystem.accentBlue,
                      size: 24,
                    ),
                    onPressed: () {
                      debugPrint('FAQ button pressed');
                      _showFAQDialog(context);
                    },
                  ),
                  // Refresh button
                  IconButton(
                    icon: const Icon(
                      Icons.refresh,
                      color: DesignSystem.accentBlue,
                      size: 24,
                    ),
                    onPressed: () {
                      debugPrint('Refresh button pressed');

                      // Show loading indicator
                      setState(() {
                        _isLoading = true;
                      });

                      // Show a snackbar to inform the user
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Refreshing data...'),
                          duration: Duration(seconds: 2),
                          backgroundColor: DesignSystem.accentBlue,
                        ),
                      );

                      // Update last refresh time to prevent auto-refresh from triggering too soon
                      _lastRefreshTime = DateTime.now();

                      // Load all data from scratch
                      _loadAllData();
                      
                      // Refresh crypto volume data
                      _fetchCryptoVolumes();
                    },
                  ),
                  // Title with Enhanced Version toggle
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Title
                      Text(
                        'Crypto Pulse',
                        style: DesignSystem.headingS.copyWith(
                          letterSpacing: DesignSystem.letterSpacingTight,
                        ),
                      ),
                      const SizedBox(width: 16),
                      // Enhanced Version toggle with animation
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(76), // 0.3 opacity
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withAlpha(25), // 0.1 opacity
                            width: 1,
                          ),
                        ),
                        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            // Regular version
                            GestureDetector(
                              onTap: () {
                                debugPrint('Switching to Regular mode');
                                setState(() {
                                  _isEnhancedVersion = false;
                                  // Reset metrics update time to force refresh
                                  _lastMetricsUpdateTime = DateTime.now().subtract(const Duration(hours: 1));
                                });

                                // Add haptic feedback
                                HapticFeedback.lightImpact();
                                
                                // Reload data for Regular mode
                                _loadAllData();
                              },
                              child: AnimatedContainer(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: !_isEnhancedVersion
                                      ? DesignSystem.accentBlue
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: AnimatedDefaultTextStyle(
                                  duration: const Duration(milliseconds: 300),
                                  style: DesignSystem.labelS.copyWith(
                                    color: !_isEnhancedVersion
                                        ? Colors.white
                                        : DesignSystem.textSecondary,
                                  ),
                                  child: const Text('Regular'),
                                ),
                              ),
                            ),
                            // Enhanced version
                            GestureDetector(
                              onTap: () {
                                debugPrint('Switching to Enhanced mode');
                                setState(() {
                                  _isEnhancedVersion = true;
                                  // Reset metrics update time to force refresh
                                  _lastMetricsUpdateTime = DateTime.now().subtract(const Duration(hours: 1));
                                });

                                // Add haptic feedback
                                HapticFeedback.mediumImpact();
                                
                                // Reload data for Enhanced mode
                                _loadAllData();
                              },
                              child: AnimatedContainer(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                decoration: BoxDecoration(
                                  color: _isEnhancedVersion
                                      ? DesignSystem.accentBlue
                                      : Colors.transparent,
                                  borderRadius: BorderRadius.circular(12),
                                ),
                                child: AnimatedDefaultTextStyle(
                                  duration: const Duration(milliseconds: 300),
                                  style: DesignSystem.labelS.copyWith(
                                    color: _isEnhancedVersion
                                        ? Colors.white
                                        : DesignSystem.textSecondary,
                                  ),
                                  child: const Text('Enhanced'),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  // Empty space to balance the layout
                  Expanded(child: Container()),
                ],
              ),
              centerTitle: false,
            ),
            // Content with navigation at bottom
            body: Stack(
              children: [
                // Main content
                _buildContent(),
                // Navigation bar positioned at bottom
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: AppBottomNavigation(
                    currentIndex: 2,
                    onTap: (index) {
                      if (index != 2) {
                        switch (index) {
                          case 0:
                            Navigator.pushReplacementNamed(context, '/news');
                            break;
                          case 1:
                            Navigator.pushReplacementNamed(context, '/charts');
                            break;
                          case 3:
                            Navigator.pushReplacementNamed(context, '/courses');
                            break;
                          case 4:
                            Navigator.pushReplacementNamed(context, '/profile');
                            break;
                        }
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build animated Mintlify-style background with two orbs and subtle colors
  Widget _buildAnimatedMintlifyBackground() {
    return AnimatedBuilder(
      animation: Listenable.merge([_backgroundAnimation, _colorAnimation]),
      builder: (context, child) {
        final sentimentColor = DesignSystem.getSentimentColor(_indicatorValue);
        
        // First orb (main) - increased by 2x
        final centerX1 = -0.3 + (_backgroundAnimation.value * 0.3); // Move from -0.3 to 0.0
        final centerY1 = -0.4 + (_backgroundAnimation.value * 0.2); // Move from -0.4 to -0.2
        final radius1 = 1.4 + (_backgroundAnimation.value * 0.6); // From 1.4 to 2.0 (2x larger)
        
        // Second orb (smaller) - increased by 2x
        final centerX2 = 0.2 + (_colorAnimation.value * 0.2); // Move from 0.2 to 0.4
        final centerY2 = -0.6 + (_colorAnimation.value * 0.3); // Move from -0.6 to -0.3
        final radius2 = 0.8 + (_colorAnimation.value * 0.4); // From 0.8 to 1.2 (2x larger)
        
        // Third orb (below the right one)
        final centerX3 = 0.3 + (_backgroundAnimation.value * 0.15); // Move from 0.3 to 0.45
        final centerY3 = 0.1 + (_backgroundAnimation.value * 0.2); // Move from 0.1 to 0.3 (below the second orb)
        final radius3 = 0.6 + (_backgroundAnimation.value * 0.3); // From 0.6 to 0.9
        
        // Fourth orb (in the highlighted area - bottom left of screen)
        final centerX4 = -0.8 + (_colorAnimation.value * 0.15); // Move from -0.8 to -0.65 (far left)
        final centerY4 = 0.6 + (_colorAnimation.value * 0.2); // Move from 0.6 to 0.8 (bottom area)
        final radius4 = 0.5 + (_colorAnimation.value * 0.3); // From 0.5 to 0.8 (smaller size)
        
        // Increased minimum brightness to prevent complete fade out
        final colorIntensity = 0.15 + (_colorAnimation.value * 0.15); // From 0.15 to 0.30
        
        return Container(
          decoration: BoxDecoration(
            // Darker base with slight blue tint like Mintlify
            color: const Color(0xFF0A0B0D),
          ),
          child: Stack(
            children: [
              // First orb (main)
              Container(
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    center: Alignment(centerX1, centerY1),
                    radius: radius1,
                    colors: [
                      // Bright center glow with enhanced visibility
                      sentimentColor.withOpacity(colorIntensity),
                      sentimentColor.withOpacity(colorIntensity * 0.8),
                      sentimentColor.withOpacity(colorIntensity * 0.6),
                      sentimentColor.withOpacity(colorIntensity * 0.4),
                      sentimentColor.withOpacity(colorIntensity * 0.25),
                      sentimentColor.withOpacity(colorIntensity * 0.15),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.1, 0.2, 0.35, 0.5, 0.7, 1.0],
                  ),
                ),
              ),
              // Second orb (smaller)
              Container(
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    center: Alignment(centerX2, centerY2),
                    radius: radius2,
                    colors: [
                      // Bright secondary glow
                      sentimentColor.withOpacity(colorIntensity * 0.7),
                      sentimentColor.withOpacity(colorIntensity * 0.5),
                      sentimentColor.withOpacity(colorIntensity * 0.35),
                      sentimentColor.withOpacity(colorIntensity * 0.25),
                      sentimentColor.withOpacity(colorIntensity * 0.15),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.15, 0.3, 0.5, 0.7, 1.0],
                  ),
                ),
              ),
              // Third orb (below the right one)
              Container(
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    center: Alignment(centerX3, centerY3),
                    radius: radius3,
                    colors: [
                      // Tertiary glow
                      sentimentColor.withOpacity(colorIntensity * 0.6),
                      sentimentColor.withOpacity(colorIntensity * 0.4),
                      sentimentColor.withOpacity(colorIntensity * 0.3),
                      sentimentColor.withOpacity(colorIntensity * 0.2),
                      sentimentColor.withOpacity(colorIntensity * 0.1),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.2, 0.35, 0.55, 0.75, 1.0],
                  ),
                ),
              ),
              // Fourth orb (bottom left from the first orb)
              Container(
                decoration: BoxDecoration(
                  gradient: RadialGradient(
                    center: Alignment(centerX4, centerY4),
                    radius: radius4,
                    colors: [
                      // Fourth orb glow
                      sentimentColor.withOpacity(colorIntensity * 0.5),
                      sentimentColor.withOpacity(colorIntensity * 0.35),
                      sentimentColor.withOpacity(colorIntensity * 0.25),
                      sentimentColor.withOpacity(colorIntensity * 0.18),
                      sentimentColor.withOpacity(colorIntensity * 0.12),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.25, 0.4, 0.6, 0.8, 1.0],
                  ),
                ),
              ),
              // Subtle overlay for additional depth
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      sentimentColor.withOpacity(0.026 + (_colorAnimation.value * 0.039)),
                      Colors.transparent,
                      sentimentColor.withOpacity(0.0195 + (_backgroundAnimation.value * 0.026)),
                      Colors.transparent,
                    ],
                    stops: const [0.0, 0.3, 0.7, 1.0],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  /// Build transparent container decoration like regular page
  BoxDecoration _buildDarkContainerDecoration({Color? accentColor}) {
    final accent = accentColor ?? DesignSystem.getSentimentColor(_indicatorValue);
    
    return BoxDecoration(
      // Same transparency as regular page
      color: Colors.black.withAlpha(51), // Same as regular page
      borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
      border: Border.all(
        color: accent.withAlpha(51), // Same transparency as regular page
        width: 1.0,
      ),
      boxShadow: DesignSystem.subtleShadow, // Use design system shadow
    );
  }

  /// Get sentiment label based on value
  String _getSentimentLabel(double value) {
    if (value >= 80) return 'Surge';
    if (value >= 60) return 'Lift';
    if (value >= 40) return 'Stasis';
    if (value >= 20) return 'Anxiety';
    return 'Crash';
  }

  /// Build a custom loading state with animated elements
  Widget _buildLoadingState() {
    final sentimentColor = DesignSystem.getSentimentColor(50.0); // Default color

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Animated reactor icon
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.8, end: 1.0),
            duration: const Duration(milliseconds: 1500),
            curve: Curves.easeInOut,
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: sentimentColor.withAlpha(40),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Image.asset(
                    'logo/Sinusoid/Reactor.png',
                    fit: BoxFit.contain,
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: 24),

          // Animated loading text
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.0, end: 1.0),
            duration: const Duration(milliseconds: 800),
            builder: (context, value, child) {
              return Opacity(
                opacity: value,
                child: Text(
                  'Loading market data...',
                  style: DesignSystem.labelL.copyWith(
                    color: DesignSystem.textSecondary,
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: 16),

          // Animated progress indicator
          TweenAnimationBuilder<double>(
            tween: Tween<double>(begin: 0.0, end: 1.0),
            duration: const Duration(milliseconds: 1000),
            builder: (context, value, child) {
              return Opacity(
                opacity: value,
                child: SizedBox(
                  width: 200,
                  child: LinearProgressIndicator(
                    backgroundColor: Colors.grey.withAlpha(50),
                    valueColor: AlwaysStoppedAnimation<Color>(sentimentColor),
                    minHeight: 4,
                  ),
                ),
              );
            },
          ),
        ],
      ),
    );
  }



  Widget _buildContent() {
    // Use a simple layout without Scaffold to allow background to show
    return AnimatedOpacity(
        opacity: _pageOpacity,
        duration: const Duration(milliseconds: 800),
        curve: Curves.easeOutCubic,
        child: _isLoading
            ? _buildLoadingState()
            : _isEnhancedVersion
                ? Container(
                    height: double.infinity,
                    child: RefreshIndicator(
                      onRefresh: _fetchMarketSentiment,
                      color: DesignSystem.accentBlue,
                      backgroundColor: DesignSystem.cardBackground,
                      child: Padding(
                        padding: EdgeInsets.only(
                          left: DesignSystem.spacing16,
                          right: DesignSystem.spacing16,
                          top: DesignSystem.spacing16,
                          bottom: 120, // Add bottom padding to avoid navigation overlap
                        ),
                        child: _buildEnhancedContent(),
                      ),
                    ),
                  )
                : Container(
                    height: double.infinity,
                    child: RefreshIndicator(
                      onRefresh: _fetchMarketSentiment,
                      color: DesignSystem.accentBlue,
                      backgroundColor: Colors.transparent, // Make RefreshIndicator transparent
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        physics: const AlwaysScrollableScrollPhysics(),
                        child: ConstrainedBox(
                          constraints: BoxConstraints(
                            minHeight: MediaQuery.of(context).size.height - 
                                       MediaQuery.of(context).padding.top - 
                                       kToolbarHeight - 
                                       kBottomNavigationBarHeight,
                          ),
                          child: Padding(
                            padding: EdgeInsets.only(
                              left: DesignSystem.spacing16,
                              right: DesignSystem.spacing16,
                              top: DesignSystem.spacing16,
                              bottom: 120, // Add bottom padding to avoid navigation overlap
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                // Main content row без анимации
                                Row(
                                  key: const ValueKey('regular_mode'),
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Left side - Reactor image (only in regular mode)
                                    _buildReactorImage(),

                                    SizedBox(width: DesignSystem.spacing24),

                                    // Middle - Speedometer and value
                                    Expanded(
                                      flex: 2,
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          // Add space to move speedometer down
                                          SizedBox(height: DesignSystem.spacing64 + DesignSystem.spacing40 + DesignSystem.spacing16),

                                          // Speedometer indicator
                                          _buildSpeedometerIndicator(),
                                        ],
                                      ),
                                    ),

                                    SizedBox(width: DesignSystem.spacing24),

                                    // Right side - Predictions
                                    if (_predictions.isNotEmpty)
                                      Expanded(
                                        flex: 2,
                                        child: _buildPredictionsSection(),
                                      ),
                                  ],
                                ),

                                // Trading signal button (only in regular mode)
                                Transform.translate(
                                  offset: const Offset(8, 0), // No vertical offset, just 8px right
                                  child: _buildSignalButton(),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
    );
  }

  /// Build enhanced content for Enhanced Mode exactly like screenshot
  Widget _buildEnhancedContent() {
    return Padding(
      padding: const EdgeInsets.only(top: 50), // Move content down by 50px total
      child: Row(
        key: const ValueKey('enhanced_mode'),
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
        // Left column - Enhanced Analytics + Trend Summary (как на скриншоте)
        Expanded(
          flex: 1,
          child: Column(
            children: [
              // Enhanced Analytics (верх левой колонки)
              Expanded(
                flex: 3,
                child: _buildScreenshotEnhancedMetrics(),
              ),
              
              SizedBox(height: DesignSystem.spacing16),
              
              // Trend Summary (низ левой колонки)
              Expanded(
                flex: 1,
                child: _buildScreenshotTrendSummary(),
              ),
            ],
          ),
        ),
        
        SizedBox(width: DesignSystem.spacing16),
        
        // Center column - Numeric Scale + Extra Sphere + Crypto Volume (как на скриншоте)
        Expanded(
          flex: 1,
          child: Column(
            children: [
              // Market Sentiment (верх центральной колонки)
              Expanded(
                flex: 2,
                child: _buildScreenshotNumericScale(),
              ),
              
              SizedBox(height: DesignSystem.spacing16),
              
              // Crypto Volume (низ центральной колонки)
              Expanded(
                flex: 1,
                child: _buildCryptoVolumeContainer(),
              ),
            ],
          ),
        ),
        
        SizedBox(width: DesignSystem.spacing16),
        
        // Right column - Predictions + Market Summary (как на скриншоте)
        Expanded(
          flex: 1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Enhanced Prognosis (верх правой колонки)
              Expanded(
                flex: 3,
                child: _buildScreenshotPredictions(),
              ),
              
              SizedBox(height: DesignSystem.spacing16),
              
              // Market Summary (низ правой колонки)
              Expanded(
                flex: 2,
                child: _buildScreenshotMarketSummary(),
              ),
            ],
          ),
        ),
      ],
    ),
    );
  }

  /// Build enhanced metrics exactly like screenshot
  Widget _buildScreenshotEnhancedMetrics() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing16),
      decoration: _buildDarkContainerDecoration(accentColor: DesignSystem.accentBlue),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.analytics_outlined,
                color: DesignSystem.textSecondary,
                size: 16,
              ),
              SizedBox(width: DesignSystem.spacing4),
              Text(
                'Advanced Analytics',
                style: DesignSystem.labelS.copyWith(
                  color: DesignSystem.accentBlue.withOpacity(0.9),
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                ),
              ),
            ],
          ),

          SizedBox(height: DesignSystem.spacing8),

          // Metrics list exactly like screenshot
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildSimpleMetricRow('Trend', '2.5'),
                SizedBox(height: DesignSystem.spacing4),
                _buildSimpleMetricRow('Volatility', '12.8%'),
                SizedBox(height: DesignSystem.spacing4),
                _buildSimpleMetricRow('Momentum', '1.2'),
                SizedBox(height: DesignSystem.spacing4),
                _buildSimpleMetricRow('Efficiency', '69%'),
                SizedBox(height: DesignSystem.spacing4),
                _buildSimpleMetricRow('RSI', '54'),
                SizedBox(height: DesignSystem.spacing4),
                _buildSimpleMetricRow('MACD', '0.80'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build simple metric row like screenshot with color accents and icons
  Widget _buildSimpleMetricRow(String label, String value) {
    // Determine accent color and icon based on metric type
    Color accentColor = DesignSystem.textPrimary;
    IconData icon = Icons.analytics;
    
    if (label.contains('Trend') || label.contains('Direction')) {
      accentColor = DesignSystem.accentGreen;
      icon = Icons.trending_up;
    } else if (label.contains('Volatility')) {
      accentColor = DesignSystem.accentOrange;
      icon = Icons.show_chart;
    } else if (label.contains('Volume')) {
      accentColor = DesignSystem.accentOrange;
      icon = Icons.bar_chart;
    } else if (label.contains('Momentum')) {
      accentColor = DesignSystem.accentGreen;
      icon = Icons.speed;
    } else if (label.contains('Efficiency')) {
      accentColor = DesignSystem.accentBlue;
      icon = Icons.settings_outlined;
    } else if (label.contains('RSI')) {
      accentColor = DesignSystem.accentBlue;
      icon = Icons.timeline;
    } else if (label.contains('MACD')) {
      accentColor = DesignSystem.accentBlue;
      icon = Icons.multiline_chart;
    } else if (label.contains('Status') || label.contains('Confidence')) {
      accentColor = const Color(0xFF6366F1);
      icon = Icons.check_circle_outline;
    }
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: accentColor.withOpacity(0.8),
              size: 16,
            ),
            SizedBox(width: DesignSystem.spacing4),
            Text(
              label,
              style: DesignSystem.labelS.copyWith(
                color: DesignSystem.textPrimary.withOpacity(0.8),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        Text(
          value,
          style: DesignSystem.labelS.copyWith(
            color: accentColor.withOpacity(0.9),
            fontWeight: FontWeight.w600,
            fontSize: 15,
          ),
        ),
      ],
    );
  }

  /// Build crypto volume container with beautiful styling
  Widget _buildCryptoVolumeContainer() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing8),
      decoration: _buildDarkContainerDecoration(accentColor: DesignSystem.accentOrange),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.bar_chart,
                color: DesignSystem.textSecondary,
                size: 16,
              ),
              SizedBox(width: DesignSystem.spacing4),
              Text(
                'Trading Volume (24h)',
                style: DesignSystem.labelS.copyWith(
                  color: DesignSystem.accentOrange.withOpacity(0.9),
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                ),
              ),
            ],
          ),

          SizedBox(height: DesignSystem.spacing8),

          // Volume data
          if (_isLoadingVolumes)
            Expanded(
              child: Center(
                child: SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      DesignSystem.accentOrange.withOpacity(0.7),
                    ),
                  ),
                ),
              ),
            )
          else
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildVolumeRow('BTC', _btcVolume),
                  _buildVolumeRow('ETH', _ethVolume),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// Build volume row for crypto
  Widget _buildVolumeRow(String symbol, double volume) {
    String formattedVolume;
    
    if (volume == 0.0) {
      formattedVolume = 'Loading...';
    } else if (volume >= 1e9) {
      formattedVolume = '\$${(volume / 1e9).toStringAsFixed(1)}B';
    } else if (volume >= 1e6) {
      formattedVolume = '\$${(volume / 1e6).toStringAsFixed(1)}M';
    } else if (volume >= 1e3) {
      formattedVolume = '\$${(volume / 1e3).toStringAsFixed(1)}K';
    } else {
      formattedVolume = '\$${volume.toStringAsFixed(2)}';
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            Icon(
              symbol == 'BTC' ? Icons.currency_bitcoin : Icons.auto_awesome,
              color: DesignSystem.accentOrange.withOpacity(0.7),
              size: 16,
            ),
            SizedBox(width: DesignSystem.spacing4),
            Text(
              symbol,
              style: DesignSystem.labelS.copyWith(
                color: DesignSystem.textSecondary,
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
          ],
        ),
        Text(
          formattedVolume,
          style: DesignSystem.labelS.copyWith(
            color: volume == 0.0 
                ? DesignSystem.textTertiary 
                : DesignSystem.accentOrange.withOpacity(0.9),
            fontWeight: FontWeight.w600,
            fontSize: 16,
          ),
        ),
      ],
    );
  }

  /// Build numeric scale exactly like screenshot - enhanced with better height and icon
  Widget _buildScreenshotNumericScale() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing16),
      decoration: _buildDarkContainerDecoration(), // Uses sentiment color
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Header with icon
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.psychology_outlined,
                color: DesignSystem.getSentimentColor(_indicatorValue).withOpacity(0.9),
                size: 18,
              ),
              SizedBox(width: DesignSystem.spacing4),
              Text(
                'Market Sentiment',
                style: DesignSystem.labelS.copyWith(
                  color: DesignSystem.getSentimentColor(_indicatorValue).withOpacity(0.9),
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),

          SizedBox(height: DesignSystem.spacing8),

          // Large circular indicator
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  DesignSystem.getSentimentColor(_indicatorValue).withOpacity(0.8),
                  DesignSystem.getSentimentColor(_indicatorValue).withOpacity(0.4),
                  DesignSystem.getSentimentColor(_indicatorValue).withOpacity(0.1),
                ],
                stops: const [0.0, 0.7, 1.0],
              ),
              border: Border.all(
                color: DesignSystem.getSentimentColor(_indicatorValue).withOpacity(0.6),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: DesignSystem.getSentimentColor(_indicatorValue).withOpacity(0.3),
                  blurRadius: 20,
                  spreadRadius: 3,
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Large number
                Text(
                  '${_indicatorValue.round()}',
                  style: DesignSystem.headingXL.copyWith(
                    color: DesignSystem.textPrimary,
                    fontSize: 32,
                    fontWeight: FontWeight.bold,
                    height: 1.0,
                    shadows: [
                      Shadow(
                        color: Colors.black.withOpacity(0.5),
                        blurRadius: 4,
                        offset: const Offset(1, 1),
                      ),
                    ],
                  ),
                ),
                
                // Level name
                Text(
                  _getSentimentLabel(_indicatorValue),
                  style: DesignSystem.labelS.copyWith(
                    color: DesignSystem.textSecondary,
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),

          SizedBox(height: DesignSystem.spacing8),

          // Gradient bar
          LayoutBuilder(
            builder: (context, constraints) {
              final barWidth = constraints.maxWidth;
              return Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Stack(
                    children: [
                      // Gradient bar
                      Container(
                        height: 12,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [
                              Color(0xFFFF3B30), // Red
                              Color(0xFFFF9500), // Orange  
                              Color(0xFFFFCC00), // Yellow
                              Color(0xFFAED581), // Lime
                              Color(0xFF34C759), // Green
                            ],
                          ),
                          borderRadius: BorderRadius.circular(6),
                        ),
                      ),
                      
                      // White circle indicator
                      Positioned(
                        left: (_indicatorValue / 100) * (barWidth - 12),
                        top: 0,
                        child: Container(
                          width: 12,
                          height: 12,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: DesignSystem.getSentimentColor(_indicatorValue),
                              width: 2,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.3),
                                blurRadius: 4,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: DesignSystem.spacing4),

                  // Scale labels
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('0', style: DesignSystem.labelS.copyWith(color: DesignSystem.textTertiary, fontSize: 10)),
                      Text('25', style: DesignSystem.labelS.copyWith(color: DesignSystem.textTertiary, fontSize: 10)),
                      Text('50', style: DesignSystem.labelS.copyWith(color: DesignSystem.textTertiary, fontSize: 10)),
                      Text('75', style: DesignSystem.labelS.copyWith(color: DesignSystem.textTertiary, fontSize: 10)),
                      Text('100', style: DesignSystem.labelS.copyWith(color: DesignSystem.textTertiary, fontSize: 10)),
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }



  /// Build predictions exactly like screenshot
  Widget _buildScreenshotPredictions() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing8),
      decoration: _buildDarkContainerDecoration(accentColor: DesignSystem.accentGreen),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.auto_graph,
                color: DesignSystem.textSecondary,
                size: 16,
              ),
              SizedBox(width: DesignSystem.spacing4),
              Text(
                'Enhanced Prognosis',
                style: DesignSystem.labelS.copyWith(
                  color: DesignSystem.accentGreen.withOpacity(0.9),
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                ),
              ),
            ],
          ),

          SizedBox(height: DesignSystem.spacing8),

          // Short-term section exactly like screenshot
          Text(
            'Short-term (3 days)',
            style: DesignSystem.labelS.copyWith(
              color: DesignSystem.textSecondary,
              fontSize: 16,
            ),
          ),
          
          SizedBox(height: DesignSystem.spacing4),
          
          _buildSimplePredictionRow('Tomorrow', '63.1', '+2.4'),
          SizedBox(height: DesignSystem.spacing8),
          _buildSimplePredictionRow('Day 2', '65.3', '+5.2'),
          SizedBox(height: DesignSystem.spacing8),
          _buildSimplePredictionRow('Day 3', '67.2', '+7.8'),

          SizedBox(height: DesignSystem.spacing16),

          // Medium-term section exactly like screenshot
          Text(
            'Medium-term (7 days)',
            style: DesignSystem.labelS.copyWith(
              color: DesignSystem.textSecondary,
              fontSize: 16,
            ),
          ),
          
          SizedBox(height: DesignSystem.spacing4),
          
          _buildSimplePredictionRow('Day 4', '68.5', '+1.3'),
          SizedBox(height: DesignSystem.spacing8),
          _buildSimplePredictionRow('Day 5', '69.3', '+1.4'),
          SizedBox(height: DesignSystem.spacing8),
          _buildSimplePredictionRow('Week', '70.9', '+1.6'),
        ],
      ),
    );
  }

  /// Build simple prediction row like screenshot with enhanced colors
  Widget _buildSimplePredictionRow(String label, String value, String change) {
    final isPositive = change.startsWith('+');
    final changeColor = isPositive ? DesignSystem.accentGreen : DesignSystem.accentRed;
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: DesignSystem.labelS.copyWith(
            color: DesignSystem.textSecondary,
            fontSize: 16,
          ),
        ),
        Row(
          children: [
            Text(
              value,
              style: DesignSystem.labelS.copyWith(
                color: DesignSystem.accentGreen.withOpacity(0.9),
                fontWeight: FontWeight.w600,
                fontSize: 16,
              ),
            ),
            SizedBox(width: DesignSystem.spacing4),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: DesignSystem.spacing4,
                vertical: DesignSystem.spacing2,
              ),
              decoration: BoxDecoration(
                color: changeColor.withOpacity(0.15),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: changeColor.withOpacity(0.3),
                  width: 0.5,
                ),
              ),
              child: Text(
                change,
                style: DesignSystem.labelS.copyWith(
                  color: changeColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 12,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build trend summary exactly like screenshot
  Widget _buildScreenshotTrendSummary() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing8),
      decoration: _buildDarkContainerDecoration(accentColor: DesignSystem.accentOrange),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Trend Summary',
            style: DesignSystem.labelS.copyWith(
              color: DesignSystem.accentOrange.withOpacity(0.9),
              fontWeight: FontWeight.w600,
              fontSize: 18,
            ),
          ),

          SizedBox(height: DesignSystem.spacing4),

          // Trend info exactly like screenshot
          Row(
            children: [
              Icon(
                Icons.trending_up,
                color: DesignSystem.textSecondary,
                size: 14,
              ),
              SizedBox(width: DesignSystem.spacing4),
              Text(
                'Direction: Bullish',
                style: DesignSystem.labelS.copyWith(
                  color: DesignSystem.textSecondary,
                  fontSize: 16,
                ),
              ),
            ],
          ),
          
          SizedBox(height: DesignSystem.spacing2),
          
          Row(
            children: [
              Icon(
                Icons.verified_outlined,
                color: DesignSystem.textSecondary,
                size: 14,
              ),
              SizedBox(width: DesignSystem.spacing4),
                              Text(
                  'Confidence: 78%',
                  style: DesignSystem.labelS.copyWith(
                    color: DesignSystem.textSecondary,
                    fontSize: 16,
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build market summary exactly like screenshot
  Widget _buildScreenshotMarketSummary() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing8),
      decoration: _buildDarkContainerDecoration(accentColor: const Color(0xFF6366F1)), // Purple accent
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.assessment,
                color: DesignSystem.textSecondary,
                size: 20,
              ),
              SizedBox(width: DesignSystem.spacing8),
              Text(
                'Market Summary',
                style: DesignSystem.labelS.copyWith(
                  color: const Color(0xFF6366F1).withOpacity(0.9),
                  fontWeight: FontWeight.w600,
                  fontSize: 18,
                ),
              ),
            ],
          ),

          SizedBox(height: DesignSystem.spacing16),

          // Market status exactly like screenshot
          Expanded(
            child: Column(
              children: [
                _buildSimpleMetricRow('Status', 'Lift'),
                SizedBox(height: DesignSystem.spacing8),
                _buildSimpleMetricRow('Trend', 'Bullish'),
                SizedBox(height: DesignSystem.spacing8),
                _buildSimpleMetricRow('Volume', 'High'),
                SizedBox(height: DesignSystem.spacing8),
                _buildSimpleMetricRow('Volatility', 'Moderate'),
                SizedBox(height: DesignSystem.spacing8),
                _buildSimpleMetricRow('Support', '42'),
                SizedBox(height: DesignSystem.spacing8),
                _buildSimpleMetricRow('Resistance', '58'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build beautiful enhanced metrics for Enhanced Mode
  Widget _buildBeautifulEnhancedMetrics() {
    final sentimentColor = DesignSystem.getSentimentColor(_indicatorValue);

    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.black.withAlpha(77),
            Colors.black.withAlpha(51),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
        border: Border.all(
          color: sentimentColor.withAlpha(77),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: sentimentColor.withAlpha(26),
            blurRadius: 20,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with glow effect
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(DesignSystem.spacing8),
                decoration: BoxDecoration(
                  color: sentimentColor.withAlpha(38),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: sentimentColor.withAlpha(77),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.analytics_outlined,
                  color: sentimentColor,
                  size: 24,
                ),
              ),
              SizedBox(width: DesignSystem.spacing16),
              Expanded(
                child: Text(
                  'Advanced Analytics',
                  style: DesignSystem.headingM.copyWith(
                    color: sentimentColor,
                    fontWeight: FontWeight.bold,
                    letterSpacing: DesignSystem.letterSpacingTight,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: DesignSystem.spacing24),

          // Beautiful metrics with enhanced styling
          Expanded(
            child: Column(
              children: [
                _buildBeautifulMetricRow('Trend Strength', '${_cachedTrendStrength.toStringAsFixed(1)}', 
                    _cachedTrendStrength > 0 ? DesignSystem.accentGreen : DesignSystem.accentRed,
                    Icons.trending_up),
                SizedBox(height: DesignSystem.spacing16),
                _buildBeautifulMetricRow('Volatility', '${_cachedVolatility.toStringAsFixed(1)}%', 
                    _cachedVolatility > 15 ? DesignSystem.accentOrange : DesignSystem.accentBlue,
                    Icons.show_chart),
                SizedBox(height: DesignSystem.spacing16),
                _buildBeautifulMetricRow('Momentum', '${_cachedMomentum.toStringAsFixed(1)}', 
                    _cachedMomentum > 0 ? DesignSystem.accentGreen : DesignSystem.accentRed,
                    Icons.speed),
                SizedBox(height: DesignSystem.spacing16),
                _buildBeautifulMetricRow('Efficiency', '${_cachedMarketEfficiency.toStringAsFixed(0)}%', 
                    _cachedMarketEfficiency > 60 ? DesignSystem.accentGreen : DesignSystem.accentOrange,
                    Icons.auto_graph),
                SizedBox(height: DesignSystem.spacing16),
                _buildBeautifulMetricRow('RSI', '${_cachedRSI.toStringAsFixed(0)}', 
                    _cachedRSI > 70 ? DesignSystem.accentRed : _cachedRSI < 30 ? DesignSystem.accentGreen : DesignSystem.accentBlue,
                    Icons.stacked_line_chart),
                SizedBox(height: DesignSystem.spacing16),
                _buildBeautifulMetricRow('MACD', '${_cachedMACD.toStringAsFixed(2)}', 
                    _cachedMACD > 0 ? DesignSystem.accentGreen : DesignSystem.accentRed,
                    Icons.timeline),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build beautiful metric row with enhanced styling
  Widget _buildBeautifulMetricRow(String label, String value, Color color, IconData icon) {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            color.withAlpha(26),
            color.withAlpha(13),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignSystem.borderRadiusM),
        border: Border.all(
          color: color.withAlpha(51),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(DesignSystem.spacing8),
            decoration: BoxDecoration(
              color: color.withAlpha(51),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 20,
            ),
          ),
          SizedBox(width: DesignSystem.spacing16),
          Expanded(
            child: Text(
              label,
              style: DesignSystem.labelL.copyWith(
                color: DesignSystem.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            value,
            style: DesignSystem.headingM.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Build beautiful numeric scale for Enhanced Mode
  Widget _buildBeautifulNumericScale() {
    final sentimentColor = DesignSystem.getSentimentColor(_indicatorValue);

    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.black.withAlpha(77),
            Colors.black.withAlpha(51),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
        border: Border.all(
          color: sentimentColor.withAlpha(77),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: sentimentColor.withAlpha(26),
            blurRadius: 20,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Header with glow effect
          Container(
            padding: EdgeInsets.symmetric(horizontal: DesignSystem.spacing16, vertical: DesignSystem.spacing8),
            decoration: BoxDecoration(
              color: sentimentColor.withAlpha(26),
              borderRadius: BorderRadius.circular(DesignSystem.borderRadiusM),
            ),
            child: Text(
              'Market Sentiment',
              style: DesignSystem.headingM.copyWith(
                color: sentimentColor,
                fontWeight: FontWeight.bold,
                letterSpacing: DesignSystem.letterSpacingTight,
              ),
            ),
          ),

          SizedBox(height: DesignSystem.spacing32),

          // Large value display with glow
          Container(
            padding: EdgeInsets.all(DesignSystem.spacing16),
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  sentimentColor.withAlpha(51),
                  Colors.transparent,
                ],
              ),
            ),
            child: Text(
              _indicatorValue.round().toString(),
              style: DesignSystem.headingXL.copyWith(
                color: sentimentColor,
                fontSize: 72,
                fontWeight: FontWeight.bold,
                shadows: [
                  Shadow(
                    color: sentimentColor.withAlpha(77),
                    blurRadius: 10,
                  ),
                ],
              ),
            ),
          ),

          SizedBox(height: DesignSystem.spacing16),

          // Level name with beautiful styling
          Container(
            padding: EdgeInsets.symmetric(horizontal: DesignSystem.spacing16, vertical: DesignSystem.spacing8),
            decoration: BoxDecoration(
              color: sentimentColor.withAlpha(26),
              borderRadius: BorderRadius.circular(DesignSystem.borderRadiusM),
              border: Border.all(
                color: sentimentColor.withAlpha(51),
                width: 1,
              ),
            ),
            child: Text(
              _getCurrentLevel(_indicatorValue),
              style: DesignSystem.headingM.copyWith(
                color: sentimentColor,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          SizedBox(height: DesignSystem.spacing32),

          // Beautiful gradient bar with enhanced indicator
          LayoutBuilder(
            builder: (context, constraints) {
              final barWidth = constraints.maxWidth;
              return Column(
                children: [
                  Stack(
                    children: [
                      // Gradient bar with glow
                      Container(
                        height: 12,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          gradient: const LinearGradient(
                            colors: [
                              Color(0xFFFF3B30), // Red
                              Color(0xFFFF9500), // Orange
                              Color(0xFFFFCC00), // Yellow
                              Color(0xFFAED581), // Lime
                              Color(0xFF34C759), // Green
                            ],
                          ),
                          borderRadius: BorderRadius.circular(6),
                          boxShadow: [
                            BoxShadow(
                              color: sentimentColor.withAlpha(77),
                              blurRadius: 8,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                      ),
                      
                      // Enhanced moveable indicator
                      Positioned(
                        left: (_indicatorValue / 100) * (barWidth - 24),
                        top: -6,
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: sentimentColor,
                              width: 3,
                            ),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withAlpha(77),
                                blurRadius: 8,
                                spreadRadius: 2,
                              ),
                              BoxShadow(
                                color: sentimentColor.withAlpha(77),
                                blurRadius: 12,
                                spreadRadius: 3,
                              ),
                            ],
                          ),
                          child: Center(
                            child: Container(
                              width: 8,
                              height: 8,
                              decoration: BoxDecoration(
                                color: sentimentColor,
                                shape: BoxShape.circle,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),

                  SizedBox(height: DesignSystem.spacing16),

                  // Enhanced scale labels
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildScaleLabel('0', 'Crash'),
                      _buildScaleLabel('25', 'Anxiety'),
                      _buildScaleLabel('50', 'Stasis'),
                      _buildScaleLabel('75', 'Lift'),
                      _buildScaleLabel('100', 'Surge'),
                    ],
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }

  /// Build scale label with enhanced styling
  Widget _buildScaleLabel(String value, String label) {
    return Column(
      children: [
        Text(
          value,
          style: DesignSystem.labelM.copyWith(
            color: DesignSystem.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: DesignSystem.spacing4),
        Text(
          label,
          style: DesignSystem.labelS.copyWith(
            color: DesignSystem.textSecondary,
          ),
        ),
      ],
    );
  }

  /// Build beautiful predictions for Enhanced Mode
  Widget _buildBeautifulPredictions() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [
            Colors.black.withAlpha(77),
            Colors.black.withAlpha(51),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
        border: Border.all(
          color: DesignSystem.accentBlue.withAlpha(77),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: DesignSystem.accentBlue.withAlpha(26),
            blurRadius: 20,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with glow effect
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(DesignSystem.spacing8),
                decoration: BoxDecoration(
                  color: DesignSystem.accentBlue.withAlpha(38),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: DesignSystem.accentBlue.withAlpha(77),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.auto_graph,
                  color: DesignSystem.accentBlue,
                  size: 24,
                ),
              ),
              SizedBox(width: DesignSystem.spacing16),
              Expanded(
                child: Text(
                  'Enhanced Prognosis',
                  style: DesignSystem.headingM.copyWith(
                    color: DesignSystem.accentBlue,
                    fontWeight: FontWeight.bold,
                    letterSpacing: DesignSystem.letterSpacingTight,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: DesignSystem.spacing24),

          // Beautiful predictions list
          Expanded(
            child: Column(
              children: [
                // Short-term section
                _buildPredictionSection('Short-term (3 days)', [
                  if (_predictions.isNotEmpty) 
                    _buildBeautifulPredictionItem('Tomorrow', _predictions[0].value, Icons.today),
                  if (_predictions.length > 1) 
                    _buildBeautifulPredictionItem('Day 2', _predictions[1].value, Icons.calendar_today),
                  if (_predictions.length > 2) 
                    _buildBeautifulPredictionItem('Day 3', _predictions[2].value, Icons.date_range),
                ]),

                SizedBox(height: DesignSystem.spacing24),

                // Medium-term section
                _buildPredictionSection('Medium-term (7 days)', [
                  if (_predictions.length > 6) 
                    _buildBeautifulPredictionItem('Week', _predictions[6].value, Icons.event_note),
                ]),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build prediction section with header
  Widget _buildPredictionSection(String title, List<Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: DesignSystem.spacing16, vertical: DesignSystem.spacing8),
          decoration: BoxDecoration(
            color: DesignSystem.accentBlue.withAlpha(26),
            borderRadius: BorderRadius.circular(DesignSystem.borderRadiusM),
          ),
          child: Text(
            title,
            style: DesignSystem.labelL.copyWith(
              color: DesignSystem.accentBlue,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        SizedBox(height: DesignSystem.spacing16),
        ...items,
      ],
    );
  }

  /// Build beautiful prediction item
  Widget _buildBeautifulPredictionItem(String label, double value, IconData icon) {
    final sentimentColor = DesignSystem.getSentimentColor(value);
    final change = value - _indicatorValue;
    final isPositive = change >= 0;

    return Container(
      margin: EdgeInsets.only(bottom: DesignSystem.spacing8),
      padding: EdgeInsets.all(DesignSystem.spacing16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            sentimentColor.withAlpha(26),
            sentimentColor.withAlpha(13),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignSystem.borderRadiusM),
        border: Border.all(
          color: sentimentColor.withAlpha(51),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(DesignSystem.spacing8),
            decoration: BoxDecoration(
              color: sentimentColor.withAlpha(51),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: sentimentColor,
              size: 16,
            ),
          ),
          SizedBox(width: DesignSystem.spacing16),
          Expanded(
            child: Text(
              label,
              style: DesignSystem.labelL.copyWith(
                color: DesignSystem.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Row(
            children: [
              Text(
                value.toStringAsFixed(1),
                style: DesignSystem.headingS.copyWith(
                  color: sentimentColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(width: DesignSystem.spacing8),
              Container(
                padding: EdgeInsets.all(DesignSystem.spacing4),
                decoration: BoxDecoration(
                  color: (isPositive ? DesignSystem.accentGreen : DesignSystem.accentRed).withAlpha(26),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                  color: isPositive ? DesignSystem.accentGreen : DesignSystem.accentRed,
                  size: 16,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build beautiful trend visualization
  Widget _buildBeautifulTrendVisualization() {
    final sentimentColor = DesignSystem.getSentimentColor(_indicatorValue);

    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.black.withAlpha(77),
            Colors.black.withAlpha(51),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
        border: Border.all(
          color: sentimentColor.withAlpha(77),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: sentimentColor.withAlpha(26),
            blurRadius: 20,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with glow effect
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(DesignSystem.spacing8),
                decoration: BoxDecoration(
                  color: sentimentColor.withAlpha(38),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: sentimentColor.withAlpha(77),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.show_chart,
                  color: sentimentColor,
                  size: 24,
                ),
              ),
              SizedBox(width: DesignSystem.spacing16),
              Expanded(
                child: Text(
                  'Trend Visualization',
                  style: DesignSystem.headingM.copyWith(
                    color: sentimentColor,
                    fontWeight: FontWeight.bold,
                    letterSpacing: DesignSystem.letterSpacingTight,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: DesignSystem.spacing24),

          // Beautiful trend chart
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    Colors.black.withAlpha(51),
                    Colors.black.withAlpha(26),
                  ],
                ),
                borderRadius: BorderRadius.circular(DesignSystem.borderRadiusM),
                border: Border.all(
                  color: sentimentColor.withAlpha(51),
                  width: 1,
                ),
              ),
              child: _predictions.length >= 2 
                ? CustomPaint(
                    painter: TrendLinePainter(
                      dataPoints: [_indicatorValue] + _predictions.take(4).map((p) => p.value).toList(),
                      color: sentimentColor,
                    ),
                    size: Size.infinite,
                  )
                : Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.trending_flat,
                          color: DesignSystem.textTertiary,
                          size: 48,
                        ),
                        SizedBox(height: DesignSystem.spacing8),
                        Text(
                          'Insufficient data',
                          style: DesignSystem.labelL.copyWith(
                            color: DesignSystem.textTertiary,
                          ),
                        ),
                      ],
                    ),
                  ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build beautiful market summary
  Widget _buildBeautifulMarketSummary() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topRight,
          end: Alignment.bottomLeft,
          colors: [
            Colors.black.withAlpha(77),
            Colors.black.withAlpha(51),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
        border: Border.all(
          color: DesignSystem.accentBlue.withAlpha(77),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: DesignSystem.accentBlue.withAlpha(26),
            blurRadius: 20,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with glow effect
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(DesignSystem.spacing8),
                decoration: BoxDecoration(
                  color: DesignSystem.accentBlue.withAlpha(38),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: DesignSystem.accentBlue.withAlpha(77),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Icon(
                  Icons.assessment,
                  color: DesignSystem.accentBlue,
                  size: 24,
                ),
              ),
              SizedBox(width: DesignSystem.spacing16),
              Expanded(
                child: Text(
                  'Market Summary',
                  style: DesignSystem.headingM.copyWith(
                    color: DesignSystem.accentBlue,
                    fontWeight: FontWeight.bold,
                    letterSpacing: DesignSystem.letterSpacingTight,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: DesignSystem.spacing24),

          // Beautiful market status
          Expanded(
            child: Column(
              children: [
                _buildBeautifulSummaryRow('Status', _getCurrentLevel(_indicatorValue), 
                    DesignSystem.getSentimentColor(_indicatorValue), Icons.flag),
                SizedBox(height: DesignSystem.spacing16),
                _buildBeautifulSummaryRow('Trend', _cachedTrendStrength > 0 ? 'Bullish' : 'Bearish', 
                    _cachedTrendStrength > 0 ? DesignSystem.accentGreen : DesignSystem.accentRed, Icons.trending_up),
                SizedBox(height: DesignSystem.spacing16),
                _buildBeautifulSummaryRow('Volume', _cachedVolatility > 15 ? 'High' : 'Moderate', 
                    _cachedVolatility > 15 ? DesignSystem.accentOrange : DesignSystem.accentBlue, Icons.bar_chart),
                SizedBox(height: DesignSystem.spacing16),
                _buildBeautifulSummaryRow('Volatility', _cachedVolatility > 15 ? 'High' : 'Moderate', 
                    _cachedVolatility > 15 ? DesignSystem.accentRed : DesignSystem.accentGreen, Icons.show_chart),
                SizedBox(height: DesignSystem.spacing16),
                _buildBeautifulSummaryRow('Support', '${_cachedSupportLevel.toStringAsFixed(0)}', 
                    DesignSystem.accentGreen, Icons.support),
                SizedBox(height: DesignSystem.spacing16),
                _buildBeautifulSummaryRow('Resistance', '${_cachedResistanceLevel.toStringAsFixed(0)}', 
                    DesignSystem.accentRed, Icons.block),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build beautiful summary row
  Widget _buildBeautifulSummaryRow(String label, String value, Color color, IconData icon) {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
          colors: [
            color.withAlpha(26),
            color.withAlpha(13),
          ],
        ),
        borderRadius: BorderRadius.circular(DesignSystem.borderRadiusM),
        border: Border.all(
          color: color.withAlpha(51),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(DesignSystem.spacing8),
            decoration: BoxDecoration(
              color: color.withAlpha(51),
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 16,
            ),
          ),
          SizedBox(width: DesignSystem.spacing16),
          Expanded(
            child: Text(
              label,
              style: DesignSystem.labelL.copyWith(
                color: DesignSystem.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Text(
            value,
            style: DesignSystem.headingS.copyWith(
              color: color,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  /// Build compact enhanced metrics for Enhanced Mode
  Widget _buildCompactEnhancedMetrics() {
    final sentimentColor = DesignSystem.getSentimentColor(_indicatorValue);

    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing8),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(51),
        borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
        border: Border.all(
          color: sentimentColor.withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.analytics_outlined,
                color: sentimentColor,
                size: 20,
              ),
              SizedBox(width: DesignSystem.spacing8),
              Expanded(
                child: Text(
                  'Advanced Analytics',
                  style: DesignSystem.headingS.copyWith(
                    color: sentimentColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: DesignSystem.spacing8),

          // Compact metrics
          _buildCompactMetricRow('Trend', '${_cachedTrendStrength.toStringAsFixed(1)}', 
              _cachedTrendStrength > 0 ? DesignSystem.accentGreen : DesignSystem.accentRed),
          SizedBox(height: DesignSystem.spacing8),
          _buildCompactMetricRow('Volatility', '${_cachedVolatility.toStringAsFixed(1)}%', 
              _cachedVolatility > 15 ? DesignSystem.accentOrange : DesignSystem.accentBlue),
          SizedBox(height: DesignSystem.spacing8),
          _buildCompactMetricRow('Momentum', '${_cachedMomentum.toStringAsFixed(1)}', 
              _cachedMomentum > 0 ? DesignSystem.accentGreen : DesignSystem.accentRed),
          SizedBox(height: DesignSystem.spacing8),
          _buildCompactMetricRow('Efficiency', '${_cachedMarketEfficiency.toStringAsFixed(0)}%', 
              _cachedMarketEfficiency > 60 ? DesignSystem.accentGreen : DesignSystem.accentOrange),
          SizedBox(height: DesignSystem.spacing8),
          _buildCompactMetricRow('RSI', '${_cachedRSI.toStringAsFixed(0)}', 
              _cachedRSI > 70 ? DesignSystem.accentRed : _cachedRSI < 30 ? DesignSystem.accentGreen : DesignSystem.accentBlue),
          SizedBox(height: DesignSystem.spacing8),
          _buildCompactMetricRow('MACD', '${_cachedMACD.toStringAsFixed(2)}', 
              _cachedMACD > 0 ? DesignSystem.accentGreen : DesignSystem.accentRed),
        ],
      ),
    );
  }

  /// Build compact metric row
  Widget _buildCompactMetricRow(String label, String value, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: DesignSystem.labelM.copyWith(
            color: DesignSystem.textSecondary,
          ),
        ),
        Text(
          value,
          style: DesignSystem.labelL.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// Build compact numeric scale for Enhanced Mode
  Widget _buildCompactNumericScale() {
    final sentimentColor = DesignSystem.getSentimentColor(_indicatorValue);

    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing16),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(51),
        borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
        border: Border.all(
          color: sentimentColor.withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Header
          Text(
            'Market Sentiment',
            style: DesignSystem.headingS.copyWith(
              color: DesignSystem.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),

          SizedBox(height: DesignSystem.spacing16),

          // Large value display
          Text(
            _indicatorValue.round().toString(),
            style: DesignSystem.headingXL.copyWith(
              color: sentimentColor,
              fontSize: 60,
              fontWeight: FontWeight.bold,
            ),
          ),

          SizedBox(height: DesignSystem.spacing8),

          // Level name
          Text(
            _getCurrentLevel(_indicatorValue),
            style: DesignSystem.headingM.copyWith(
              color: sentimentColor,
              fontWeight: FontWeight.bold,
            ),
          ),

          SizedBox(height: DesignSystem.spacing16),

          // Horizontal gradient bar with moveable indicator
          LayoutBuilder(
            builder: (context, constraints) {
              final barWidth = constraints.maxWidth;
              return Stack(
                children: [
                  // Gradient bar
                  Container(
                    height: 8,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [
                          Color(0xFFFF3B30), // Red
                          Color(0xFFFF9500), // Orange
                          Color(0xFFFFCC00), // Yellow
                          Color(0xFFAED581), // Lime
                          Color(0xFF34C759), // Green
                        ],
                      ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),
                  
                  // Moveable white circle indicator
                  Positioned(
                    left: (_indicatorValue / 100) * (barWidth - 16), // Dynamic width adjustment
                    top: -4, // Center vertically on the bar
                    child: Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(77),
                            blurRadius: 4,
                            spreadRadius: 1,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),

          SizedBox(height: DesignSystem.spacing8),

          // Scale labels
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('0', style: DesignSystem.labelS.copyWith(color: DesignSystem.textSecondary)),
              Text('25', style: DesignSystem.labelS.copyWith(color: DesignSystem.textSecondary)),
              Text('50', style: DesignSystem.labelS.copyWith(color: DesignSystem.textSecondary)),
              Text('75', style: DesignSystem.labelS.copyWith(color: DesignSystem.textSecondary)),
              Text('100', style: DesignSystem.labelS.copyWith(color: DesignSystem.textSecondary)),
            ],
          ),
        ],
      ),
    );
  }

  /// Build compact predictions for Enhanced Mode
  Widget _buildCompactPredictionsForEnhanced() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing8),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(51),
        borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
        border: Border.all(
          color: DesignSystem.accentBlue.withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.auto_graph,
                color: DesignSystem.accentBlue,
                size: 20,
              ),
              SizedBox(width: DesignSystem.spacing8),
              Expanded(
                child: Text(
                  'Enhanced Prognosis',
                  style: DesignSystem.headingS.copyWith(
                    color: DesignSystem.accentBlue,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          SizedBox(height: DesignSystem.spacing8),

          // Short-term predictions (3 days)
          Text(
            'Short-term (3 days)',
            style: DesignSystem.labelM.copyWith(
              color: DesignSystem.textSecondary,
            ),
          ),
          SizedBox(height: DesignSystem.spacing8),

          if (_predictions.isNotEmpty) ...[
            _buildCompactPredictionItem('Tomorrow', _predictions[0].value),
            if (_predictions.length > 1) ...[
              SizedBox(height: DesignSystem.spacing4),
              _buildCompactPredictionItem('Day 2', _predictions[1].value),
            ],
            if (_predictions.length > 2) ...[
              SizedBox(height: DesignSystem.spacing4),
              _buildCompactPredictionItem('Day 3', _predictions[2].value),
            ],
          ],

          SizedBox(height: DesignSystem.spacing8),

          // Medium-term predictions (4-7 days)
          Text(
            'Medium-term (4-7 days)',
            style: DesignSystem.labelM.copyWith(
              color: DesignSystem.textSecondary,
            ),
          ),
          SizedBox(height: DesignSystem.spacing8),

          if (_predictions.length > 3) ...[
            _buildCompactPredictionItem('Week', _predictions[6].value),
          ],
        ],
      ),
    );
  }

  /// Build compact prediction item
  Widget _buildCompactPredictionItem(String label, double value) {
    final sentimentColor = DesignSystem.getSentimentColor(value);
    final change = value - _indicatorValue;
    final isPositive = change >= 0;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: DesignSystem.labelS.copyWith(
            color: DesignSystem.textSecondary,
          ),
        ),
        Row(
          children: [
            Text(
              value.toStringAsFixed(1),
              style: DesignSystem.labelM.copyWith(
                color: sentimentColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(width: DesignSystem.spacing4),
            Icon(
              isPositive ? Icons.arrow_upward : Icons.arrow_downward,
              color: isPositive ? DesignSystem.accentGreen : DesignSystem.accentRed,
              size: 12,
            ),
          ],
        ),
      ],
    );
  }

  /// Build compact trend visualization
  Widget _buildCompactTrendVisualization() {
    final sentimentColor = DesignSystem.getSentimentColor(_indicatorValue);

    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing8),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(51),
        borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
        border: Border.all(
          color: sentimentColor.withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.show_chart,
                color: sentimentColor,
                size: 20,
              ),
              SizedBox(width: DesignSystem.spacing8),
              Text(
                'Trend Visualization',
                style: DesignSystem.headingS.copyWith(
                  color: sentimentColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          SizedBox(height: DesignSystem.spacing8),

          // Simple trend chart
          Container(
            height: 120, // Fixed height instead of Expanded
            width: double.infinity,
            decoration: BoxDecoration(
              color: Colors.black.withAlpha(26),
              borderRadius: BorderRadius.circular(DesignSystem.borderRadiusM),
            ),
            child: _predictions.length >= 2 
              ? CustomPaint(
                  painter: TrendLinePainter(
                    dataPoints: [_indicatorValue] + _predictions.take(4).map((p) => p.value).toList(),
                    color: sentimentColor,
                  ),
                  size: Size.infinite,
                )
              : Center(
                  child: Text(
                    'Insufficient data',
                    style: DesignSystem.labelS.copyWith(
                      color: DesignSystem.textTertiary,
                    ),
                  ),
                ),
          ),
        ],
      ),
    );
  }

  /// Build market summary
  Widget _buildMarketSummary() {
    return Container(
      padding: EdgeInsets.all(DesignSystem.spacing8),
      decoration: BoxDecoration(
        color: Colors.black.withAlpha(51),
        borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
        border: Border.all(
          color: DesignSystem.accentBlue.withAlpha(51),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.assessment,
                color: DesignSystem.accentBlue,
                size: 20,
              ),
              SizedBox(width: DesignSystem.spacing8),
              Text(
                'Market Summary',
                style: DesignSystem.headingS.copyWith(
                  color: DesignSystem.accentBlue,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),

          SizedBox(height: DesignSystem.spacing8),

          // Market status
          _buildSummaryRow('Status', _getCurrentLevel(_indicatorValue), 
              DesignSystem.getSentimentColor(_indicatorValue)),
          SizedBox(height: DesignSystem.spacing8),
          _buildSummaryRow('Trend', _cachedTrendStrength > 0 ? 'Bullish' : 'Bearish', 
              _cachedTrendStrength > 0 ? DesignSystem.accentGreen : DesignSystem.accentRed),
          SizedBox(height: DesignSystem.spacing8),
          _buildSummaryRow('Volume', _cachedVolatility > 15 ? 'High' : 'Moderate', 
              _cachedVolatility > 15 ? DesignSystem.accentOrange : DesignSystem.accentBlue),
          SizedBox(height: DesignSystem.spacing8),
          _buildSummaryRow('Volatility', _cachedVolatility > 15 ? 'High' : 'Moderate', 
              _cachedVolatility > 15 ? DesignSystem.accentRed : DesignSystem.accentGreen),
          SizedBox(height: DesignSystem.spacing8),
          _buildSummaryRow('Support', '${_cachedSupportLevel.toStringAsFixed(0)}', 
              DesignSystem.accentGreen),
          SizedBox(height: DesignSystem.spacing8),
          _buildSummaryRow('Resistance', '${_cachedResistanceLevel.toStringAsFixed(0)}', 
              DesignSystem.accentRed),
        ],
      ),
    );
  }

  /// Build summary row
  Widget _buildSummaryRow(String label, String value, Color color) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: DesignSystem.labelM.copyWith(
            color: DesignSystem.textSecondary,
          ),
        ),
        Text(
          value,
          style: DesignSystem.labelM.copyWith(
            color: color,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// Build enhanced metrics section with vertical layout for Enhanced Mode
  Widget _buildEnhancedMetricsSectionVertical() {
    final sentimentColor = DesignSystem.getSentimentColor(_indicatorValue);

    return Transform.translate(
      offset: const Offset(0, 30), // Смещаем вниз на 30 пикселей
      child: Container(
        width: 270, // Увеличиваем ширину на 20 пикселей (250 + 20 = 270)
        padding: EdgeInsets.all(DesignSystem.spacing16),
        decoration: BoxDecoration(
          color: Colors.black.withAlpha(51),
          borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
          border: Border.all(
            color: sentimentColor.withAlpha(51),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with enhanced mode title
            Row(
              children: [
                Icon(
                  Icons.analytics_outlined,
                  color: sentimentColor,
                  size: 24,
                ),
                SizedBox(width: DesignSystem.spacing8),
                Text(
                  'Advanced Analytics',
                  style: DesignSystem.headingM.copyWith(
                    color: sentimentColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            SizedBox(height: DesignSystem.spacing16),

            // Primary metrics section
            Text(
              'Market Dynamics',
              style: DesignSystem.headingS.copyWith(
                color: DesignSystem.textPrimary,
              ),
            ),

            SizedBox(height: DesignSystem.spacing8),

            // Primary metrics in a column
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildEnhancedMetricItemVertical(
                  'Trend Strength',
                  _cachedTrendStrength.toStringAsFixed(1),
                  Icons.trending_up,
                  _cachedTrendStrength > 0 ? DesignSystem.accentGreen : DesignSystem.accentRed,
                  'Rate of market directional change',
                ),

                SizedBox(height: DesignSystem.spacing8),

                _buildEnhancedMetricItemVertical(
                  'Volatility',
                  '${_cachedVolatility.toStringAsFixed(1)}%',
                  Icons.show_chart,
                  _cachedVolatility > 15 ? DesignSystem.accentOrange : DesignSystem.accentBlue,
                  'Market price fluctuation intensity',
                ),

                SizedBox(height: DesignSystem.spacing8),

                _buildEnhancedMetricItemVertical(
                  'Momentum',
                  _cachedMomentum.toStringAsFixed(1),
                  Icons.speed,
                  _cachedMomentum > 0 ? DesignSystem.accentGreen : DesignSystem.accentRed,
                  'Acceleration of price movement',
                ),
              ],
            ),

            SizedBox(height: DesignSystem.spacing16),

            // Technical indicators section
            Text(
              'Technical Indicators',
              style: DesignSystem.headingS.copyWith(
                color: DesignSystem.textPrimary,
              ),
            ),

            SizedBox(height: DesignSystem.spacing8),

            // Technical indicators in a column
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildEnhancedMetricItemVertical(
                  'Market Efficiency',
                  '${_cachedMarketEfficiency.toStringAsFixed(0)}%',
                  Icons.auto_graph,
                  _cachedMarketEfficiency > 60 ? DesignSystem.accentGreen : DesignSystem.accentOrange,
                  'How directional the market movement is',
                ),

                SizedBox(height: DesignSystem.spacing8),

                _buildEnhancedMetricItemVertical(
                  'RSI',
                  _cachedRSI.toStringAsFixed(0),
                  Icons.stacked_line_chart,
                  _cachedRSI > 70 ? DesignSystem.accentRed :
                  _cachedRSI < 30 ? DesignSystem.accentGreen :
                  DesignSystem.accentBlue,
                  'Relative Strength Index (overbought/oversold)',
                ),

                SizedBox(height: DesignSystem.spacing8),

                _buildEnhancedMetricItemVertical(
                  'MACD',
                  _cachedMACD.toStringAsFixed(2),
                  Icons.show_chart,
                  _cachedMACD > 0 ? DesignSystem.accentGreen : DesignSystem.accentRed,
                  'Moving Average Convergence/Divergence',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build numeric scale for enhanced mode
  Widget _buildNumericScale() {
    final sentimentColor = DesignSystem.getSentimentColor(_indicatorValue);

    return Transform.translate(
      offset: const Offset(-50, 0), // Move 50px left
      child: Padding(
        padding: const EdgeInsets.only(top: 150),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Заголовок
            Text(
              'Market Sentiment',
              style: DesignSystem.headingM.copyWith(
                color: DesignSystem.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 24),

            // Числовая шкала
            Container(
              width: 300,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.black.withAlpha(51),
                borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
                border: Border.all(
                  color: sentimentColor.withAlpha(51),
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  // Текущее значение
                  Text(
                    _indicatorValue.round().toString(),
                    style: DesignSystem.headingXL.copyWith(
                      color: sentimentColor,
                      fontSize: 72,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Название уровня
                  Text(
                    _getCurrentLevel(_indicatorValue),
                    style: DesignSystem.headingM.copyWith(
                      color: sentimentColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Линейная шкала
                  Container(
                    height: 8,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [
                          Color(0xFFFF3B30), // Red - Crash
                          Color(0xFFFF9500), // Orange - Anxiety
                          Color(0xFFFFCC00), // Yellow - Stasis
                          Color(0xFFAED581), // Lime - Lift
                          Color(0xFF34C759), // Green - Surge
                        ],
                      ),
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Метки шкалы
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text('0', style: DesignSystem.labelM.copyWith(color: DesignSystem.textSecondary)),
                      Text('25', style: DesignSystem.labelM.copyWith(color: DesignSystem.textSecondary)),
                      Text('50', style: DesignSystem.labelM.copyWith(color: DesignSystem.textSecondary)),
                      Text('75', style: DesignSystem.labelM.copyWith(color: DesignSystem.textSecondary)),
                      Text('100', style: DesignSystem.labelM.copyWith(color: DesignSystem.textSecondary)),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Индикатор текущего положения
                  Stack(
                    clipBehavior: Clip.none,
                    children: [
                      SizedBox(
                        height: 24,
                        width: double.infinity,
                      ),
                      Positioned(
                        left: (_indicatorValue / 100) * (300 - 32 - 32), // Учитываем padding и ширину индикатора
                        child: Container(
                          width: 24,
                          height: 24,
                          decoration: BoxDecoration(
                            color: sentimentColor,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: sentimentColor.withAlpha(77),
                                blurRadius: 8,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: const Icon(
                            Icons.arrow_drop_down,
                            color: Colors.white,
                            size: 20,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 24),
          ],
        ),
      ),
    );
  }

  /// Build enhanced metric item with vertical layout
  Widget _buildEnhancedMetricItemVertical(String label, String value, IconData icon, Color color, String description) {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(DesignSystem.spacing8),
      decoration: BoxDecoration(
        color: color.withAlpha(26), // withOpacity(0.1) -> withAlpha(26)
        borderRadius: BorderRadius.circular(DesignSystem.borderRadiusM),
        border: Border.all(
          color: color.withAlpha(51), // withOpacity(0.2) -> withAlpha(51)
          width: 1,
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(DesignSystem.spacing8),
            decoration: BoxDecoration(
              color: color.withAlpha(51), // withOpacity(0.2) -> withAlpha(51)
              shape: BoxShape.circle,
            ),
            child: Icon(
              icon,
              color: color,
              size: 24,
            ),
          ),
          SizedBox(width: DesignSystem.spacing8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      label,
                      style: DesignSystem.labelL.copyWith(
                        color: DesignSystem.textSecondary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Text(
                      value,
                      style: DesignSystem.headingM.copyWith(
                        color: color,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: DesignSystem.spacing4),
                Text(
                  description,
                  style: DesignSystem.labelS.copyWith(
                    color: DesignSystem.textTertiary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }



  /// Build the reactor image with premium design
  Widget _buildReactorImage() {
    // Increase size by 1.3x
    final reactorSize = 380 * 1.3;
    final sentimentColor = DesignSystem.getSentimentColor(_indicatorValue);

    return Transform.translate(
      offset: const Offset(0, 50), // Move 50px down (35px + 15px)
      child: SizedBox(
        height: reactorSize,
        width: reactorSize,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Reactor background image with subtle glow
            Container(
              width: reactorSize,
              height: reactorSize,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: sentimentColor.withAlpha(26), // withOpacity(0.1) -> withAlpha(26)
                    blurRadius: 40,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Image.asset(
                'logo/Sinusoid/Reactor.png',
                fit: BoxFit.contain,
                width: reactorSize,
                height: reactorSize,
                errorBuilder: (context, error, stackTrace) {
                  // Show a fallback colored circle if image fails to load
                  return Container(
                    width: reactorSize * 0.95,
                    height: reactorSize * 0.95,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.grey.shade900,
                      border: Border.all(
                        color: sentimentColor,
                        width: 5,
                      ),
                    ),
                  );
                },
              ),
            ),

            // Central color indicator with premium glassmorphism effect
            Transform.translate(
              offset: const Offset(-2, -10), // Move 2px left and 10px up
              child: Center(
                child: Container(
                  width: 230,
                  height: 230,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: sentimentColor.withAlpha(179), // withOpacity(0.7) -> withAlpha(179)
                    boxShadow: [
                      BoxShadow(
                        color: sentimentColor.withAlpha(77), // withOpacity(0.3) -> withAlpha(77)
                        blurRadius: 30,
                        spreadRadius: 10,
                      ),
                    ],
                    border: Border.all(
                      color: Colors.white.withAlpha(26), // withOpacity(0.1) -> withAlpha(26)
                      width: 1,
                    ),
                  ),
                ),
              ),
            ),

            // Animated particles with improved visual effect
            Transform.translate(
              offset: const Offset(-2, -5), // Move 2px left and 5px up
              child: CustomPaint(
                size: Size(reactorSize, reactorSize),
                painter: ReactorAnimationPainter(
                  particles: _particles,
                  animationValue: _animationValue,
                  indicatorValue: _indicatorValue,
                  color: sentimentColor,
                ),
              ),
            ),

            // Removed debug overlay that was causing performance issues

            // Removed clickable phase name under reactor
          ],
        ),
      ),
    );
  }

  /// Build the premium minimalist speedometer indicator
  Widget _buildSpeedometerIndicator() {
    final sentimentColor = DesignSystem.getSentimentColor(_indicatorValue);

    // Уменьшаем размер спидометра в 0.91 раза в Enhanced режиме (1.09 / 1.2 = 0.91)
    final width = _isEnhancedVersion ? 265.0 : 240.0; // 220.8 * 1.2 = 264.96
    final height = _isEnhancedVersion ? 198.7 : 180.0; // 165.6 * 1.2 = 198.72

    return Padding(
      padding: EdgeInsets.only(top: _isEnhancedVersion ? 153 : 120), // Поднимаем на 30 пикселей выше в Enhanced режиме
      child: _isEnhancedVersion
        ? Column(
            children: [
              Stack(
                alignment: Alignment.center,
                children: [
                  // Сначала отображаем спидометр
                  Container(
                    width: width,
                    height: height,
                    decoration: BoxDecoration(
                      boxShadow: [
                        BoxShadow(
                          color: sentimentColor.withAlpha(13),
                          blurRadius: 18,
                          spreadRadius: 1.2,
                        ),
                      ],
                    ),
                    child: CustomPaint(
                      painter: IOSGaugeSpeedometerPainter(
                        value: _indicatorValue,
                        color: sentimentColor,
                      ),
                    ),
                  ),
                  // Отображаем цифровой индикатор внутри спидометра, смещенный на 15 или 22 пикселя вверх в зависимости от режима
                  Transform.translate(
                    offset: Offset(0, _isEnhancedVersion ? -22 : -15),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          _indicatorValue.round().toString(),
                          style: DesignSystem.headingXL.copyWith(color: DesignSystem.textPrimary),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _getCurrentLevel(_indicatorValue),
                          style: DesignSystem.labelL.copyWith(color: sentimentColor, fontSize: 16),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              // Добавляем блок "Советы и стратегии"
              const SizedBox(height: 20),
              Column(
                children: [
                  Text(
                    'Советы и стратегии',
                    style: DesignSystem.labelL.copyWith(color: DesignSystem.textPrimary),
                  ),
                  const SizedBox(height: 10),
                  Container(
                    width: 350,
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: DesignSystem.cardBackground,
                      borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
                      border: Border.all(
                        color: DesignSystem.accentGreen.withAlpha(77),
                        width: 1.0,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: DesignSystem.accentGreen.withAlpha(26),
                          blurRadius: 8,
                          spreadRadius: 0,
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 32,
                          height: 32,
                          decoration: BoxDecoration(
                            color: DesignSystem.accentGreen.withAlpha(38),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.lightbulb_outline,
                            color: DesignSystem.accentGreen,
                            size: 18,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            'Нажмите, чтобы увидеть рекомендации для текущей фазы рынка',
                            style: DesignSystem.bodyS.copyWith(color: DesignSystem.textSecondary),
                          ),
                        ),
                        const Icon(
                          Icons.arrow_forward_ios,
                          color: DesignSystem.accentGreen,
                          size: 14,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          )
        : Container(
            width: width,
            height: height,
            padding: EdgeInsets.only(top: DesignSystem.spacing8),
            decoration: BoxDecoration(
              boxShadow: [
                BoxShadow(
                  color: sentimentColor.withAlpha(13), // withOpacity(0.05) -> withAlpha(13)
                  blurRadius: 20,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: CustomPaint(
              painter: IOSGaugeSpeedometerPainter(
                value: _indicatorValue,
                color: sentimentColor,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  // Value with premium typography
                  Padding(
                    padding: const EdgeInsets.only(bottom: 10),
                    child: Text(
                      _indicatorValue.round().toString(),
                      style: DesignSystem.headingXL.copyWith(color: DesignSystem.textPrimary),
                    ),
                  ),

              // Level with premium typography
              Padding(
                padding: const EdgeInsets.only(bottom: 20),
                child: GestureDetector(
                  onTap: () {
                    _showLevelDescriptionDialog(context, _getCurrentLevel(_indicatorValue));
                  },
                  child: Column(
                    children: [
                      Text(
                        _getCurrentLevel(_indicatorValue),
                        style: DesignSystem.headingM.copyWith(
                          color: sentimentColor,
                        ),
                      ),
                      SizedBox(height: DesignSystem.spacing2),
                      // Premium underline with gradient
                      Container(
                        height: 2,
                        width: 100,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              sentimentColor.withAlpha(77), // withOpacity(0.3) -> withAlpha(77)
                              sentimentColor,
                              sentimentColor.withAlpha(77), // withOpacity(0.3) -> withAlpha(77)
                            ],
                            begin: Alignment.centerLeft,
                            end: Alignment.centerRight,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              const SizedBox(height: 4),

              // Description with premium typography
              Padding(
                padding: const EdgeInsets.only(bottom: 20),
                child: Text(
                  _getCurrentLevelDescription(_indicatorValue),
                  textAlign: TextAlign.center,
                  style: DesignSystem.bodyS,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build signal button that shows recommendation when clicked
  Widget _buildSignalButton() {
    // Determine recommendation based on predictions
    String recommendation;
    Color recommendationColor;
    IconData recommendationIcon;
    String recommendationDescription;

    // Get tomorrow and day after tomorrow predictions
    final tomorrow = _predictions.isNotEmpty ? _predictions[0] : null;
    final dayAfterTomorrow = _predictions.length > 1 ? _predictions[1] : null;

    // Calculate trend based on predictions
    double trendDirection = 0;
    double futureValue = 0;

    if (tomorrow != null) {
      // Calculate initial trend direction from current to tomorrow
      trendDirection = tomorrow.value - _indicatorValue;
      futureValue = tomorrow.value;

      // If we have day after tomorrow prediction, refine the trend
      if (dayAfterTomorrow != null) {
        // Add weight to the trend if it continues in the same direction
        double secondDayTrend = dayAfterTomorrow.value - tomorrow.value;

        // If both days show the same trend direction, strengthen the signal
        if ((trendDirection > 0 && secondDayTrend > 0) ||
            (trendDirection < 0 && secondDayTrend < 0)) {
          trendDirection = (trendDirection + secondDayTrend) * 1.2;
        }

        // Use the furthest prediction for future value
        futureValue = dayAfterTomorrow.value;
      }
    } else {
      // Fallback to current value if no predictions
      futureValue = _indicatorValue;
    }

    // Get volatility from prediction metrics if available
    double volatility = 0;
    if (tomorrow != null && tomorrow.metrics.containsKey('volatility')) {
      volatility = tomorrow.metrics['volatility']!;
    }

    // Determine recommendation based on trend direction and future value
    if (trendDirection > 5 || (trendDirection > 2 && futureValue > 70)) {
      // Strong buy signal - significant upward trend or high future value
      recommendation = "Buy";
      recommendationColor = DesignSystem.accentGreen;
      recommendationIcon = Icons.trending_up;
      recommendationDescription = "Strong bullish trend predicted. Consider increasing positions.";
    } else if (trendDirection > 2 || (trendDirection > 0 && futureValue > 60)) {
      // Moderate buy signal - moderate upward trend
      recommendation = "Buy";
      recommendationColor = DesignSystem.accentGreen.withAlpha(200);
      recommendationIcon = Icons.trending_up;
      recommendationDescription = "Positive trend predicted. Good entry opportunity.";
    } else if (trendDirection.abs() < 2 || (futureValue >= 45 && futureValue <= 55)) {
      // Hold signal - stable trend or neutral future value
      recommendation = "Hold";
      recommendationColor = DesignSystem.sentimentStasis;
      recommendationIcon = Icons.trending_flat;
      recommendationDescription = volatility > 5
          ? "Market expected to remain stable with some volatility. Maintain positions."
          : "Market expected to remain in equilibrium. Maintain current positions.";
    } else if (trendDirection < -2 || (trendDirection < 0 && futureValue < 40)) {
      // Moderate sell signal - moderate downward trend
      recommendation = "Sell";
      recommendationColor = DesignSystem.accentRed.withAlpha(200);
      recommendationIcon = Icons.trending_down;
      recommendationDescription = "Negative trend predicted. Consider reducing exposure.";
    } else if (trendDirection < -5 || (trendDirection < -2 && futureValue < 30)) {
      // Strong sell signal - significant downward trend or low future value
      recommendation = "Sell";
      recommendationColor = DesignSystem.accentRed;
      recommendationIcon = Icons.trending_down;
      recommendationDescription = "Strong bearish trend predicted. Consider exiting positions.";
    } else {
      // Default hold signal if no clear pattern
      recommendation = "Hold";
      recommendationColor = DesignSystem.sentimentStasis;
      recommendationIcon = Icons.trending_flat;
      recommendationDescription = "No clear trend detected. Monitor market conditions.";
    }

    // Create a button that shows a dialog with recommendation details when pressed
    return Center(
      child: GestureDetector(
        onTap: () {
          // Show dialog with recommendation details
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                backgroundColor: const Color(0xFF1A2639), // Solid dark background
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
                ),
                title: Row(
                  children: [
                    Icon(
                      recommendationIcon,
                      color: recommendationColor,
                      size: 24,
                    ),
                    SizedBox(width: DesignSystem.spacing8),
                    Text(
                      'Trading Signal: $recommendation',
                      style: DesignSystem.headingS.copyWith(
                        color: DesignSystem.textPrimary,
                      ),
                    ),
                  ],
                ),
                content: Text(
                  recommendationDescription,
                  style: DesignSystem.bodyM.copyWith(
                    color: DesignSystem.textSecondary,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'Close',
                      style: DesignSystem.labelL.copyWith(
                        color: DesignSystem.accentBlue,
                      ),
                    ),
                  ),
                ],
              );
            },
          );
        },
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: DesignSystem.spacing24,
            vertical: DesignSystem.spacing16,
          ),
          decoration: BoxDecoration(
            // Solid color background instead of transparent
            color: DesignSystem.cardBackground,
            borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
            border: Border.all(
              color: recommendationColor,
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: recommendationColor.withAlpha(77), // 0.3 opacity
                blurRadius: 10,
                spreadRadius: 0,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                recommendationIcon,
                color: recommendationColor,
                size: 24,
              ),
              SizedBox(width: DesignSystem.spacing8),
              Text(
                recommendation,
                style: DesignSystem.headingM.copyWith(
                  color: recommendationColor,
                  letterSpacing: DesignSystem.letterSpacingTight,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }








  /// Build the predictions section with premium design
  Widget _buildPredictionsSection() {
    // Get tomorrow and day after tomorrow predictions
    final tomorrow = _predictions.isNotEmpty ? _predictions[0] : null;
    final dayAfterTomorrow = _predictions.length > 1 ? _predictions[1] : null;

    // Get additional predictions for enhanced version
    final threeDaysAhead = _predictions.length > 2 ? _predictions[2] : null;
    final fourDaysAhead = _predictions.length > 3 ? _predictions[3] : null;

    return Transform.translate(
      offset: Offset(0, 0), // Не смещаем
      child: Container(
        width: 250, // Уменьшаем ширину
        padding: EdgeInsets.all(DesignSystem.spacing16),
        decoration: BoxDecoration(
          color: Colors.black.withAlpha(51),
          borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
          border: Border.all(
            color: DesignSystem.accentBlue.withAlpha(51),
            width: 1.0,
          ),
          boxShadow: DesignSystem.subtleShadow,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Premium header with icon
            Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: DesignSystem.accentBlue.withAlpha(38), // withOpacity(0.15) -> withAlpha(38)
                    borderRadius: BorderRadius.circular(DesignSystem.borderRadiusM),
                    boxShadow: [
                      BoxShadow(
                        color: DesignSystem.accentBlue.withAlpha(26), // withOpacity(0.1) -> withAlpha(26)
                        blurRadius: 8,
                        spreadRadius: 0,
                      ),
                    ],
                  ),
                  child: Icon(
                    _isEnhancedVersion ? Icons.auto_graph : Icons.insights,
                    color: DesignSystem.accentBlue,
                    size: 18,
                  ),
                ),
                SizedBox(width: DesignSystem.spacing8), // Уменьшил отступ с 16 до 8
                Flexible(
                  child: Text(
                    _isEnhancedVersion ? 'Enhanced Prognosis' : 'Future Prognosis',
                    style: DesignSystem.headingM,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),

            SizedBox(height: DesignSystem.spacing16),

            // Premium divider
            Container(
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white.withAlpha(3), // withOpacity(0.01) -> withAlpha(3)
                    Colors.white.withAlpha(38), // withOpacity(0.15) -> withAlpha(38)
                    Colors.white.withAlpha(3), // withOpacity(0.01) -> withAlpha(3)
                  ],
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                ),
              ),
            ),

            SizedBox(height: DesignSystem.spacing24),

            // Tomorrow prediction with enhanced iOS styling
            if (tomorrow != null)
              _buildIOSPredictionItem(
                'Tomorrow',
                tomorrow.value,
                _yesterdayEntry != null ? tomorrow.value - _indicatorValue : 0,
                _getPredictionConfidence(tomorrow),
              ),

            const SizedBox(height: 16),

            // iOS-style divider
            Container(
              height: 0.5, // Thin iOS-style divider
              color: Colors.grey.withAlpha(77),
            ),

            const SizedBox(height: 16),

            // Day after tomorrow prediction with enhanced iOS styling
            if (dayAfterTomorrow != null)
              _buildIOSPredictionItem(
                'Day After Tomorrow',
                dayAfterTomorrow.value,
                tomorrow != null ? dayAfterTomorrow.value - tomorrow.value : 0,
                _getPredictionConfidence(dayAfterTomorrow) * 0.8, // Lower confidence for further predictions
              ),

            // Enhanced version shows more predictions
            if (_isEnhancedVersion) ...[
              if (threeDaysAhead != null) ...[
                const SizedBox(height: 16),
                Container(
                  height: 0.5,
                  color: Colors.grey.withAlpha(77),
                ),
                const SizedBox(height: 16),
                _buildIOSPredictionItem(
                  '3 Days Ahead',
                  threeDaysAhead.value,
                  dayAfterTomorrow != null ? threeDaysAhead.value - dayAfterTomorrow.value : 0,
                  _getPredictionConfidence(threeDaysAhead) * 0.7,
                ),
              ],

              if (fourDaysAhead != null) ...[
                const SizedBox(height: 16),
                Container(
                  height: 0.5,
                  color: Colors.grey.withAlpha(77),
                ),
                const SizedBox(height: 16),
                _buildIOSPredictionItem(
                  '4 Days Ahead',
                  fourDaysAhead.value,
                  threeDaysAhead != null ? fourDaysAhead.value - threeDaysAhead.value : 0,
                  _getPredictionConfidence(fourDaysAhead) * 0.6,
                ),
              ],
            ],

            const SizedBox(height: 16),

            // iOS-style divider
            Container(
              height: 0.5, // Thin iOS-style divider
              color: Colors.grey.withAlpha(77),
            ),

            const SizedBox(height: 16),

            // Market trend analysis with enhanced iOS style
            _buildIOSMarketTrendAnalysis(),

            // Enhanced version shows additional trend visualization
            if (_isEnhancedVersion && _predictions.length >= 2) ...[
              const SizedBox(height: 24),
              _buildTrendVisualization(),
            ],
          ],
        ),
      ),
    );
  }

  /// Build a simple trend visualization for enhanced version
  Widget _buildTrendVisualization() {
    final sentimentColor = DesignSystem.getSentimentColor(_indicatorValue);

    // Create data points for visualization
    List<double> dataPoints = [_indicatorValue];
    for (var prediction in _predictions.take(4)) {
      dataPoints.add(prediction.value);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Trend Visualization',
          style: DesignSystem.labelL.copyWith(
            color: DesignSystem.textPrimary,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          height: 100,
          width: double.infinity,
          padding: EdgeInsets.all(DesignSystem.spacing8),
          decoration: BoxDecoration(
            color: Colors.black.withAlpha(51), // withOpacity(0.2) -> withAlpha(51)
            borderRadius: BorderRadius.circular(DesignSystem.borderRadiusM),
          ),
          child: CustomPaint(
            painter: TrendLinePainter(
              dataPoints: dataPoints,
              color: sentimentColor,
            ),
            size: Size.infinite,
          ),
        ),
      ],
    );
  }

  /// Get prediction confidence from the prediction metrics or calculate it
  /// Uses a more sophisticated approach to calculate real mathematical probability
  double _getPredictionConfidence(SentimentHistoryEntry prediction) {
    // Check if the prediction has a confidence metric
    if (prediction.metrics.containsKey('confidence')) {
      // Use the confidence value from the prediction (0-100 scale)
      // This is now calculated using statistical methods in the prediction service
      return prediction.metrics['confidence']! / 100;
    }

    // Fallback to calculating confidence if not provided
    // This is a more sophisticated approach that considers multiple factors

    // Base confidence level - starts higher for more accurate predictions
    double confidence = 0.75;

    // Get volatility from metrics if available
    double volatility = 0;
    if (prediction.metrics.containsKey('volatility')) {
      volatility = prediction.metrics['volatility']!;
    }

    // Adjust based on volatility (higher volatility = lower confidence)
    confidence -= (volatility / 20.0).clamp(0.0, 0.3);

    // Adjust based on prediction distance from current date
    final today = DateTime.now();
    final daysInFuture = prediction.date.difference(today).inDays;

    // Confidence decreases non-linearly with prediction distance
    // Uses exponential decay function for more realistic drop-off
    final distanceFactor = daysInFuture / 7; // Normalize to a week
    final distanceDecay = math.pow(0.9, distanceFactor); // Exponential decay
    confidence *= distanceDecay;

    // Adjust based on historical data availability
    if (_yesterdayEntry != null) confidence += 0.05;
    if (_lastWeekEntry != null) confidence += 0.05;

    // Adjust based on metrics completeness
    if (prediction.metrics.isNotEmpty) {
      confidence += 0.05 * (prediction.metrics.length / 7); // Assuming 7 is the max number of metrics
    }

    // Adjust based on extreme values (extreme values are less predictable)
    final extremenessFactor = (prediction.value < 30 || prediction.value > 70) ? 0.1 : 0.0;
    confidence -= extremenessFactor;

    // Log the confidence calculation components for debugging
    debugPrint('Confidence calculation: base=0.75, volatility=${volatility/20}, distanceDecay=$distanceDecay, extremenessFactor=$extremenessFactor');
    debugPrint('Final confidence: ${confidence.clamp(0.3, 0.95)}');

    return confidence.clamp(0.3, 0.95); // Clamp to a reasonable range
  }

  /// Build premium Future Market Trend analysis section
  Widget _buildIOSMarketTrendAnalysis() {
    // Variables for trend analysis
    String trendDirection;
    String trendDescription;
    IconData trendIcon;
    Color trendColor;

    // Get tomorrow and day after tomorrow predictions
    final tomorrow = _predictions.isNotEmpty ? _predictions[0] : null;
    final dayAfterTomorrow = _predictions.length > 1 ? _predictions[1] : null;

    // Use cached volatility for trend descriptions
    double volatility = _cachedVolatility;

    if (tomorrow != null) {
      // Get trend strength from prediction metrics if available
      if (tomorrow.metrics.containsKey('trend_strength')) {
        final newTrendStrength = tomorrow.metrics['trend_strength']!;

        // Check if we should update the trend strength based on time and threshold
        final now = DateTime.now();
        final timeSinceLastUpdate = now.difference(_lastMetricsUpdateTime);
        final trendStrengthDiff = (newTrendStrength - _cachedTrendStrength).abs();

        // Only update if:
        // 1. Enough time has passed since last update OR
        // 2. The change is significant enough AND some minimum time has passed
        if (timeSinceLastUpdate >= _minUpdateInterval ||
            (trendStrengthDiff >= _metricChangeThreshold && timeSinceLastUpdate.inMinutes >= 5)) {

          // Apply smoothing to make changes more gradual
          final smoothedTrendStrength = _cachedTrendStrength * 0.7 + newTrendStrength * 0.3;

          // Update the cached value
          if (smoothedTrendStrength != _cachedTrendStrength || !_metricsLogged) {
            _cachedTrendStrength = smoothedTrendStrength;
            _lastMetricsUpdateTime = now;
            debugPrint('Updated trend strength: $smoothedTrendStrength (original: $newTrendStrength)');

            // Save to SharedPreferences
            _saveEnhancedMetricsToPrefs();
          }
        } else {
          // Проверяем, прошло ли достаточно времени с момента последнего лога
          final now = DateTime.now();
          final timeSinceLastLog = now.difference(_lastSkipLogTime);

          // Выводим сообщение только если прошло достаточно времени с момента последнего лога
          if (timeSinceLastLog >= _minLogInterval) {
            debugPrint('Skipping trend strength update (diff: $trendStrengthDiff, time since last: ${timeSinceLastUpdate.inMinutes}m)');
            _lastSkipLogTime = now; // Обновляем время последнего лога
          }
        }
      }

      // Get volatility from prediction metrics if available
      if (tomorrow.metrics.containsKey('volatility')) {
        final newVolatility = tomorrow.metrics['volatility']!;

        // Check if we should update the volatility based on time and threshold
        final now = DateTime.now();
        final timeSinceLastUpdate = now.difference(_lastMetricsUpdateTime);
        final volatilityDiff = (newVolatility - _cachedVolatility).abs();

        // Only update if:
        // 1. Enough time has passed since last update OR
        // 2. The change is significant enough AND some minimum time has passed
        if (timeSinceLastUpdate >= _minUpdateInterval ||
            (volatilityDiff >= _metricChangeThreshold && timeSinceLastUpdate.inMinutes >= 5)) {

          // Apply smoothing to make changes more gradual
          final smoothedVolatility = _cachedVolatility * 0.7 + newVolatility * 0.3;

          // Update the cached value
          if (smoothedVolatility != _cachedVolatility || !_metricsLogged) {
            _cachedVolatility = smoothedVolatility;
            _lastMetricsUpdateTime = now;
            debugPrint('Updated volatility: $smoothedVolatility (original: $newVolatility)');

            // Save to SharedPreferences
            _saveEnhancedMetricsToPrefs();
          }
        } else {
          // Проверяем, прошло ли достаточно времени с момента последнего лога
          final now = DateTime.now();
          final timeSinceLastLog = now.difference(_lastSkipLogTime);

          // Выводим сообщение только если прошло достаточно времени с момента последнего лога
          if (timeSinceLastLog >= _minLogInterval) {
            debugPrint('Skipping volatility update (diff: $volatilityDiff, time since last: ${timeSinceLastUpdate.inMinutes}m)');
            _lastSkipLogTime = now; // Обновляем время последнего лога
          }
        }
      }

      // Mark metrics as logged to avoid repeated logging
      if (!_metricsLogged) {
        _metricsLogged = true;
      }
    }

    // Calculate trend based on predictions
    double shortTermChange = 0;
    double longTermChange = 0;

    if (tomorrow != null) {
      // Short term change (current to tomorrow)
      shortTermChange = tomorrow.value - _indicatorValue;

      // Long term change (if we have day after tomorrow)
      if (dayAfterTomorrow != null) {
        longTermChange = dayAfterTomorrow.value - _indicatorValue;
      } else {
        // If no day after tomorrow, use tomorrow's change
        longTermChange = shortTermChange;
      }
    }

    // Determine trend direction and description based on changes and predictions
    if (longTermChange > 5) {
      // Strong upward trend
      trendDirection = "Strongly Bullish";
      trendDescription = volatility > 8
          ? "Strong positive momentum expected with high volatility."
          : "Strong positive momentum expected to continue.";
      trendIcon = Icons.trending_up;
      trendColor = const Color(0xFF34C759); // iOS green
    } else if (longTermChange > 2) {
      // Moderate upward trend
      trendDirection = "Bullish";
      trendDescription = volatility > 5
          ? "Positive trend expected with some volatility."
          : "Positive trend expected to develop.";
      trendIcon = Icons.trending_up;
      trendColor = const Color(0xFF30D158); // iOS light green
    } else if (longTermChange > 0 && shortTermChange > 0) {
      // Slight upward trend
      trendDirection = "Mildly Bullish";
      trendDescription = "Slight improvement in market sentiment expected.";
      trendIcon = Icons.trending_up;
      trendColor = const Color(0xFF30D158); // iOS light green
    } else if (longTermChange.abs() < 2 && shortTermChange.abs() < 2) {
      // Stable trend
      trendDirection = "Neutral";
      trendDescription = volatility > 5
          ? "Market sentiment expected to remain stable with some volatility."
          : "Market sentiment expected to remain balanced.";
      trendIcon = Icons.trending_flat;
      trendColor = const Color(0xFFFFCC00); // iOS yellow
    } else if (longTermChange < 0 && shortTermChange < 0 && longTermChange > -3) {
      // Slight downward trend
      trendDirection = "Mildly Bearish";
      trendDescription = "Slight decrease in market sentiment expected.";
      trendIcon = Icons.trending_down;
      trendColor = const Color(0xFFFF9500); // iOS orange
    } else if (longTermChange < -2 && longTermChange > -5) {
      // Moderate downward trend
      trendDirection = "Bearish";
      trendDescription = volatility > 5
          ? "Negative trend expected with some volatility."
          : "Negative trend expected to develop.";
      trendIcon = Icons.trending_down;
      trendColor = const Color(0xFFFF453A); // iOS light red
    } else if (longTermChange < -5) {
      // Strong downward trend
      trendDirection = "Strongly Bearish";
      trendDescription = volatility > 8
          ? "Strong negative momentum expected with high volatility."
          : "Strong negative momentum expected to continue.";
      trendIcon = Icons.trending_down;
      trendColor = const Color(0xFFFF3B30); // iOS red
    } else if (shortTermChange < 0 && longTermChange > 0) {
      // Short-term dip but long-term improvement
      trendDirection = "Recovery Expected";
      trendDescription = "Short-term dip followed by recovery.";
      trendIcon = Icons.trending_up;
      trendColor = const Color(0xFF30D158); // iOS light green
    } else if (shortTermChange > 0 && longTermChange < 0) {
      // Short-term rise but long-term decline
      trendDirection = "Correction Expected";
      trendDescription = "Short-term rise followed by correction.";
      trendIcon = Icons.trending_down;
      trendColor = const Color(0xFFFF9500); // iOS orange
    } else {
      // Default case - use the trend from _getFutureMarketTrend
      final trendInfo = _getFutureMarketTrend();
      trendDirection = trendInfo['direction'] as String;
      trendDescription = trendInfo['description'] as String;
      trendIcon = trendInfo['icon'] as IconData;
      trendColor = trendInfo['color'] as Color;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Premium header
        const Text(
          'Future Market Trend',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600, // SF Pro semibold weight
            color: Colors.white,
            letterSpacing: -0.5, // SF Pro style
          ),
        ),
        const SizedBox(height: 12),

        // iOS-style trend indicator
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: trendColor.withAlpha(40),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(trendIcon, color: trendColor, size: 18),
              const SizedBox(width: 8),
              Text(
                trendDirection,
                style: TextStyle(
                  fontSize: 15,
                  fontWeight: FontWeight.w500, // SF Pro medium weight
                  color: trendColor,
                  letterSpacing: -0.3, // SF Pro style
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 12),

        // Trend description
        Text(
          trendDescription,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.white70,
            fontWeight: FontWeight.w400, // SF Pro regular weight
            letterSpacing: -0.2, // SF Pro style
          ),
        ),
        const SizedBox(height: 16),


      ],
    );
  }

  // Removed unused method

  /// Build premium prediction item
  Widget _buildIOSPredictionItem(String label, double value, double change, double confidence) {
    final isPositive = change >= 0;
    final sentimentColor = DesignSystem.getSentimentColor(value);
    final changeColor = isPositive ? DesignSystem.accentGreen : DesignSystem.accentRed;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Premium label
        Text(
          label,
          style: DesignSystem.labelM.copyWith(
            color: DesignSystem.textSecondary,
          ),
        ),
        SizedBox(height: DesignSystem.spacing16),

        // Premium value and change
        Row(
          children: [
            // Value with premium typography
            Text(
              value.toStringAsFixed(1),
              style: DesignSystem.numberL.copyWith(
                color: sentimentColor,
              ),
            ),
            SizedBox(width: DesignSystem.spacing8), // Уменьшил отступ с 16 до 8

            // Change indicator with premium design
            if (change != 0)
              Flexible(
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 6, // Уменьшил отступ с 8 до 6
                    vertical: DesignSystem.spacing4
                  ),
                  decoration: BoxDecoration(
                    color: changeColor.withAlpha(26), // withOpacity(0.1) -> withAlpha(26)
                    borderRadius: BorderRadius.circular(DesignSystem.borderRadiusM),
                    border: Border.all(
                      color: changeColor.withAlpha(51), // withOpacity(0.2) -> withAlpha(51)
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                        color: changeColor,
                        size: 14,
                      ),
                      SizedBox(width: DesignSystem.spacing2), // Уменьшил отступ с 4 до 2
                      Flexible(
                        child: Text(
                          '${isPositive ? '+' : ''}${change.toStringAsFixed(1)}',
                          style: DesignSystem.labelS.copyWith(
                            color: changeColor,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
          ],
        ),
        SizedBox(height: DesignSystem.spacing16),


      ],
    );
  }

  /// Get future market trend information based on tomorrow's prediction
  Map<String, dynamic> _getFutureMarketTrend() {
    // Default values
    String direction = "Neutral";
    String description = "Market sentiment appears stable.";
    IconData icon = Icons.trending_flat;
    Color color = const Color(0xFFFFCC00); // Yellow for neutral

    // Calculate change between current value and tomorrow's prediction
    double change = 0;
    double volatility = 0;

    if (_predictions.isNotEmpty) {
      // Get tomorrow's prediction
      final tomorrowValue = _predictions[0].value;

      // Calculate change from current value
      change = tomorrowValue - _indicatorValue;

      // Get volatility from prediction metrics
      volatility = _predictions[0].metrics['volatility'] ?? 0;

      // Determine trend direction based on tomorrow's change
      if (change > 5) {
        direction = "Bullish";
        description = "Strong upward momentum expected.";
        icon = Icons.trending_up;
        color = const Color(0xFF34C759); // Green
      } else if (change > 2) {
        direction = "Mildly Bullish";
        description = "Slight upward momentum expected.";
        icon = Icons.trending_up;
        color = const Color(0xFF30D158); // Light green
      } else if (change < -5) {
        direction = "Bearish";
        description = "Strong downward momentum expected.";
        icon = Icons.trending_down;
        color = const Color(0xFFFF3B30); // Red
      } else if (change < -2) {
        direction = "Mildly Bearish";
        description = "Slight downward momentum expected.";
        icon = Icons.trending_down;
        color = const Color(0xFFFF453A); // Light red
      } else {
        // Neutral trend (change between -2 and +2)
        if (volatility > 8) {
          direction = "Volatile";
          description = "High market uncertainty expected.";
          icon = Icons.shuffle;
          color = const Color(0xFFFF9500); // Orange
        } else {
          direction = "Neutral";
          description = "Market sentiment expected to remain stable.";
          icon = Icons.trending_flat;
          color = const Color(0xFFFFCC00); // Yellow
        }
      }
    }

    return {
      'direction': direction,
      'description': description,
      'icon': icon,
      'color': color,
    };
  }

  // Removed unused methods
}

/// Simple painter for trend visualization
class TrendLinePainter extends CustomPainter {
  final List<double> dataPoints;
  final Color color;

  TrendLinePainter({
    required this.dataPoints,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (dataPoints.isEmpty) return;

    final paint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..strokeCap = StrokeCap.round;

    final dotPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    final path = Path();

    // Find min and max values for scaling
    final double minValue = dataPoints.reduce((a, b) => a < b ? a : b);
    final double maxValue = dataPoints.reduce((a, b) => a > b ? a : b);

    // Calculate scaling factors
    final double xStep = size.width / (dataPoints.length - 1);
    final double yScale = size.height / (maxValue - minValue + 10); // Add padding

    // Start path at first point
    final double startX = 0;
    final double startY = size.height - ((dataPoints[0] - minValue) * yScale);
    path.moveTo(startX, startY);

    // Draw dots and connect points
    for (int i = 0; i < dataPoints.length; i++) {
      final double x = i * xStep;
      final double y = size.height - ((dataPoints[i] - minValue) * yScale);

      if (i > 0) {
        path.lineTo(x, y);
      }

      // Draw dot at each point
      canvas.drawCircle(Offset(x, y), 4, dotPaint);
    }

    // Draw the path
    canvas.drawPath(path, paint);

    // Draw horizontal grid lines
    final gridPaint = Paint()
      ..color = Colors.white.withAlpha(26) // withOpacity(0.1) -> withAlpha(26)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 0.5;

    for (int i = 0; i <= 4; i++) {
      final y = size.height * i / 4;
      canvas.drawLine(Offset(0, y), Offset(size.width, y), gridPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}



/// Custom painter для продвинутой анимации реактора в стиле iOS
class ReactorAnimationPainter extends CustomPainter {
  final List<Particle> particles;
  final double animationValue;
  final double indicatorValue;
  final Color color;

  ReactorAnimationPainter({
    required this.particles,
    required this.animationValue,
    required this.indicatorValue,
    required this.color,
  });

  // Вспомогательный метод для генерации равномерных остановок градиента
  List<double> generateGradientStops(int count) {
    final stops = <double>[];
    for (int i = 0; i < count; i++) {
      stops.add(i / (count - 1));
    }
    return stops;
  }

  // Вспомогательный метод для отрисовки различных форм частиц
  void drawParticleShape(Canvas canvas, Offset center, double size, ParticleShape shape, Paint paint) {
    switch (shape) {
      case ParticleShape.circle:
        canvas.drawCircle(center, size, paint);
        break;

      case ParticleShape.square:
        canvas.drawRect(
          Rect.fromCenter(center: center, width: size * 2, height: size * 2),
          paint
        );
        break;

      case ParticleShape.triangle:
        final path = Path();
        path.moveTo(center.dx, center.dy - size);
        path.lineTo(center.dx - size, center.dy + size);
        path.lineTo(center.dx + size, center.dy + size);
        path.close();
        canvas.drawPath(path, paint);
        break;

      case ParticleShape.diamond:
        final path = Path();
        path.moveTo(center.dx, center.dy - size);
        path.lineTo(center.dx + size, center.dy);
        path.lineTo(center.dx, center.dy + size);
        path.lineTo(center.dx - size, center.dy);
        path.close();
        canvas.drawPath(path, paint);
        break;

      case ParticleShape.star:
        final path = Path();
        final outerRadius = size;
        final innerRadius = size * 0.4;
        final double step = math.pi / 5;

        for (int i = 0; i < 10; i++) {
          final radius = i % 2 == 0 ? outerRadius : innerRadius;
          final angle = i * step - math.pi / 2;
          final point = center + Offset(math.cos(angle) * radius, math.sin(angle) * radius);

          if (i == 0) {
            path.moveTo(point.dx, point.dy);
          } else {
            path.lineTo(point.dx, point.dy);
          }
        }

        path.close();
        canvas.drawPath(path, paint);
        break;

      case ParticleShape.line:
        final linePaint = Paint()
          ..color = paint.color
          ..style = PaintingStyle.stroke
          ..strokeWidth = size / 2
          ..strokeCap = StrokeCap.round;

        canvas.drawLine(
          center - Offset(size, 0),
          center + Offset(size, 0),
          linePaint
        );
        break;

      case ParticleShape.custom:
        // Можно добавить кастомную форму при необходимости
        canvas.drawCircle(center, size, paint);
        break;
    }
  }

  @override
  void paint(Canvas canvas, Size size) {
    // Отрисовка частиц с продвинутыми эффектами
    for (final particle in particles) {
      // Применяем вращение и масштабирование
      canvas.save();
      canvas.translate(particle.position.dx, particle.position.dy);
      canvas.rotate(particle.rotation);
      canvas.scale(particle.scale, particle.scale);

      // Создаем базовый Paint для частицы
      final paint = Paint()
        ..color = particle.color.withAlpha((particle.opacity * 255).toInt())
        ..style = PaintingStyle.fill;

      // Если используется градиент, создаем его
      if (particle.useGradient && particle.gradientColors.isNotEmpty) {
        paint.shader = RadialGradient(
          colors: particle.gradientColors,
          stops: generateGradientStops(particle.gradientColors.length),
        ).createShader(Rect.fromCircle(
          center: Offset.zero,
          radius: particle.size * 1.2,
        ));
      }

      // Если используется свечение, рисуем его сначала
      if (particle.useGlow) {
        final glowPaint = Paint()
          ..color = particle.color.withAlpha((particle.opacity * 0.5 * 255).toInt())
          ..style = PaintingStyle.fill
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, particle.size * particle.glowIntensity * 0.5);

        drawParticleShape(canvas, Offset.zero, particle.size * 1.3, particle.shape, glowPaint);
      }

      // Рисуем основную форму частицы
      drawParticleShape(canvas, Offset.zero, particle.size, particle.shape, paint);

      canvas.restore();
    }

    // Точный центр перекрестия, смещен на 5px вверх
    final exactCenter = Offset(size.width / 2, size.height / 2 - 5); // Смещен на 5px вверх

    // Максимальный радиус для анимаций, увеличен в 2 раза
    final maxRadius = 70.0; // Увеличен в 2 раза (с 35 до 70)

    // Дополнительные эффекты в зависимости от уровня индикатора с улучшенной iOS-стилизацией

    if (indicatorValue < 20) {
      // Crash (0-20): Интенсивные красные вспышки с эффектом пульсации
      // Создаем базовый слой свечения
      final glowPaint = Paint()
        ..color = Color.lerp(Colors.red, Colors.white, 0.2)!.withAlpha(40)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 20);

      canvas.drawCircle(exactCenter, maxRadius * 0.6, glowPaint);

      // Пульсирующий круг
      final pulseSize = 0.5 + math.sin(animationValue * 3) * 0.2;
      final pulsePaint = Paint()
        ..color = Colors.red.withAlpha(100)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 10);

      canvas.drawCircle(exactCenter, maxRadius * pulseSize, pulsePaint);

      // Мигающие линии с градиентом
      for (int i = 0; i < 8; i++) {
        final angle = i * math.pi / 4 + animationValue;
        final opacity = 0.6 + math.sin(animationValue * 5 + i) * 0.4;

        final linePaint = Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.5 + math.sin(animationValue * 3 + i) * 1.5
          ..strokeCap = StrokeCap.round;

        // Создаем градиент для линии
        linePaint.shader = LinearGradient(
          colors: [
            Colors.red.withAlpha((opacity * 255).toInt()),
            Colors.white.withAlpha((opacity * 180).toInt()),
            Colors.red.withAlpha((opacity * 255).toInt()),
          ],
          stops: [0.0, 0.5, 1.0],
        ).createShader(Rect.fromPoints(
          exactCenter,
          exactCenter + Offset(math.cos(angle) * maxRadius, math.sin(angle) * maxRadius),
        ));

        final start = exactCenter + Offset(math.cos(angle) * 5, math.sin(angle) * 5);
        final end = exactCenter + Offset(
          math.cos(angle) * (maxRadius * (0.7 + math.sin(animationValue * 2 + i) * 0.3)),
          math.sin(angle) * (maxRadius * (0.7 + math.sin(animationValue * 2 + i) * 0.3)),
        );

        canvas.drawLine(start, end, linePaint);
      }

      // Добавляем искры в центре
      final sparkPaint = Paint()
        ..color = Colors.white.withAlpha(200)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 2);

      for (int i = 0; i < 3; i++) {
        final sparkAngle = animationValue * 10 + i * math.pi * 2 / 3;
        final sparkDistance = 5 + math.sin(animationValue * 5 + i) * 3;
        final sparkPosition = exactCenter + Offset(
          math.cos(sparkAngle) * sparkDistance,
          math.sin(sparkAngle) * sparkDistance,
        );

        canvas.drawCircle(sparkPosition, 1.5, sparkPaint);
      }
    } else if (indicatorValue < 40) {
      // Anxiety (21-40): Оранжевые вихри и вращающиеся шестеренки
      // Создаем базовый слой свечения
      final glowPaint = Paint()
        ..color = Color.lerp(Colors.orange, Colors.amber, 0.3)!.withAlpha(50)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 15);

      canvas.drawCircle(exactCenter, maxRadius * 0.7, glowPaint);

      // Рисуем вихревой фон
      final vortexPaint = Paint()
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.5;

      for (int i = 0; i < 12; i++) {
        final angle = i * math.pi / 6;
        final spiralPath = Path();

        vortexPaint.shader = RadialGradient(
          colors: [
            Colors.orange.withAlpha(20),
            Colors.amber.withAlpha(120),
            Colors.orange.withAlpha(20),
          ],
          stops: [0.0, 0.5, 1.0],
        ).createShader(Rect.fromCircle(
          center: exactCenter,
          radius: maxRadius,
        ));

        spiralPath.moveTo(exactCenter.dx, exactCenter.dy);

        for (double r = 5; r < maxRadius * 0.8; r += 3) {
          final spiralAngle = angle + animationValue * (1.0 - r / maxRadius) * 3;
          final x = exactCenter.dx + math.cos(spiralAngle) * r;
          final y = exactCenter.dy + math.sin(spiralAngle) * r;
          spiralPath.lineTo(x, y);
        }

        canvas.drawPath(spiralPath, vortexPaint);
      }

      // Рисуем шестеренки с улучшенным стилем
      final gearPaint1 = Paint()
        ..color = Colors.amber.withAlpha(180)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.5
        ..strokeCap = StrokeCap.round;

      final gearPaint2 = Paint()
        ..color = Colors.orange.withAlpha(200)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0
        ..strokeCap = StrokeCap.round;

      _drawGear(canvas, exactCenter, maxRadius * 0.5, 10, animationValue * 0.7, gearPaint1);
      _drawGear(canvas, exactCenter + Offset(maxRadius * 0.35, -maxRadius * 0.25), maxRadius * 0.3, 8, -animationValue * 1.2, gearPaint2);
      _drawGear(canvas, exactCenter + Offset(-maxRadius * 0.3, maxRadius * 0.3), maxRadius * 0.25, 6, animationValue * 1.5, gearPaint2);
    } else if (indicatorValue < 60) {
      // Stasis (41-60): Желтые горизонтальные линии с пульсацией и эффектами
      // Создаем базовый слой свечения
      final glowPaint = Paint()
        ..color = Color.lerp(Colors.yellow, Colors.white, 0.3)!.withAlpha(40)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 15);

      canvas.drawCircle(exactCenter, maxRadius * 0.7, glowPaint);

      // Рисуем горизонтальные линии с эффектами
      for (int i = -5; i <= 5; i++) {
        final y = i * 5.0;
        final offset = math.sin(animationValue * 2 + i * 0.5) * 4;
        final lineOpacity = 0.4 + math.sin(animationValue + i * 0.7) * 0.2;

        final linePaint = Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.5 + (i < 0 ? -i : i) * 0.2
          ..strokeCap = StrokeCap.round;

        // Создаем градиент для линии
        linePaint.shader = LinearGradient(
          colors: [
            Colors.transparent,
            Colors.yellow.withAlpha((lineOpacity * 255).toInt()),
            Colors.white.withAlpha((lineOpacity * 200).toInt()),
            Colors.yellow.withAlpha((lineOpacity * 255).toInt()),
            Colors.transparent,
          ],
          stops: [0.0, 0.2, 0.5, 0.8, 1.0],
        ).createShader(Rect.fromPoints(
          exactCenter + Offset(-maxRadius, y),
          exactCenter + Offset(maxRadius, y),
        ));

        canvas.drawLine(
          exactCenter + Offset(-maxRadius * 0.9 + offset, y),
          exactCenter + Offset(maxRadius * 0.9 + offset, y),
          linePaint,
        );
      }

      // Добавляем пульсирующие точки
      for (int i = 0; i < 5; i++) {
        final angle = i * math.pi * 2 / 5 + animationValue * 0.5;
        final distance = maxRadius * 0.4 + math.sin(animationValue * 2 + i) * 10;
        final dotSize = 2.0 + math.sin(animationValue * 3 + i * 0.7) * 1.0;

        final dotPaint = Paint()
          ..color = Colors.yellow.withAlpha(180)
          ..style = PaintingStyle.fill
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, dotSize * 0.5);

        final dotPosition = exactCenter + Offset(
          math.cos(angle) * distance,
          math.sin(angle) * distance,
        );

        canvas.drawCircle(dotPosition, dotSize, dotPaint);
      }
    } else if (indicatorValue < 80) {
      // Lift (61-80): Зеленые вращающиеся шестеренки с эффектом ускорения и свечением
      // Создаем базовый слой свечения
      final glowPaint = Paint()
        ..color = Color.lerp(Colors.green, Colors.lightGreen, 0.3)!.withAlpha(50)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 20);

      canvas.drawCircle(exactCenter, maxRadius * 0.8, glowPaint);

      // Рисуем вращающиеся кольца
      for (int i = 0; i < 3; i++) {
        final ringRadius = maxRadius * (0.4 + i * 0.15);
        final ringPaint = Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0 + i * 0.5
          ..color = Colors.lightGreen.withAlpha(100 - i * 20);

        canvas.drawCircle(exactCenter, ringRadius, ringPaint);
      }

      // Рисуем шестеренки с улучшенным стилем
      final gearPaint1 = Paint()
        ..color = Colors.lightGreen.withAlpha(200)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.5
        ..strokeCap = StrokeCap.round;

      final gearPaint2 = Paint()
        ..color = Colors.green.withAlpha(220)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2.0
        ..strokeCap = StrokeCap.round;

      // Основная шестеренка
      _drawGear(canvas, exactCenter, maxRadius * 0.6, 12, animationValue * 1.0, gearPaint1);

      // Вспомогательные шестеренки
      _drawGear(canvas, exactCenter + Offset(-maxRadius * 0.4, maxRadius * 0.3), maxRadius * 0.25, 8, -animationValue * 1.5, gearPaint2);
      _drawGear(canvas, exactCenter + Offset(maxRadius * 0.35, -maxRadius * 0.35), maxRadius * 0.2, 6, animationValue * 2.0, gearPaint2);

      // Добавляем следы ускорения
      final trailPaint = Paint()
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1.5
        ..strokeCap = StrokeCap.round;

      for (int i = 0; i < 12; i++) {
        final angle = i * math.pi / 6 + animationValue;
        final opacity = 0.3 + math.sin(animationValue * 2 + i) * 0.2;

        trailPaint.shader = RadialGradient(
          colors: [
            Colors.white.withAlpha((opacity * 255).toInt()),
            Colors.lightGreen.withAlpha((opacity * 180).toInt()),
            Colors.transparent,
          ],
          stops: [0.0, 0.3, 1.0],
        ).createShader(Rect.fromCircle(
          center: exactCenter,
          radius: maxRadius,
        ));

        final start = exactCenter + Offset(
          math.cos(angle) * maxRadius * 0.3,
          math.sin(angle) * maxRadius * 0.3,
        );

        final end = exactCenter + Offset(
          math.cos(angle) * maxRadius * 0.7,
          math.sin(angle) * maxRadius * 0.7,
        );

        canvas.drawLine(start, end, trailPaint);
      }
    } else {
      // Surge (81-100): Яркие зеленые искры с эффектом взрыва и энергетическими волнами
      // Создаем базовый слой свечения
      final glowPaint = Paint()
        ..color = Colors.green.withAlpha(70)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 25);

      canvas.drawCircle(exactCenter, maxRadius * 0.9, glowPaint);

      // Рисуем энергетические волны
      for (int i = 0; i < 4; i++) {
        final waveRadius = maxRadius * (0.3 + 0.15 * i + math.sin(animationValue * 2) * 0.1);
        final wavePaint = Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0 - i * 0.3
          ..color = Color.lerp(Colors.green, Colors.white, i * 0.2)!.withAlpha(150 - i * 20);

        canvas.drawCircle(exactCenter, waveRadius, wavePaint);
      }

      // Рисуем вращающуюся шестеренку с эффектом свечения
      final gearPaint = Paint()
        ..color = Colors.green.withAlpha(220)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 3.0
        ..strokeCap = StrokeCap.round;

      _drawGear(canvas, exactCenter, maxRadius * 0.65, 16, animationValue * 1.5, gearPaint);

      // Добавляем искры
      for (int i = 0; i < 16; i++) {
        final angle = i * math.pi / 8 + animationValue * 2;
        final distance = maxRadius * (0.4 + math.sin(animationValue * 3 + i) * 0.2);

        // Создаем градиент для искры
        final sparkPaint = Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 2.0 + math.sin(animationValue + i) * 1.0
          ..strokeCap = StrokeCap.round;

        sparkPaint.shader = LinearGradient(
          colors: [
            Colors.white.withAlpha(220),
            Colors.green.withAlpha(180),
            Colors.lightGreen.withAlpha(100),
          ],
          stops: [0.0, 0.5, 1.0],
        ).createShader(Rect.fromPoints(
          exactCenter,
          exactCenter + Offset(math.cos(angle) * distance, math.sin(angle) * distance),
        ));

        final start = exactCenter + Offset(
          math.cos(angle) * distance * 0.3,
          math.sin(angle) * distance * 0.3,
        );

        final end = exactCenter + Offset(
          math.cos(angle) * distance,
          math.sin(angle) * distance,
        );

        canvas.drawLine(start, end, sparkPaint);

        // Добавляем яркую точку на конце искры
        final dotPaint = Paint()
          ..color = Colors.white.withAlpha(200)
          ..style = PaintingStyle.fill
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, 2);

        canvas.drawCircle(end, 1.5 + math.sin(animationValue * 5 + i) * 0.5, dotPaint);
      }

      // Добавляем эффект взрыва в центре
      final burstPaint = Paint()
        ..color = Colors.white.withAlpha(100 + (math.sin(animationValue * 5) * 50).toInt())
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 5);

      canvas.drawCircle(exactCenter, 10 + math.sin(animationValue * 8) * 5, burstPaint);
    }
  }

  // Вспомогательный метод для отрисовки шестеренки
  void _drawGear(Canvas canvas, Offset center, double radius, int teeth, double rotation, Paint paint) {
    final path = Path();
    final angleStep = 2 * math.pi / teeth;

    for (int i = 0; i < teeth; i++) {
      final angle1 = i * angleStep + rotation;
      final angle2 = angle1 + angleStep * 0.4;
      final angle3 = angle1 + angleStep * 0.5;
      final angle4 = angle1 + angleStep * 0.9;

      final outer1 = center + Offset(math.cos(angle1) * (radius * 1.2), math.sin(angle1) * (radius * 1.2));
      final outer2 = center + Offset(math.cos(angle2) * (radius * 1.2), math.sin(angle2) * (radius * 1.2));
      final inner1 = center + Offset(math.cos(angle3) * radius, math.sin(angle3) * radius);
      final inner2 = center + Offset(math.cos(angle4) * radius, math.sin(angle4) * radius);

      if (i == 0) {
        path.moveTo(outer1.dx, outer1.dy);
      } else {
        path.lineTo(outer1.dx, outer1.dy);
      }

      path.lineTo(outer2.dx, outer2.dy);
      path.lineTo(inner1.dx, inner1.dy);
      path.lineTo(inner2.dx, inner2.dy);
    }

    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(ReactorAnimationPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue ||
           oldDelegate.indicatorValue != indicatorValue ||
           oldDelegate.particles != particles ||
           oldDelegate.color != color;
  }
}

/// Custom painter for the iOS-style minimalist gauge speedometer
class IOSGaugeSpeedometerPainter extends CustomPainter {
  final double value;
  final Color color;

  IOSGaugeSpeedometerPainter({
    required this.value,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 3 + 30); // Опущен на 30px вниз
    final radius = size.width * 0.4; // Smaller radius for semi-circle

    // Draw the arc background - thinner for iOS style
    final bgPaint = Paint()
      ..color = Colors.grey.shade800.withAlpha(77) // 0.3 opacity
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4.0 // Thinner
      ..strokeCap = StrokeCap.round;

    // Draw background arc (180 degrees)
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi, // Start from the left (180 degrees)
      math.pi, // End at the right (0 degrees)
      false,
      bgPaint,
    );

    // Create gradient for the colored arc - iOS style with softer colors
    final gradientPaint = Paint()
      ..shader = SweepGradient(
        center: Alignment.center,
        startAngle: math.pi,
        endAngle: 2 * math.pi,
        colors: const [
          Color(0xFFFF3B30), // Red - Crash
          Color(0xFFFF9500), // Orange - Anxiety
          Color(0xFFFFCC00), // Yellow - Stasis
          Color(0xFFAED581), // Lime - Lift
          Color(0xFF34C759), // Green - Surge
        ],
        stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4.0 // Thinner for iOS style
      ..strokeCap = StrokeCap.round;

    // Calculate the angle based on the value (0-100)
    final valueAngle = (value / 100) * math.pi;

    // Draw the colored arc up to the current value
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi, // Start from the left (180 degrees)
      valueAngle, // End at the position corresponding to the value
      false,
      gradientPaint,
    );

    // Draw subtle tick marks - iOS style
    _drawIOSTickMarks(canvas, center, radius);

    // Draw the moving ball instead of a needle
    _drawMovingBall(canvas, center, radius, value);
  }

  // Draw more visible tick marks for the speedometer
  void _drawIOSTickMarks(Canvas canvas, Offset center, double radius) {
    final tickPaint = Paint()
      ..color = Colors.grey.shade400.withAlpha(180) // Increased opacity and brightness
      ..strokeWidth = 2.0 // Thicker
      ..style = PaintingStyle.stroke;

    // Draw 5 major ticks with labels for different phases
    for (int i = 0; i <= 4; i++) {
      final angle = math.pi + (i / 4) * math.pi;

      // Make ticks longer and more visible
      final outerPoint = Offset(
        center.dx + (radius + 2) * math.cos(angle),
        center.dy + (radius + 2) * math.sin(angle),
      );
      final innerPoint = Offset(
        center.dx + (radius - 8) * math.cos(angle),
        center.dy + (radius - 8) * math.sin(angle),
      );

      // Draw the tick mark
      canvas.drawLine(innerPoint, outerPoint, tickPaint);

      // Add small label indicators for each section
      final labelPoint = Offset(
        center.dx + (radius + 15) * math.cos(angle),
        center.dy + (radius + 15) * math.sin(angle),
      );

      // Draw small dots at each major tick
      final dotPaint = Paint()
        ..color = _getTickColor(i)
        ..style = PaintingStyle.fill;
      canvas.drawCircle(labelPoint, 3.0, dotPaint);
    }

    // Draw minor ticks between major ones
    final minorTickPaint = Paint()
      ..color = Colors.grey.shade600.withAlpha(100)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    for (int i = 0; i < 4; i++) {
      final angle = math.pi + (i / 4) * math.pi + math.pi / 8;
      final outerPoint = Offset(
        center.dx + radius * math.cos(angle),
        center.dy + radius * math.sin(angle),
      );
      final innerPoint = Offset(
        center.dx + (radius - 4) * math.cos(angle),
        center.dy + (radius - 4) * math.sin(angle),
      );

      canvas.drawLine(innerPoint, outerPoint, minorTickPaint);
    }
  }

  // Get color for each tick mark
  Color _getTickColor(int index) {
    switch (index) {
      case 0: return Colors.red; // Crash
      case 1: return Colors.orange; // Anxiety
      case 2: return Colors.yellow; // Stasis
      case 3: return const Color(0xFFAED581); // Lift (Lime)
      case 4: return Colors.green; // Surge
      default: return Colors.grey;
    }
  }

  // Draw a moving ball along the arc instead of a needle
  void _drawMovingBall(Canvas canvas, Offset center, double radius, double value) {
    final ballAngle = math.pi + (value / 100) * math.pi;

    // Calculate ball position along the arc
    final ballPosition = Offset(
      center.dx + radius * math.cos(ballAngle),
      center.dy + radius * math.sin(ballAngle),
    );

    // Draw ball shadow
    final shadowPaint = Paint()
      ..color = Colors.black.withAlpha(51) // 0.2 opacity
      ..style = PaintingStyle.fill;
    canvas.drawCircle(
      Offset(ballPosition.dx, ballPosition.dy + 1), // Slight offset for shadow
      6.0,
      shadowPaint,
    );

    // Draw the ball
    final ballPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;
    canvas.drawCircle(ballPosition, 5.0, ballPaint);

    // Draw highlight on the ball for 3D effect
    final highlightPaint = Paint()
      ..color = Colors.white.withAlpha(179) // 0.7 opacity
      ..style = PaintingStyle.fill;
    canvas.drawCircle(
      Offset(ballPosition.dx - 1.5, ballPosition.dy - 1.5), // Offset for highlight
      2.0,
      highlightPaint,
    );
  }

  @override
  bool shouldRepaint(IOSGaugeSpeedometerPainter oldDelegate) {
    return oldDelegate.value != value || oldDelegate.color != color;
  }
}
