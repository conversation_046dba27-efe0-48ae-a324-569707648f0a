import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // For SystemUiOverlayStyle
import '../services/enhanced_sentiment_service.dart';
import '../models/sentiment_history_model.dart';
import '../widgets/app_bottom_navigation.dart';
import '../config/design_system.dart';
import '../services/stable_forecast_service.dart';
import 'dart:math' as math;
import 'dart:async'; // For Timer

/// Enhanced version of the Reactor Sinusoid screen with improved prediction stability
/// and more accurate market sentiment calculation
class EnhancedReactorScreen extends StatefulWidget {
  const EnhancedReactorScreen({Key? key}) : super(key: key);

  @override
  State<EnhancedReactorScreen> createState() => _EnhancedReactorScreenState();
}

class _EnhancedReactorScreenState extends State<EnhancedReactorScreen> {
  final EnhancedSentimentService _sentimentService = EnhancedSentimentService();
  final StableForecastService _forecastService = StableForecastService(windowSize: 14);
  final ScrollController _scrollController = ScrollController();

  double _indicatorValue = 50.0;
  Map<String, double> _metricValues = {};
  bool _isLoading = true;
  bool _isLoadingHistory = true;
  bool _showDownArrow = false;

  // Timer for periodic data refresh (every 10 minutes)
  Timer? _dataRefreshTimer;
  DateTime _lastRefreshTime = DateTime.now();

  // Variables for reactor animation
  Timer? _animationTimer;
  double _animationValue = 0.0;
  List<Particle> _particles = [];
  SentimentHistoryEntry? _yesterdayEntry;
  SentimentHistoryEntry? _lastWeekEntry;
  List<SentimentHistoryEntry> _predictions = [];

  // Cache for prediction metrics
  double _cachedTrendStrength = 0;
  double _cachedVolatility = 0;
  bool _metricsLogged = false;

  @override
  void initState() {
    super.initState();
    _loadAllData();

    // Add scroll listener
    _scrollController.addListener(() {
      if (_scrollController.offset <= 50 && !_showDownArrow) {
        setState(() {
          _showDownArrow = true;
        });
      }
    });

    // Start animation timer
    _startAnimation();

    // Enable auto-refresh timer
    _startDataRefreshTimer();
  }

  /// Start timer for periodic data refresh (every 10 minutes)
  void _startDataRefreshTimer() {
    _dataRefreshTimer?.cancel();

    _dataRefreshTimer = Timer.periodic(const Duration(minutes: 10), (timer) {
      debugPrint('Auto-refresh timer triggered, refreshing data...');

      final now = DateTime.now();
      final timeSinceLastRefresh = now.difference(_lastRefreshTime);

      if (timeSinceLastRefresh.inMinutes >= 1) {
        debugPrint('Refreshing data (last refresh was ${timeSinceLastRefresh.inMinutes} minutes ago)');

        _sentimentService.clearCache().then((_) {
          _loadAllData();
        });
      } else {
        debugPrint('Skipping auto-refresh as manual refresh was done ${timeSinceLastRefresh.inSeconds} seconds ago');
      }
    });

    debugPrint('Started data refresh timer (every 10 minutes)');
  }

  /// Start reactor animation
  void _startAnimation() {
    _animationTimer?.cancel();

    _animationTimer = Timer.periodic(const Duration(milliseconds: 50), (timer) {
      if (mounted) {
        setState(() {
          _animationValue += 0.1;
          if (_animationValue > 2 * math.pi) {
            _animationValue = 0;
          }

          // Update existing particles
          for (int i = _particles.length - 1; i >= 0; i--) {
            _particles[i].update();
            if (_particles[i].isDead()) {
              _particles.removeAt(i);
            }
          }

          // Limit number of particles for performance
          if (_particles.length > 200) {
            _particles.removeRange(0, _particles.length - 200);
          }

          // Add new particles based on indicator level
          _addParticlesBasedOnLevel();
        });
      }
    });
  }

  /// Add particles based on indicator level - deterministic version
  void _addParticlesBasedOnLevel() {
    // Center of the reactor
    final center = Offset(247, 242);

    // Maximum radius for particles
    final maxRadius = 70.0;

    // Use animation value and indicator value for deterministic behavior
    final animationFactor = math.sin(_animationValue) * 0.5 + 0.5; // 0.0 to 1.0
    final timeFactor = DateTime.now().millisecondsSinceEpoch % 1000 / 1000; // 0.0 to 1.0

    // Different animations for different levels
    if (_indicatorValue < 20) {
      // Crash (0-20): Red flashes with pulsation effect
      if (_particles.length < 50) { // Limit particles for performance
        // Create main particles
        final angle = _animationValue * 0.5;

        _particles.add(Particle(
          position: center,
          velocity: Offset(
            math.cos(angle) * 4.0,
            math.sin(angle) * 4.0,
          ),
          size: 7.0,
          opacity: 0.95,
          color: Color.lerp(Colors.red, Colors.white, 0.3)!,
          shape: timeFactor < 0.7 ? ParticleShape.circle : ParticleShape.line,
          useGlow: true,
          glowIntensity: 0.9,
          lifespan: 50.0,
          decay: 0.02,
          rotationSpeed: 0.2,
          useGradient: true,
          gradientColors: [
            Colors.red.withAlpha(204),
            Colors.orange.withAlpha(153),
            Colors.red.withAlpha(204),
          ],
        ));

        // Add pulsation effect (additional particles)
        if (timeFactor < 0.2) {
          for (int i = 0; i < 3; i++) {
            final pulseAngle = angle + (i * math.pi * 2 / 3);
            _particles.add(Particle(
              position: center,
              velocity: Offset(
                math.cos(pulseAngle) * 5.0,
                math.sin(pulseAngle) * 5.0,
              ),
              size: 3.0,
              opacity: 0.7,
              color: Colors.red.withAlpha(204),
              shape: ParticleShape.circle,
              useGlow: true,
              glowIntensity: 0.6,
              lifespan: 25.0,
              decay: 0.04,
            ));
          }
        }
      }
    } else if (_indicatorValue < 40) {
      // Anxiety (21-40): Orange vortices and rotating gears
      if (_particles.length < 60) {
        final angle = _animationValue;
        final distance = maxRadius * 0.6 * animationFactor;

        // Main particles - gears
        _particles.add(Particle(
          position: center + Offset(
            math.cos(angle) * distance,
            math.sin(angle) * distance,
          ),
          velocity: Offset(
            math.cos(angle + math.pi/2) * 0.8,
            math.sin(angle + math.pi/2) * 0.8,
          ),
          size: 8.0,
          opacity: 0.85,
          color: Color.lerp(Colors.orange, Colors.amber, animationFactor)!,
          shape: timeFactor < 0.6 ? ParticleShape.circle : ParticleShape.diamond,
          rotation: angle * 0.5,
          rotationSpeed: 0.05 * (timeFactor < 0.5 ? 1 : -1),
          useGlow: true,
          glowIntensity: 0.5,
          lifespan: 80.0,
          decay: 0.01,
          useGradient: true,
          gradientColors: [
            Colors.orange.withAlpha(178),
            Colors.amber.withAlpha(127),
          ],
        ));

        // Add vortex trails
        if (timeFactor < 0.15) {
          for (int i = 0; i < 2; i++) {
            final trailAngle = angle + (i * 0.2 - 0.1);
            final trailDistance = distance * 0.9;
            _particles.add(Particle(
              position: center + Offset(
                math.cos(trailAngle) * trailDistance,
                math.sin(trailAngle) * trailDistance,
              ),
              velocity: Offset(
                math.cos(trailAngle + math.pi/2) * 0.4,
                math.sin(trailAngle + math.pi/2) * 0.4,
              ),
              size: 2.0,
              opacity: 0.6,
              color: Colors.amber.withAlpha(178),
              shape: ParticleShape.circle,
              lifespan: 50.0,
              decay: 0.02,
            ));
          }
        }
      }
    } else if (_indicatorValue < 60) {
      // Stasis (41-60): Yellow horizontal lines with pulsation
      if (_particles.length < 40) {
        // Main horizontal lines
        final offsetX = math.cos(_animationValue) * maxRadius * 0.5;
        final offsetY = math.sin(_animationValue * 3) * 3.0;

        _particles.add(Particle(
          position: center + Offset(offsetX, offsetY),
          velocity: Offset(
            math.sin(_animationValue) * 1.5,
            math.cos(_animationValue * 2) * 0.1,
          ),
          size: 4.0 + animationFactor * 3.0,
          opacity: 0.8,
          color: Color.lerp(Colors.yellow, Colors.white, 0.3)!,
          shape: ParticleShape.line,
          rotation: 0,
          useGlow: true,
          glowIntensity: 0.4,
          lifespan: 80.0,
          decay: 0.015,
          scale: 1.3,
          scaleSpeed: 0.005 * (timeFactor < 0.5 ? 1 : -1),
        ));

        // Add pulsating dots
        if (timeFactor < 0.1) {
          _particles.add(Particle(
            position: center + Offset(
              math.cos(_animationValue * 2) * maxRadius * 0.6,
              math.sin(_animationValue * 3) * 5.0,
            ),
            velocity: Offset(
              math.sin(_animationValue) * 0.3,
              math.cos(_animationValue) * 0.3,
            ),
            size: 3.0,
            opacity: 0.7,
            color: Colors.yellow.withAlpha(204),
            shape: ParticleShape.circle,
            useGlow: true,
            glowIntensity: 0.5,
            lifespan: 65.0,
            decay: 0.015,
            scale: 1.0,
            scaleSpeed: 0.01 * (timeFactor < 0.5 ? 1 : -1),
          ));
        }
      }
    } else if (_indicatorValue < 80) {
      // Lift (61-80): Green rotating gears with acceleration effect
      if (_particles.length < 70) {
        final angle = _animationValue * 1.5;
        final distance = maxRadius * 0.7 * animationFactor;

        // Main particles - gears
        _particles.add(Particle(
          position: center + Offset(
            math.cos(angle) * distance,
            math.sin(angle) * distance,
          ),
          velocity: Offset(
            math.cos(angle + math.pi/4) * 1.2,
            math.sin(angle + math.pi/4) * 1.2,
          ),
          size: 6.0,
          opacity: 0.9,
          color: Color.lerp(Colors.lightGreen, Colors.green, animationFactor)!,
          shape: timeFactor < 0.7 ? ParticleShape.circle :
                 timeFactor < 0.85 ? ParticleShape.diamond : ParticleShape.star,
          rotation: angle,
          rotationSpeed: 0.1,
          useGlow: true,
          glowIntensity: 0.7,
          lifespan: 100.0,
          decay: 0.01,
          useGradient: true,
          gradientColors: [
            Colors.lightGreen.withAlpha(204),
            Colors.green.withAlpha(153),
          ],
        ));

        // Add acceleration trails
        if (timeFactor < 0.2) {
          for (int i = 0; i < 3; i++) {
            final trailAngle = angle - (0.2 - i * 0.05);
            final trailDistance = distance * 0.85;
            _particles.add(Particle(
              position: center + Offset(
                math.cos(trailAngle) * trailDistance,
                math.sin(trailAngle) * trailDistance,
              ),
              velocity: Offset(
                math.cos(trailAngle + math.pi/4) * 0.6,
                math.sin(trailAngle + math.pi/4) * 0.6,
              ),
              size: 2.0,
              opacity: 0.6,
              color: Colors.lightGreen.withAlpha(153),
              shape: ParticleShape.circle,
              lifespan: 40.0,
              decay: 0.03,
            ));
          }
        }
      }
    } else {
      // Surge (81-100): Bright green sparks with explosion effect
      if (_particles.length < 80) {
        final angle = _animationValue * 2;
        final distance = maxRadius * animationFactor;

        // Main particles - sparks
        _particles.add(Particle(
          position: center + Offset(
            math.cos(angle) * distance,
            math.sin(angle) * distance,
          ),
          velocity: Offset(
            math.cos(angle) * 3.0,
            math.sin(angle) * 3.0,
          ),
          size: 5.0,
          opacity: 0.95,
          color: Color.lerp(Colors.green, Colors.white, 0.3)!,
          shape: timeFactor < 0.6 ? ParticleShape.star :
                 timeFactor < 0.8 ? ParticleShape.diamond : ParticleShape.circle,
          rotation: angle * 2,
          rotationSpeed: 0.15,
          useGlow: true,
          glowIntensity: 0.8,
          lifespan: 90.0,
          decay: 0.01,
          useGradient: true,
          gradientColors: [
            Colors.green.withAlpha(229),
            Colors.lightGreen.withAlpha(178),
            Colors.white.withAlpha(127),
          ],
        ));

        // Add explosion effect (additional particles)
        if (timeFactor < 0.15) {
          final burstCenter = center + Offset(
            math.cos(angle) * distance * 0.7,
            math.sin(angle) * distance * 0.7,
          );

          for (int i = 0; i < 5; i++) {
            final burstAngle = angle + (i * math.pi * 2 / 5);
            final burstSpeed = 2.5;

            _particles.add(Particle(
              position: burstCenter,
              velocity: Offset(
                math.cos(burstAngle) * burstSpeed,
                math.sin(burstAngle) * burstSpeed,
              ),
              size: 2.0,
              opacity: 0.8,
              color: Color.lerp(Colors.green, Colors.white, 0.4)!,
              shape: ParticleShape.circle,
              useGlow: true,
              glowIntensity: 0.6,
              lifespan: 50.0,
              decay: 0.03,
            ));
          }
        }
      }
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    _animationTimer?.cancel();
    _dataRefreshTimer?.cancel();
    super.dispose();
  }

  // Static flag to prevent multiple simultaneous data loads
  static bool isDataLoading = false;

  /// Load all data for the screen
  Future<void> _loadAllData() async {
    try {
      if (isDataLoading) {
        debugPrint('Data loading already in progress, skipping duplicate request');
        return;
      }

      isDataLoading = true;

      try {
        debugPrint('Loading all data for Enhanced Reactor screen...');

        setState(() {
          _isLoading = true;
        });

        // Get market sentiment
        final indicator = await _sentimentService.calculateMarketSentiment();

        // Get historical data
        final history = await _sentimentService.getHistoricalData();

        // Get historical data for stable forecasting
        final historicalScores = history.entries.map((entry) => entry.value).toList();

        // Get predictions using stable forecast service
        final stableForecasts = await _forecastService.getForecasts(
          historicalScores: historicalScores,
          lastDate: DateTime.now(),
          daysAhead: 7,
        );

        // Convert forecasts to SentimentHistoryEntry format
        final predictions = stableForecasts.map((forecast) =>
          SentimentHistoryEntry(
            date: forecast.date,
            value: forecast.predictedScore,
            metrics: {
              'Fear & Greed Index': forecast.predictedScore,
              'News Sentiment': forecast.predictedScore,
              'Holders Score': forecast.predictedScore,
              'Volume Score': forecast.predictedScore,
              'Social Engagement': forecast.predictedScore,
              'Price Volatility': forecast.predictedScore,
              'Bitcoin Dominance': forecast.predictedScore,
            },
          )
        ).toList();

        // Get yesterday and last week entries
        final yesterdayEntry = history.getYesterdayEntry();
        final lastWeekEntry = history.getLastWeekEntry();

        // Update state
        if (mounted) {
          setState(() {
            _indicatorValue = indicator;
            _predictions = predictions;
            _yesterdayEntry = yesterdayEntry;
            _lastWeekEntry = lastWeekEntry;
            _isLoading = false;
            _isLoadingHistory = false;
          });
        }

        // Set last refresh time
        _lastRefreshTime = DateTime.now();
        debugPrint('Data refresh completed at: $_lastRefreshTime');
      } finally {
        isDataLoading = false;
      }
    } catch (e) {
      debugPrint('Error loading data: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
          _isLoadingHistory = false;
        });
      }
    }
  }

  /// Get the current sentiment level description
  String _getCurrentLevel(double value) {
    if (value >= 80) return 'Surge';
    if (value >= 60) return 'Lift';
    if (value >= 40) return 'Stasis';
    if (value >= 20) return 'Anxiety';
    return 'Crash';
  }

  /// Get the detailed description for the current sentiment level
  String _getCurrentLevelDescription(double value) {
    if (value >= 80) return 'Euphoric momentum driving a strong rally.';
    if (value >= 60) return 'Optimism fueling steady upward movement.';
    if (value >= 40) return 'Balanced market with no clear direction.';
    if (value >= 20) return 'Caution and moderate downward trend.';
    return 'Extreme panic and sharp market sell-off.';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      extendBody: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        title: Row(
          children: [
            // Refresh button
            IconButton(
              icon: const Icon(
                Icons.refresh,
                color: DesignSystem.accentBlue,
                size: 24,
              ),
              onPressed: () {
                debugPrint('Refresh button pressed');

                setState(() {
                  _isLoading = true;
                });

                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Refreshing data...'),
                    duration: Duration(seconds: 2),
                    backgroundColor: DesignSystem.accentBlue,
                  ),
                );

                _sentimentService.clearCache().then((_) {
                  debugPrint('All caches cleared, reloading data with fresh API calls...');
                  _lastRefreshTime = DateTime.now();
                  _loadAllData();
                });
              },
            ),
            // Empty space to push the title to the center
            Expanded(child: Container()),
            // Title
            Text(
              'Crypto Pulse',
              style: DesignSystem.headingS.copyWith(
                letterSpacing: DesignSystem.letterSpacingTight,
              ),
            ),
            // Empty space to balance the layout
            Expanded(child: Container()),
          ],
        ),
        centerTitle: false,
        elevation: 0,
        systemOverlayStyle: SystemUiOverlayStyle.light,
        automaticallyImplyLeading: false,
      ),
      // Gradient background container
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              DesignSystem.backgroundStart,
              DesignSystem.backgroundEnd,
            ],
          ),
        ),
        child: _buildContent(),
      ),
    );
  }

  Widget _buildContent() {
    return Stack(
      children: [
        // Main content area
        Positioned.fill(
          child: _isLoading
              ? const Center(child: CircularProgressIndicator())
              : RefreshIndicator(
                  onRefresh: _loadAllData,
                  color: DesignSystem.accentBlue,
                  backgroundColor: DesignSystem.cardBackground,
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Padding(
                      padding: EdgeInsets.only(
                        left: DesignSystem.spacing16,
                        right: DesignSystem.spacing16,
                        top: DesignSystem.spacing16 + 60, // Увеличиваю отступ сверху чтобы опустить содержимое
                        bottom: DesignSystem.spacing16 + 100, // Добавляю отступ снизу для навигационной панели
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          // Main content row with reactor, speedometer and predictions
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Left side - Reactor image (moved 50px left)
                              Transform.translate(
                                offset: const Offset(-50, 0),
                                child: _buildReactorImage(),
                              ),

                              SizedBox(width: DesignSystem.spacing24),

                              // Middle - Speedometer and value (moved 50px left)
                              Transform.translate(
                                offset: const Offset(-50, 0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    // Add space to move speedometer down
                                    SizedBox(height: DesignSystem.spacing64 + DesignSystem.spacing40 + DesignSystem.spacing16),

                                    // Speedometer indicator
                                    _buildSpeedometerIndicator(),
                                  ],
                                ),
                              ),

                              SizedBox(width: DesignSystem.spacing24),

                              // Right side - Trend Visualization (positioned to the right of speedometer)
                              if (_predictions.isNotEmpty)
                                Transform.translate(
                                  offset: const Offset(0, 60), // Move down to align with speedometer
                                  child: _buildTrendVisualization(),
                                ),
                            ],
                          ),

                          SizedBox(height: DesignSystem.spacing24),

                          // Enhanced Prognosis section (horizontal layout, wider by 45px and moved down by 20px)
                          if (_predictions.isNotEmpty)
                            Transform.translate(
                              offset: const Offset(0, 20), // Move down by 20px
                              child: Container(
                                width: double.infinity,
                                padding: EdgeInsets.all(DesignSystem.spacing16 + 15), // Added 15px padding
                                margin: EdgeInsets.symmetric(horizontal: DesignSystem.spacing16 - 45), // Reduced margin by 45px to make it wider
                                decoration: BoxDecoration(
                                  color: DesignSystem.cardBackground,
                                  borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
                                  border: Border.all(
                                    color: Colors.grey.shade800.withAlpha(76), // withOpacity(0.3) -> withAlpha(76)
                                    width: 1,
                                  ),
                                ),
                                child: _buildPredictionsSection(),
                              ),
                            ),

                          SizedBox(height: DesignSystem.spacing24),

                          // Trading signal button
                          Transform.translate(
                            offset: const Offset(8, -95), // Move up by 95px (was -145px) and 8px right
                            child: _buildSignalButton(),
                          ),

                          // Add some space at the bottom
                          SizedBox(height: DesignSystem.spacing40),
                        ],
                      ),
                    ),
                  ),
                ),
        ),

        // Bottom navigation bar
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: AppBottomNavigation(
            currentIndex: 2,
            onTap: (index) {
              if (index != 2) {
                switch (index) {
                  case 0:
                    Navigator.pushReplacementNamed(context, '/news');
                    break;
                  case 1:
                    Navigator.pushReplacementNamed(context, '/charts');
                    break;
                  case 3:
                    Navigator.pushReplacementNamed(context, '/courses');
                    break;
                  case 4:
                    Navigator.pushReplacementNamed(context, '/profile');
                    break;
                }
              }
            },
          ),
        ),
      ],
    );
  }

  /// Build the reactor image with premium design
  Widget _buildReactorImage() {
    final reactorSize = 380 * 1.3;
    final sentimentColor = DesignSystem.getSentimentColor(_indicatorValue);

    return Transform.translate(
      offset: const Offset(0, 50), // Move 50px down
      child: SizedBox(
        height: reactorSize,
        width: reactorSize,
        child: Stack(
          alignment: Alignment.center,
          children: [
            // Reactor background image with subtle glow
            Container(
              width: reactorSize,
              height: reactorSize,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: sentimentColor.withOpacity(0.1),
                    blurRadius: 40,
                    spreadRadius: 5,
                  ),
                ],
              ),
              child: Image.asset(
                'logo/Sinusoid/Reactor.png',
                fit: BoxFit.contain,
                width: reactorSize,
                height: reactorSize,
                errorBuilder: (context, error, stackTrace) {
                  // Show a fallback colored circle if image fails to load
                  return Container(
                    width: reactorSize * 0.95,
                    height: reactorSize * 0.95,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.grey.shade900,
                      border: Border.all(
                        color: sentimentColor,
                        width: 5,
                      ),
                    ),
                  );
                },
              ),
            ),

            // Central color indicator with premium glassmorphism effect
            Transform.translate(
              offset: const Offset(-2, -10), // Move 2px left and 10px up
              child: Center(
                child: Container(
                  width: 230,
                  height: 230,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: sentimentColor.withOpacity(0.7),
                    boxShadow: [
                      BoxShadow(
                        color: sentimentColor.withOpacity(0.3),
                        blurRadius: 30,
                        spreadRadius: 10,
                      ),
                    ],
                    border: Border.all(
                      color: Colors.white.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                ),
              ),
            ),

            // Animated particles
            Transform.translate(
              offset: const Offset(-2, -5), // Move 2px left and 5px up
              child: CustomPaint(
                size: Size(reactorSize, reactorSize),
                painter: ReactorAnimationPainter(
                  particles: _particles,
                  animationValue: _animationValue,
                  indicatorValue: _indicatorValue,
                  color: sentimentColor,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build the premium minimalist speedometer indicator
  Widget _buildSpeedometerIndicator() {
    final sentimentColor = DesignSystem.getSentimentColor(_indicatorValue);

    return Padding(
      padding: const EdgeInsets.only(top: 120), // Move 120px down
      child: Container(
        width: 240,
        height: 180,
        padding: EdgeInsets.only(top: DesignSystem.spacing8),
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: sentimentColor.withOpacity(0.05),
              blurRadius: 20,
              spreadRadius: 1,
            ),
          ],
        ),
        child: CustomPaint(
          painter: IOSGaugeSpeedometerPainter(
            value: _indicatorValue,
            color: sentimentColor,
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              // Value with premium typography
              Padding(
                padding: const EdgeInsets.only(bottom: 10),
                child: Text(
                  _indicatorValue.round().toString(),
                  style: DesignSystem.headingXL.copyWith(
                    color: DesignSystem.textPrimary,
                  ),
                ),
              ),

              // Level with premium typography
              Padding(
                padding: const EdgeInsets.only(bottom: 20),
                child: Column(
                  children: [
                    Text(
                      _getCurrentLevel(_indicatorValue),
                      style: DesignSystem.headingM.copyWith(
                        color: sentimentColor,
                      ),
                    ),
                    SizedBox(height: DesignSystem.spacing2),
                    // Premium underline with gradient
                    Container(
                      height: 2,
                      width: 100,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [
                            sentimentColor.withOpacity(0.3),
                            sentimentColor,
                            sentimentColor.withOpacity(0.3),
                          ],
                          begin: Alignment.centerLeft,
                          end: Alignment.centerRight,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 4),

              // Description with premium typography
              Padding(
                padding: const EdgeInsets.only(bottom: 20),
                child: Text(
                  _getCurrentLevelDescription(_indicatorValue),
                  textAlign: TextAlign.center,
                  style: DesignSystem.bodyS,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the predictions section with premium design - horizontal layout
  Widget _buildPredictionsSection() {
    // Get tomorrow and day after tomorrow predictions
    final tomorrow = _predictions.isNotEmpty ? _predictions[0] : null;
    final dayAfterTomorrow = _predictions.length > 1 ? _predictions[1] : null;

    return Padding(
      padding: const EdgeInsets.only(top: 5), // Move 5px down
      child: Container(
        padding: EdgeInsets.all(DesignSystem.spacing16),
        decoration: BoxDecoration(
          color: DesignSystem.cardBackground,
          borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
          border: Border.all(
            color: Colors.grey.shade800.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              'Future Prognosis',
              style: DesignSystem.headingS.copyWith(
                color: DesignSystem.textPrimary,
                letterSpacing: DesignSystem.letterSpacingTight,
              ),
            ),
            SizedBox(height: DesignSystem.spacing16),

            // Horizontal layout for predictions and trend analysis
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Left side - Predictions
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Tomorrow prediction
                      if (tomorrow != null)
                        _buildPredictionRow(
                          'Tomorrow',
                          tomorrow.value,
                          _indicatorValue,
                          true,
                        ),

                      SizedBox(height: 12),

                      // Day after tomorrow prediction
                      if (dayAfterTomorrow != null)
                        _buildPredictionRow(
                          'Day After',
                          dayAfterTomorrow.value,
                          tomorrow?.value ?? _indicatorValue,
                          false,
                        ),
                    ],
                  ),
                ),

                // Vertical divider
                if (tomorrow != null && dayAfterTomorrow != null)
                  Container(
                    height: 80,
                    width: 1,
                    margin: EdgeInsets.symmetric(horizontal: DesignSystem.spacing16),
                    color: Colors.grey.shade800.withAlpha(76),
                  ),

                // Right side - Trend analysis
                if (tomorrow != null && dayAfterTomorrow != null)
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Trend Analysis',
                          style: DesignSystem.labelL.copyWith(
                            color: DesignSystem.textPrimary,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: DesignSystem.spacing8),
                        _buildTrendAnalysis(),
                      ],
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build a prediction row with premium design
  Widget _buildPredictionRow(String label, double value, double previousValue, bool isHighlighted) {
    final sentimentColor = DesignSystem.getSentimentColor(value);
    final change = value - previousValue;
    final changePercent = previousValue > 0 ? (change / previousValue) * 100 : 0.0;
    final isPositive = change >= 0;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Label
        Text(
          label,
          style: DesignSystem.labelL.copyWith(
            color: isHighlighted ? DesignSystem.textPrimary : DesignSystem.textSecondary,
            fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.normal,
          ),
        ),

        // Value and change
        Row(
          children: [
            // Value
            Text(
              value.toStringAsFixed(1),
              style: DesignSystem.headingS.copyWith(
                color: sentimentColor,
                fontWeight: FontWeight.w600,
              ),
            ),
            SizedBox(width: DesignSystem.spacing8),

            // Change indicator
            Flexible(
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: 6,
                  vertical: DesignSystem.spacing4,
                ),
                decoration: BoxDecoration(
                  color: isPositive
                      ? DesignSystem.accentGreen.withAlpha(26) // withOpacity(0.1) -> withAlpha(26)
                      : DesignSystem.accentRed.withAlpha(26), // withOpacity(0.1) -> withAlpha(26)
                  borderRadius: BorderRadius.circular(DesignSystem.borderRadiusS),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                      color: isPositive ? DesignSystem.accentGreen : DesignSystem.accentRed,
                      size: 12,
                    ),
                    SizedBox(width: DesignSystem.spacing2),
                    Flexible(
                      child: Text(
                        '${change.abs().toStringAsFixed(1)} (${changePercent.abs().toStringAsFixed(1)}%)',
                        style: DesignSystem.labelS.copyWith(
                          color: isPositive ? DesignSystem.accentGreen : DesignSystem.accentRed,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Build trend analysis section
  Widget _buildTrendAnalysis() {
    // Calculate overall trend
    final firstPrediction = _predictions.isNotEmpty ? _predictions[0].value : _indicatorValue;
    final lastPrediction = _predictions.length > 1 ? _predictions[1].value : firstPrediction;

    final overallChange = lastPrediction - _indicatorValue;
    final trendStrength = overallChange.abs();

    String trendDescription;
    Color trendColor;
    IconData trendIcon;

    if (trendStrength < 2.0) {
      trendDescription = 'Stable market conditions expected';
      trendColor = DesignSystem.sentimentStasis;
      trendIcon = Icons.trending_flat;
    } else if (overallChange > 0) {
      if (trendStrength > 5.0) {
        trendDescription = 'Strong bullish trend forming';
        trendColor = DesignSystem.accentGreen;
        trendIcon = Icons.trending_up;
      } else {
        trendDescription = 'Moderate upward momentum';
        trendColor = DesignSystem.sentimentLift;
        trendIcon = Icons.trending_up;
      }
    } else {
      if (trendStrength > 5.0) {
        trendDescription = 'Strong bearish pressure ahead';
        trendColor = DesignSystem.accentRed;
        trendIcon = Icons.trending_down;
      } else {
        trendDescription = 'Slight downward pressure';
        trendColor = DesignSystem.sentimentAnxiety;
        trendIcon = Icons.trending_down;
      }
    }

    return Row(
      children: [
        Icon(
          trendIcon,
          color: trendColor,
          size: 16,
        ),
        SizedBox(width: DesignSystem.spacing8),
        Expanded(
          child: Text(
            trendDescription,
            style: DesignSystem.bodyS.copyWith(
              color: trendColor,
              fontWeight: FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  /// Build trend visualization widget
  Widget _buildTrendVisualization() {
    // Calculate overall trend
    final firstPrediction = _predictions.isNotEmpty ? _predictions[0].value : _indicatorValue;
    final lastPrediction = _predictions.length > 1 ? _predictions[1].value : firstPrediction;

    final overallChange = lastPrediction - _indicatorValue;
    final trendStrength = overallChange.abs();

    String trendDescription;
    Color trendColor;
    IconData trendIcon;

    if (trendStrength < 2.0) {
      trendDescription = 'Stable market conditions expected';
      trendColor = DesignSystem.sentimentStasis;
      trendIcon = Icons.trending_flat;
    } else if (overallChange > 0) {
      if (trendStrength > 5.0) {
        trendDescription = 'Strong bullish trend forming';
        trendColor = DesignSystem.accentGreen;
        trendIcon = Icons.trending_up;
      } else {
        trendDescription = 'Moderate upward momentum';
        trendColor = DesignSystem.sentimentLift;
        trendIcon = Icons.trending_up;
      }
    } else {
      if (trendStrength > 5.0) {
        trendDescription = 'Strong bearish pressure ahead';
        trendColor = DesignSystem.accentRed;
        trendIcon = Icons.trending_down;
      } else {
        trendDescription = 'Slight downward pressure';
        trendColor = DesignSystem.sentimentAnxiety;
        trendIcon = Icons.trending_down;
      }
    }

    return Transform.translate(
      offset: const Offset(0, 15), // Move 15px down
      child: Container(
        width: 240, // Fixed width to match speedometer
        padding: EdgeInsets.all(DesignSystem.spacing16),
        decoration: BoxDecoration(
          color: DesignSystem.cardBackground,
          borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
          border: Border.all(
            color: Colors.grey.shade800.withAlpha(76),
            width: 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Title
            Text(
              'Trend Visualization',
              style: DesignSystem.headingS.copyWith(
                color: DesignSystem.textPrimary,
                letterSpacing: DesignSystem.letterSpacingTight,
              ),
            ),
            SizedBox(height: DesignSystem.spacing16),

            // Trend icon and description
            Row(
              children: [
                Icon(
                  trendIcon,
                  color: trendColor,
                  size: 24,
                ),
                SizedBox(width: DesignSystem.spacing8),
                Expanded(
                  child: Text(
                    trendDescription,
                    style: DesignSystem.bodyM.copyWith(
                      color: trendColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),

            SizedBox(height: DesignSystem.spacing16),

            // Trend strength indicator
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Trend Strength',
                  style: DesignSystem.labelL,
                ),
                SizedBox(height: DesignSystem.spacing8),
                ClipRRect(
                  borderRadius: BorderRadius.circular(4),
                  child: LinearProgressIndicator(
                    value: trendStrength / 10, // Scale to 0-1 range (max strength is 10)
                    backgroundColor: Colors.grey.shade800.withAlpha(76),
                    valueColor: AlwaysStoppedAnimation<Color>(trendColor),
                    minHeight: 8,
                  ),
                ),
              ],
            ),

            SizedBox(height: DesignSystem.spacing16),

            // Trend direction
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Direction',
                  style: DesignSystem.labelL,
                ),
                SizedBox(height: DesignSystem.spacing8),
                Row(
                  children: [
                    Icon(
                      overallChange > 0 ? Icons.arrow_upward :
                      overallChange < 0 ? Icons.arrow_downward : Icons.arrow_forward,
                      color: trendColor,
                      size: 16,
                    ),
                    SizedBox(width: DesignSystem.spacing8),
                    Text(
                      overallChange > 0 ? 'Upward' :
                      overallChange < 0 ? 'Downward' : 'Neutral',
                      style: DesignSystem.bodyS.copyWith(
                        color: trendColor,
                      ),
                    ),
                    Spacer(),
                    Text(
                      '${overallChange.abs().toStringAsFixed(1)} points',
                      style: DesignSystem.bodyS.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// Build signal button that shows recommendation when clicked
  Widget _buildSignalButton() {
    // Determine recommendation based on predictions
    String recommendation;
    Color recommendationColor;
    IconData recommendationIcon;
    String recommendationDescription;

    // Get tomorrow and day after tomorrow predictions
    final tomorrow = _predictions.isNotEmpty ? _predictions[0] : null;
    final dayAfterTomorrow = _predictions.length > 1 ? _predictions[1] : null;

    // Calculate trend based on predictions
    double trendDirection = 0;
    double futureValue = 0;

    if (tomorrow != null) {
      // Calculate initial trend direction from current to tomorrow
      trendDirection = tomorrow.value - _indicatorValue;
      futureValue = tomorrow.value;

      // If we have day after tomorrow prediction, refine the trend
      if (dayAfterTomorrow != null) {
        // Add weight to the trend if it continues in the same direction
        double secondDayTrend = dayAfterTomorrow.value - tomorrow.value;

        // If both days show the same trend direction, strengthen the signal
        if ((trendDirection > 0 && secondDayTrend > 0) ||
            (trendDirection < 0 && secondDayTrend < 0)) {
          trendDirection = (trendDirection + secondDayTrend) * 1.2;
        }

        // Use the furthest prediction for future value
        futureValue = dayAfterTomorrow.value;
      }
    } else {
      // Fallback to current value if no predictions
      futureValue = _indicatorValue;
    }

    // Determine recommendation based on trend direction and future value
    if (trendDirection > 5 || (trendDirection > 2 && futureValue > 70)) {
      // Strong buy signal - significant upward trend or high future value
      recommendation = "Buy";
      recommendationColor = DesignSystem.accentGreen;
      recommendationIcon = Icons.trending_up;
      recommendationDescription = "Strong bullish trend predicted. Consider increasing positions.";
    } else if (trendDirection > 2 || (trendDirection > 0 && futureValue > 60)) {
      // Moderate buy signal - moderate upward trend
      recommendation = "Buy";
      recommendationColor = DesignSystem.accentGreen.withAlpha(204);
      recommendationIcon = Icons.trending_up;
      recommendationDescription = "Positive trend predicted. Good entry opportunity.";
    } else if (trendDirection.abs() < 2 || (futureValue >= 45 && futureValue <= 55)) {
      // Hold signal - stable trend or neutral future value
      recommendation = "Hold";
      recommendationColor = DesignSystem.sentimentStasis;
      recommendationIcon = Icons.trending_flat;
      recommendationDescription = "Market expected to remain stable. Maintain current positions.";
    } else if (trendDirection < -2 || (trendDirection < 0 && futureValue < 40)) {
      // Moderate sell signal - moderate downward trend
      recommendation = "Sell";
      recommendationColor = DesignSystem.accentRed.withAlpha(204);
      recommendationIcon = Icons.trending_down;
      recommendationDescription = "Negative trend predicted. Consider reducing exposure.";
    } else if (trendDirection < -5 || (trendDirection < -2 && futureValue < 30)) {
      // Strong sell signal - significant downward trend or low future value
      recommendation = "Sell";
      recommendationColor = DesignSystem.accentRed;
      recommendationIcon = Icons.trending_down;
      recommendationDescription = "Strong bearish trend predicted. Consider exiting positions.";
    } else {
      // Default hold signal if no clear pattern
      recommendation = "Hold";
      recommendationColor = DesignSystem.sentimentStasis;
      recommendationIcon = Icons.trending_flat;
      recommendationDescription = "No clear trend detected. Monitor market conditions.";
    }

    // Create a button that shows a dialog with recommendation details when pressed
    return Center(
      child: GestureDetector(
        onTap: () {
          // Show dialog with recommendation details
          showDialog(
            context: context,
            builder: (BuildContext context) {
              return AlertDialog(
                backgroundColor: const Color(0xFF1A2639),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
                ),
                title: Row(
                  children: [
                    Icon(
                      recommendationIcon,
                      color: recommendationColor,
                      size: 24,
                    ),
                    SizedBox(width: DesignSystem.spacing8),
                    Text(
                      'Trading Signal: $recommendation',
                      style: DesignSystem.headingS.copyWith(
                        color: DesignSystem.textPrimary,
                      ),
                    ),
                  ],
                ),
                content: Text(
                  recommendationDescription,
                  style: DesignSystem.bodyM.copyWith(
                    color: DesignSystem.textSecondary,
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                    child: Text(
                      'Close',
                      style: DesignSystem.labelL.copyWith(
                        color: DesignSystem.accentBlue,
                      ),
                    ),
                  ),
                ],
              );
            },
          );
        },
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: DesignSystem.spacing24,
            vertical: DesignSystem.spacing16,
          ),
          decoration: BoxDecoration(
            color: DesignSystem.cardBackground,
            borderRadius: BorderRadius.circular(DesignSystem.borderRadiusL),
            border: Border.all(
              color: recommendationColor,
              width: 1.5,
            ),
            boxShadow: [
              BoxShadow(
                color: recommendationColor.withAlpha(76),
                blurRadius: 10,
                spreadRadius: 0,
                offset: Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                recommendationIcon,
                color: recommendationColor,
                size: 24,
              ),
              SizedBox(width: DesignSystem.spacing8),
              Text(
                recommendation,
                style: DesignSystem.headingM.copyWith(
                  color: recommendationColor,
                  letterSpacing: DesignSystem.letterSpacingTight,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Particle class for animation
class Particle {
  Offset position;
  Offset velocity;
  double size;
  double opacity;
  Color color;
  double rotation;
  double rotationSpeed;
  double scale;
  double scaleSpeed;
  ParticleShape shape;
  List<Color> gradientColors;
  bool useGradient;
  bool useGlow;
  double glowIntensity;
  double lifespan;
  double decay;

  Particle({
    required this.position,
    required this.velocity,
    required this.size,
    required this.opacity,
    required this.color,
    this.rotation = 0.0,
    this.rotationSpeed = 0.0,
    this.scale = 1.0,
    this.scaleSpeed = 0.0,
    this.shape = ParticleShape.circle,
    this.gradientColors = const [],
    this.useGradient = false,
    this.useGlow = false,
    this.glowIntensity = 0.5,
    this.lifespan = 100.0,
    this.decay = 0.01,
  });

  void update() {
    position = position + velocity;
    rotation += rotationSpeed;
    scale += scaleSpeed;
    if (scale < 0.1) scale = 0.1;
    opacity = opacity - decay;
    if (opacity < 0) opacity = 0;
    lifespan -= 1.0;
  }

  bool isDead() {
    return opacity <= 0 || lifespan <= 0;
  }
}

/// Particle shapes
enum ParticleShape {
  circle,
  square,
  triangle,
  diamond,
  star,
  line,
  custom,
}

/// Custom painter for the reactor animation
class ReactorAnimationPainter extends CustomPainter {
  final List<Particle> particles;
  final double animationValue;
  final double indicatorValue;
  final Color color;

  ReactorAnimationPainter({
    required this.particles,
    required this.animationValue,
    required this.indicatorValue,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw all particles
    for (final particle in particles) {
      canvas.save();
      canvas.translate(particle.position.dx, particle.position.dy);
      canvas.rotate(particle.rotation);
      canvas.scale(particle.scale);

      final paint = Paint()
        ..color = particle.color.withOpacity(particle.opacity)
        ..style = PaintingStyle.fill;

      // Apply gradient if needed
      if (particle.useGradient && particle.gradientColors.length >= 2) {
        paint.shader = RadialGradient(
          colors: particle.gradientColors,
          stops: _generateGradientStops(particle.gradientColors.length),
        ).createShader(Rect.fromCircle(
          center: Offset.zero,
          radius: particle.size * 1.2,
        ));
      }

      // Draw glow if needed
      if (particle.useGlow) {
        final glowPaint = Paint()
          ..color = particle.color.withOpacity(particle.opacity * 0.5)
          ..style = PaintingStyle.fill
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, particle.size * particle.glowIntensity * 0.5);

        _drawParticleShape(canvas, Offset.zero, particle.size * 1.3, particle.shape, glowPaint);
      }

      // Draw main particle shape
      _drawParticleShape(canvas, Offset.zero, particle.size, particle.shape, paint);

      canvas.restore();
    }

    // Center of the reactor
    final exactCenter = Offset(size.width / 2, size.height / 2 - 5);

    // Maximum radius for animations
    final maxRadius = 70.0;

    // Additional effects based on indicator level
    if (indicatorValue < 20) {
      // Crash (0-20): Red flashes with pulsation
      final glowPaint = Paint()
        ..color = Color.lerp(Colors.red, Colors.white, 0.2)!.withOpacity(0.4)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 20);

      canvas.drawCircle(exactCenter, maxRadius * 0.6, glowPaint);

      // Pulsating circle
      final pulseSize = 0.5 + math.sin(animationValue * 3) * 0.2;
      final pulsePaint = Paint()
        ..color = Colors.red.withOpacity(0.1)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 10);

      canvas.drawCircle(exactCenter, maxRadius * pulseSize, pulsePaint);
    } else if (indicatorValue < 40) {
      // Anxiety (21-40): Orange vortices
      final glowPaint = Paint()
        ..color = Color.lerp(Colors.orange, Colors.amber, 0.3)!.withOpacity(0.05)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 15);

      canvas.drawCircle(exactCenter, maxRadius * 0.7, glowPaint);
    } else if (indicatorValue < 60) {
      // Stasis (41-60): Yellow horizontal lines
      final glowPaint = Paint()
        ..color = Color.lerp(Colors.yellow, Colors.white, 0.3)!.withOpacity(0.04)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 15);

      canvas.drawCircle(exactCenter, maxRadius * 0.7, glowPaint);
    } else if (indicatorValue < 80) {
      // Lift (61-80): Green rotating gears
      final glowPaint = Paint()
        ..color = Color.lerp(Colors.green, Colors.lightGreen, 0.3)!.withOpacity(0.05)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 20);

      canvas.drawCircle(exactCenter, maxRadius * 0.8, glowPaint);
    } else {
      // Surge (81-100): Bright green sparks
      final glowPaint = Paint()
        ..color = Colors.green.withOpacity(0.07)
        ..style = PaintingStyle.fill
        ..maskFilter = MaskFilter.blur(BlurStyle.normal, 25);

      canvas.drawCircle(exactCenter, maxRadius * 0.9, glowPaint);
    }
  }

  // Helper method to draw particle shape
  void _drawParticleShape(Canvas canvas, Offset center, double size, ParticleShape shape, Paint paint) {
    switch (shape) {
      case ParticleShape.circle:
        canvas.drawCircle(center, size, paint);
        break;
      case ParticleShape.square:
        canvas.drawRect(
          Rect.fromCenter(center: center, width: size * 2, height: size * 2),
          paint,
        );
        break;
      case ParticleShape.triangle:
        final path = Path();
        path.moveTo(center.dx, center.dy - size);
        path.lineTo(center.dx - size, center.dy + size);
        path.lineTo(center.dx + size, center.dy + size);
        path.close();
        canvas.drawPath(path, paint);
        break;
      case ParticleShape.diamond:
        final path = Path();
        path.moveTo(center.dx, center.dy - size);
        path.lineTo(center.dx + size, center.dy);
        path.lineTo(center.dx, center.dy + size);
        path.lineTo(center.dx - size, center.dy);
        path.close();
        canvas.drawPath(path, paint);
        break;
      case ParticleShape.star:
        final path = Path();
        final outerRadius = size;
        final innerRadius = size * 0.4;
        final points = 5;

        for (int i = 0; i < points * 2; i++) {
          final radius = i.isEven ? outerRadius : innerRadius;
          final angle = i * math.pi / points;
          final x = center.dx + math.cos(angle) * radius;
          final y = center.dy + math.sin(angle) * radius;

          if (i == 0) {
            path.moveTo(x, y);
          } else {
            path.lineTo(x, y);
          }
        }

        path.close();
        canvas.drawPath(path, paint);
        break;
      case ParticleShape.line:
        canvas.drawLine(
          center - Offset(size, 0),
          center + Offset(size, 0),
          paint,
        );
        break;
      case ParticleShape.custom:
        // Custom shape not implemented
        canvas.drawCircle(center, size, paint);
        break;
    }
  }

  // Helper method to generate gradient stops
  List<double> _generateGradientStops(int count) {
    final stops = <double>[];
    for (int i = 0; i < count; i++) {
      stops.add(i / (count - 1));
    }
    return stops;
  }

  @override
  bool shouldRepaint(ReactorAnimationPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue ||
           oldDelegate.indicatorValue != indicatorValue ||
           oldDelegate.particles != particles ||
           oldDelegate.color != color;
  }
}

/// Custom painter for the iOS-style gauge speedometer
class IOSGaugeSpeedometerPainter extends CustomPainter {
  final double value;
  final Color color;

  IOSGaugeSpeedometerPainter({
    required this.value,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 3 + 30);
    final radius = size.width * 0.4;

    // Draw the arc background
    final bgPaint = Paint()
      ..color = Colors.grey.shade800.withAlpha(76)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4.0
      ..strokeCap = StrokeCap.round;

    // Draw background arc (180 degrees)
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi, // Start from the left (180 degrees)
      math.pi, // End at the right (0 degrees)
      false,
      bgPaint,
    );

    // Create gradient for the colored arc
    final gradientPaint = Paint()
      ..shader = SweepGradient(
        center: Alignment.center,
        startAngle: math.pi,
        endAngle: 2 * math.pi,
        colors: const [
          Color(0xFFFF3B30), // Red - Crash
          Color(0xFFFF9500), // Orange - Anxiety
          Color(0xFFFFCC00), // Yellow - Stasis
          Color(0xFFAED581), // Lime - Lift
          Color(0xFF34C759), // Green - Surge
        ],
        stops: const [0.0, 0.25, 0.5, 0.75, 1.0],
      ).createShader(Rect.fromCircle(center: center, radius: radius))
      ..style = PaintingStyle.stroke
      ..strokeWidth = 4.0
      ..strokeCap = StrokeCap.round;

    // Calculate the angle based on the value (0-100)
    final valueAngle = (value / 100) * math.pi;

    // Draw the colored arc up to the current value
    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi, // Start from the left (180 degrees)
      valueAngle, // End at the position corresponding to the value
      false,
      gradientPaint,
    );

    // Draw subtle tick marks
    _drawTickMarks(canvas, center, radius);

    // Draw the moving ball
    _drawMovingBall(canvas, center, radius, value);
  }

  // Draw tick marks for the speedometer
  void _drawTickMarks(Canvas canvas, Offset center, double radius) {
    final tickPaint = Paint()
      ..color = Colors.grey.shade400.withAlpha(178)
      ..strokeWidth = 2.0
      ..style = PaintingStyle.stroke;

    // Draw 5 major ticks with labels for different phases
    for (int i = 0; i <= 4; i++) {
      final angle = math.pi + (i / 4) * math.pi;

      final outerPoint = Offset(
        center.dx + (radius + 2) * math.cos(angle),
        center.dy + (radius + 2) * math.sin(angle),
      );

      final innerPoint = Offset(
        center.dx + (radius - 8) * math.cos(angle),
        center.dy + (radius - 8) * math.sin(angle),
      );

      canvas.drawLine(innerPoint, outerPoint, tickPaint);

      // Draw labels for each section
      final labelOffset = Offset(
        center.dx + (radius + 15) * math.cos(angle),
        center.dy + (radius + 15) * math.sin(angle),
      );

      final textPainter = TextPainter(
        text: TextSpan(
          text: '${i * 25}',
          style: TextStyle(
            color: Colors.grey.shade400,
            fontSize: 10,
            fontWeight: FontWeight.bold,
          ),
        ),
        textAlign: TextAlign.center,
        textDirection: TextDirection.ltr,
      );

      textPainter.layout();
      textPainter.paint(
        canvas,
        Offset(
          labelOffset.dx - textPainter.width / 2,
          labelOffset.dy - textPainter.height / 2,
        ),
      );
    }

    // Draw minor ticks for better scale visualization
    for (int i = 1; i < 20; i++) {
      if (i % 5 != 0) { // Skip positions where major ticks are
        final angle = math.pi + (i / 20) * math.pi;

        final outerPoint = Offset(
          center.dx + radius * math.cos(angle),
          center.dy + radius * math.sin(angle),
        );

        final innerPoint = Offset(
          center.dx + (radius - 4) * math.cos(angle),
          center.dy + (radius - 4) * math.sin(angle),
        );

        final minorTickPaint = Paint()
          ..color = Colors.grey.shade400.withAlpha(102)
          ..strokeWidth = 1.0
          ..style = PaintingStyle.stroke;

        canvas.drawLine(innerPoint, outerPoint, minorTickPaint);
      }
    }

    // Draw dividing lines between sections
    final dividerPaint = Paint()
      ..color = Colors.grey.shade300
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw 4 dividing lines (at 20, 40, 60, 80)
    for (int i = 1; i <= 4; i++) {
      final angle = math.pi + (i / 5) * math.pi;

      final outerPoint = Offset(
        center.dx + (radius + 5) * math.cos(angle),
        center.dy + (radius + 5) * math.sin(angle),
      );

      final innerPoint = Offset(
        center.dx + (radius - 15) * math.cos(angle),
        center.dy + (radius - 15) * math.sin(angle),
      );

      canvas.drawLine(innerPoint, outerPoint, dividerPaint);
    }
  }

  // Draw the moving ball indicator
  void _drawMovingBall(Canvas canvas, Offset center, double radius, double value) {
    final angle = math.pi + (value / 100) * math.pi;

    final ballPosition = Offset(
      center.dx + radius * math.cos(angle),
      center.dy + radius * math.sin(angle),
    );

    // Draw arrow pointing to current value
    final arrowPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // Calculate arrow points
    final arrowBaseCenter = Offset(
      center.dx + (radius - 20) * math.cos(angle),
      center.dy + (radius - 20) * math.sin(angle),
    );

    // Arrow head points toward the ball
    final arrowPath = Path();
    arrowPath.moveTo(ballPosition.dx, ballPosition.dy); // Tip of arrow at ball position

    // Calculate perpendicular direction for arrow base
    final perpAngle = angle + math.pi/2;
    final arrowWidth = 6.0;

    // Base points of the arrow
    final basePoint1 = Offset(
      arrowBaseCenter.dx + arrowWidth * math.cos(perpAngle),
      arrowBaseCenter.dy + arrowWidth * math.sin(perpAngle),
    );

    final basePoint2 = Offset(
      arrowBaseCenter.dx - arrowWidth * math.cos(perpAngle),
      arrowBaseCenter.dy - arrowWidth * math.sin(perpAngle),
    );

    arrowPath.lineTo(basePoint1.dx, basePoint1.dy);
    arrowPath.lineTo(basePoint2.dx, basePoint2.dy);
    arrowPath.close();

    // Draw arrow
    canvas.drawPath(arrowPath, arrowPaint);

    // Draw glow
    final glowPaint = Paint()
      ..color = color.withAlpha(76)
      ..style = PaintingStyle.fill
      ..maskFilter = MaskFilter.blur(BlurStyle.normal, 8);

    canvas.drawCircle(ballPosition, 8, glowPaint);

    // Draw ball
    final ballPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    canvas.drawCircle(ballPosition, 6, ballPaint);

    // Draw highlight
    final highlightPaint = Paint()
      ..color = Colors.white.withAlpha(204)
      ..style = PaintingStyle.fill;

    canvas.drawCircle(
      ballPosition - const Offset(2, 2),
      2,
      highlightPaint,
    );

    // Draw current value text
    final valueText = value.toInt().toString();
    final textPainter = TextPainter(
      text: TextSpan(
        text: valueText,
        style: TextStyle(
          color: color,
          fontSize: 14,
          fontWeight: FontWeight.bold,
        ),
      ),
      textAlign: TextAlign.center,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    // Position text below the ball
    final textPosition = Offset(
      ballPosition.dx - textPainter.width / 2,
      ballPosition.dy + 15,
    );

    textPainter.paint(canvas, textPosition);
  }

  @override
  bool shouldRepaint(IOSGaugeSpeedometerPainter oldDelegate) {
    return oldDelegate.value != value || oldDelegate.color != color;
  }
}
