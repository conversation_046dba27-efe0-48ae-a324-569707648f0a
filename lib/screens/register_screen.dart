import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import '../providers/auth_provider.dart';
import 'hyperjump_animation_screen.dart';
import 'terms_of_service_screen.dart';
import '../widgets/cosmic_background.dart';
import '../widgets/falling_asteroids.dart';
import '../widgets/comet_animation.dart';
import '../widgets/twinkling_stars.dart';
import '../widgets/moving_clouds.dart';
import '../config/design_system.dart';
import '../widgets/glassmorphic_card.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({Key? key}) : super(key: key);

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _acceptedTerms = false;

  late AnimationController _starsController;
  final List<Star> _stars = [];

  @override
  void initState() {
    super.initState();

    // Создаем звезды для фона
    for (int i = 0; i < 100; i++) {
      _stars.add(Star(
        x: math.Random().nextDouble() * 1.0,
        y: math.Random().nextDouble() * 1.0,
        size: math.Random().nextDouble() * 2.0 + 1.0,
        blinkDuration: (math.Random().nextDouble() * 2.0 + 3.0) * 1000,
      ));
    }

    // Инициализируем контроллер анимации для звезд
    _starsController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 5000),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _starsController.dispose();
    super.dispose();
  }

  void _register() async {
    if (_formKey.currentState!.validate()) {
      if (!_acceptedTerms) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Please accept the Terms of Service'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
            margin: const EdgeInsets.all(16),
          ),
        );
        return;
      }

      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      try {
        final success = await authProvider.register(
          _nameController.text.trim(),
          _emailController.text.trim(),
          _passwordController.text.trim(),
        );

        if (success && mounted) {
          // Показываем сообщение об успехе
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: const Text('Account created successfully!'),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              margin: const EdgeInsets.all(16),
            ),
          );

          // Переход к анимации гиперпрыжка
          Navigator.pushReplacement(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) => const HyperjumpAnimationScreen(),
              transitionsBuilder: (context, animation, secondaryAnimation, child) {
                return FadeTransition(
                  opacity: animation,
                  child: child,
                );
              },
              transitionDuration: const Duration(milliseconds: 500),
            ),
          );
        } else if (mounted && authProvider.error.isNotEmpty) {
          // Показываем ошибку через SnackBar
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(authProvider.error),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              margin: const EdgeInsets.all(16),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Registration failed: ${e.toString()}'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
              margin: const EdgeInsets.all(16),
            ),
          );
        }
      }
    }
  }

  void _registerWithSocial(String provider) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    bool success = false;

    // Вызываем соответствующий метод в зависимости от провайдера
    switch (provider) {
      case 'google':
        success = await authProvider.registerWithGoogle();
        break;
      case 'facebook':
        success = await authProvider.registerWithFacebook();
        break;
      case 'linkedin':
        success = await authProvider.registerWithLinkedIn();
        break;
    }

    if (success && mounted) {
      // Переход к анимации гиперпрыжка
      Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => const HyperjumpAnimationScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final authProvider = Provider.of<AuthProvider>(context);

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          // Космический фон
          CosmicBackground(
            starCount: 450,
            minStarSize: 1.0,
            maxStarSize: 3.0,
            animationDuration: const Duration(milliseconds: 6000),
            enableComet: true,
            backgroundColor: const Color(0xFF000011),
            enableParallax: true,
            parallaxIntensity: 17.5,
            enableAsteroids: true,
            enableSatellites: true,
          ),

          // Основной контент
          Center(
            child: SingleChildScrollView(
              child: Container(
                width: math.min(380, size.width * 0.9),
                constraints: BoxConstraints(
                  maxHeight: math.min(580, size.height * 0.8),
                ),
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24),
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      const Color(0xFF1A1A2E).withOpacity(0.95),
                      const Color(0xFF16213E).withOpacity(0.9),
                      const Color(0xFF0F3460).withOpacity(0.85),
                    ],
                    stops: const [0.0, 0.5, 1.0],
                  ),
                  border: Border.all(
                    color: Colors.white.withOpacity(0.15),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.4),
                      blurRadius: 30,
                      offset: const Offset(0, 15),
                      spreadRadius: -5,
                    ),
                    BoxShadow(
                      color: const Color(0xFF0F3460).withOpacity(0.3),
                      blurRadius: 60,
                      offset: const Offset(0, 30),
                      spreadRadius: -10,
                    ),
                  ],
                ),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Заголовок формы
                      Column(
                        children: [
                          Text(
                            'Create account',
                            style: TextStyle(
                              fontSize: 28,
                              fontWeight: FontWeight.w700,
                              color: Colors.white,
                            ),
                          ).animate().fadeIn(duration: 500.ms, delay: 100.ms),

                          const SizedBox(height: 8),

                          Text(
                            'Join us today',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white.withOpacity(0.6),
                            ),
                          ).animate().fadeIn(duration: 500.ms, delay: 150.ms),
                        ],
                      ),

                      const SizedBox(height: 20),

                      // Поле для имени
                      _ModernTextField(
                        controller: _nameController,
                        hintText: 'Name',
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your name';
                          }
                          return null;
                        },
                      ).animate().fadeIn(duration: 500.ms, delay: 200.ms),

                      const SizedBox(height: 12),

                      // Поле для email
                      _ModernTextField(
                        controller: _emailController,
                        hintText: 'Email',
                        keyboardType: TextInputType.emailAddress,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your email';
                          }
                          // Улучшенная валидация email
                          final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
                          if (!emailRegex.hasMatch(value.trim())) {
                            return 'Please enter a valid email address';
                          }
                          return null;
                        },
                      ).animate().fadeIn(duration: 500.ms, delay: 300.ms),

                      const SizedBox(height: 12),

                      // Поле для пароля
                      _ModernTextField(
                        controller: _passwordController,
                        hintText: 'Password',
                        isPassword: true,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Please enter your password';
                          }
                          if (value.length < 8) {
                            return 'Password must be at least 8 characters';
                          }
                          // Проверка на наличие цифр и букв
                          if (!RegExp(r'^(?=.*[a-zA-Z])(?=.*\d)').hasMatch(value)) {
                            return 'Password must contain letters and numbers';
                          }
                          return null;
                        },
                      ).animate().fadeIn(duration: 500.ms, delay: 400.ms),

                      const SizedBox(height: 16),

                      // Чекбокс для пользовательского соглашения
                      Row(
                        children: [
                          Container(
                            width: 20,
                            height: 20,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              border: Border.all(
                                color: _acceptedTerms
                                  ? Colors.white.withOpacity(0.8)
                                  : Colors.white.withOpacity(0.3),
                                width: 2,
                              ),
                              gradient: _acceptedTerms
                                ? LinearGradient(
                                    colors: [
                                      const Color(0xFF1A1A2E),
                                      const Color(0xFF16213E),
                                    ],
                                  )
                                : null,
                              color: _acceptedTerms
                                ? null
                                : Colors.transparent,
                            ),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                borderRadius: BorderRadius.circular(4),
                                onTap: () {
                                  setState(() {
                                    _acceptedTerms = !_acceptedTerms;
                                  });
                                },
                                child: _acceptedTerms
                                  ? const Icon(
                                      Icons.check,
                                      color: Colors.white,
                                      size: 16,
                                    )
                                  : null,
                              ),
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _acceptedTerms = !_acceptedTerms;
                                });
                              },
                              child: RichText(
                                text: TextSpan(
                                  text: 'I accept the ',
                                  style: DesignSystem.bodyS.copyWith(
                                    color: Colors.white.withOpacity(0.7),
                                  ),
                                  children: [
                                    TextSpan(
                                      text: 'Terms of Service',
                                      style: DesignSystem.bodyS.copyWith(
                                        color: Colors.white.withOpacity(0.9),
                                        decoration: TextDecoration.underline,
                                      ),
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) => const TermsOfServiceScreen(),
                                            ),
                                          );
                                        },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ).animate().fadeIn(duration: 500.ms, delay: 500.ms),

                      const SizedBox(height: 16),

                      // Кнопка регистрации
                      _ModernButton(
                        text: 'Create account',
                        isLoading: authProvider.isLoading,
                        onPressed: _register,
                      ).animate().fadeIn(duration: 500.ms, delay: 600.ms),

                      const SizedBox(height: 16),

                      // Разделитель
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: Row(
                          children: [
                            Expanded(
                              child: Container(
                                height: 1,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.transparent,
                                      Colors.white.withOpacity(0.2),
                                      Colors.transparent,
                                    ],
                                  ),
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 16),
                              child: Text(
                                'OR',
                                style: DesignSystem.bodyS.copyWith(
                                  color: DesignSystem.textTertiary,
                                  fontWeight: FontWeight.w500,
                                  letterSpacing: 1.2,
                                ),
                              ),
                            ),
                            Expanded(
                              child: Container(
                                height: 1,
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: [
                                      Colors.transparent,
                                      Colors.white.withOpacity(0.2),
                                      Colors.transparent,
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ).animate().fadeIn(duration: 500.ms, delay: 700.ms),

                      // Кнопки социальных сетей
                      Column(
                        children: [
                          _ModernSocialButton(
                            icon: Icons.g_mobiledata,
                            text: 'Continue with Google',
                            onPressed: () => _registerWithSocial('google'),
                          ).animate().fadeIn(duration: 500.ms, delay: 800.ms),

                          const SizedBox(height: 12),

                          _ModernSocialButton(
                            icon: Icons.close,
                            text: 'Continue with X',
                            onPressed: () => _registerWithSocial('x'),
                          ).animate().fadeIn(duration: 500.ms, delay: 900.ms),
                        ],
                      ),

                      // Сообщение об ошибке
                      if (authProvider.error.isNotEmpty)
                        Container(
                          margin: const EdgeInsets.only(top: 16),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red.withOpacity(0.3)),
                          ),
                          child: Text(
                            authProvider.error,
                            style: const TextStyle(
                              color: Colors.red,
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ).animate().fadeIn(duration: 300.ms).shake(hz: 4),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Класс для представления звезды
class Star {
  final double x;
  final double y;
  final double size;
  final double blinkDuration;

  Star({
    required this.x,
    required this.y,
    required this.size,
    required this.blinkDuration,
  });
}

// Кастомный painter для отрисовки звезд
class StarsPainter extends CustomPainter {
  final List<Star> stars;
  final double animationValue;

  StarsPainter(this.stars, this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white;

    for (var star in stars) {
      // Вычисляем текущую прозрачность звезды
      final opacity = 0.3 + (0.7 - 0.3) *
          (math.sin(2 * math.pi * (animationValue + star.x * star.y) % 1) * 0.5 + 0.5);

      paint.color = Colors.white.withOpacity(opacity);

      canvas.drawCircle(
        Offset(star.x * size.width, star.y * size.height),
        star.size,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Современное поле ввода
class _ModernTextField extends StatefulWidget {
  final TextEditingController controller;
  final String hintText;
  final TextInputType? keyboardType;
  final bool isPassword;
  final bool isPasswordVisible;
  final VoidCallback? onTogglePasswordVisibility;
  final String? Function(String?)? validator;

  const _ModernTextField({
    Key? key,
    required this.controller,
    required this.hintText,
    this.keyboardType,
    this.isPassword = false,
    this.isPasswordVisible = false,
    this.onTogglePasswordVisibility,
    this.validator,
  }) : super(key: key);

  @override
  State<_ModernTextField> createState() => _ModernTextFieldState();
}

class _ModernTextFieldState extends State<_ModernTextField> {
  bool _isFocused = false;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();
    _focusNode.addListener(() {
      setState(() {
        _isFocused = _focusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _isFocused
            ? Colors.white.withOpacity(0.4)
            : Colors.white.withOpacity(0.1),
          width: 1,
        ),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.black.withOpacity(0.6),
            Colors.black.withOpacity(0.4),
          ],
        ),
      ),
      child: TextFormField(
        controller: widget.controller,
        focusNode: _focusNode,
        keyboardType: widget.keyboardType,
        obscureText: widget.isPassword && !widget.isPasswordVisible,
        style: DesignSystem.bodyL.copyWith(color: Colors.white),
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: DesignSystem.bodyL.copyWith(
            color: Colors.white.withOpacity(0.5),
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          suffixIcon: widget.isPassword
            ? IconButton(
                icon: Icon(
                  widget.isPasswordVisible ? Icons.visibility_off : Icons.visibility,
                  color: Colors.white.withOpacity(0.6),
                  size: 20,
                ),
                onPressed: widget.onTogglePasswordVisibility,
              )
            : null,
        ),
        validator: widget.validator,
      ),
    );
  }
}

// Современная кнопка
class _ModernButton extends StatefulWidget {
  final String text;
  final bool isLoading;
  final VoidCallback onPressed;

  const _ModernButton({
    Key? key,
    required this.text,
    required this.isLoading,
    required this.onPressed,
  }) : super(key: key);

  @override
  State<_ModernButton> createState() => _ModernButtonState();
}

class _ModernButtonState extends State<_ModernButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: double.infinity,
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: _isHovered
              ? [
                  const Color(0xFF2A2A3E),
                  const Color(0xFF1E1E2E),
                  const Color(0xFF16213E),
                ]
              : [
                  const Color(0xFF1A1A2E),
                  const Color(0xFF16213E),
                  const Color(0xFF0F3460),
                ],
            stops: const [0.0, 0.5, 1.0],
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 12,
              offset: const Offset(0, 4),
            ),
            if (_isHovered)
              BoxShadow(
                color: const Color(0xFF16213E).withOpacity(0.4),
                blurRadius: 20,
                offset: const Offset(0, 8),
              ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: widget.isLoading ? null : widget.onPressed,
            child: Center(
              child: widget.isLoading
                ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Text(
                    widget.text,
                    style: DesignSystem.bodyL.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
            ),
          ),
        ),
      ),
    );
  }
}

// Современная социальная кнопка
class _ModernSocialButton extends StatefulWidget {
  final IconData icon;
  final String text;
  final VoidCallback onPressed;

  const _ModernSocialButton({
    Key? key,
    required this.icon,
    required this.text,
    required this.onPressed,
  }) : super(key: key);

  @override
  State<_ModernSocialButton> createState() => _ModernSocialButtonState();
}

class _ModernSocialButtonState extends State<_ModernSocialButton> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: double.infinity,
        height: 48,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: _isHovered
              ? Colors.white.withOpacity(0.3)
              : Colors.white.withOpacity(0.1),
            width: 1,
          ),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: _isHovered
              ? [
                  Colors.black.withOpacity(0.7),
                  Colors.black.withOpacity(0.5),
                ]
              : [
                  Colors.black.withOpacity(0.5),
                  Colors.black.withOpacity(0.3),
                ],
          ),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: widget.onPressed,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  widget.icon,
                  color: Colors.white,
                  size: 20,
                ),
                const SizedBox(width: 12),
                Text(
                  widget.text,
                  style: DesignSystem.bodyM.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
