import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import 'register_screen.dart';
import 'login_screen.dart';
import 'hyperjump_animation_screen.dart';
import '../widgets/tmm_png_logo.dart';
import '../widgets/hover_button.dart';
import '../config/design_system.dart';
import '../widgets/cosmic_background.dart';
import '../widgets/glassmorphic_card.dart';

class WelcomeScreen extends StatefulWidget {
  const WelcomeScreen({Key? key}) : super(key: key);

  @override
  State<WelcomeScreen> createState() => _WelcomeScreenState();
}

class _WelcomeScreenState extends State<WelcomeScreen> with SingleTickerProviderStateMixin {
  late AnimationController _starsController;
  final List<Star> _stars = [];

  @override
  void initState() {
    super.initState();

    // Создаем звезды для фона
    for (int i = 0; i < 100; i++) {
      _stars.add(Star(
        x: math.Random().nextDouble() * 1.0,
        y: math.Random().nextDouble() * 1.0,
        size: math.Random().nextDouble() * 2.0 + 1.0,
        blinkDuration: (math.Random().nextDouble() * 2.0 + 3.0) * 3000, // Замедляем мерцание в 3 раза
      ));
    }

    // Инициализируем контроллер анимации для звезд
    _starsController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 15000), // Замедляем анимацию в 3 раза (5000 * 3 = 15000)
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _starsController.dispose();
    super.dispose();
  }

  void _navigateToHyperjump(BuildContext context) {
    Navigator.pushReplacement(
      context,
      PageRouteBuilder(
        pageBuilder: (context, animation, secondaryAnimation) => const HyperjumpAnimationScreen(),
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: child,
          );
        },
        transitionDuration: const Duration(milliseconds: 500),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    // Calculate a size that fits on the screen without scrolling
    final cardHeight = math.min(size.height * 0.65, 450.0); // Further reduced height
    final cardWidth = math.min(size.width * 0.9, 380.0); // Slightly smaller width

    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          // Космический фон
          CosmicBackground(
            starCount: 450,
            minStarSize: 1.0,
            maxStarSize: 3.0,
            animationDuration: const Duration(milliseconds: 8000), // Smoother animations
            enableComet: true,
            backgroundColor: const Color(0xFF000011),
            enableParallax: true,
            parallaxIntensity: 17.5,
            enableAsteroids: true,
            enableSatellites: true,
          ),

          // Основной контент
          Center(
            child: FuturisticContainer(
              width: cardWidth,
              height: cardHeight,
              padding: const EdgeInsets.symmetric(horizontal: 28, vertical: 20),
              borderRadius: BorderRadius.circular(30),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Логотип TMM - centered properly
                  Center(
                    child: TmmPngLogo(
                      size: 140, // Smaller logo
                    ),
                  ).animate().fadeIn(duration: 600.ms).slideY(
                    begin: -0.2,
                    end: 0,
                    curve: Curves.easeOutQuad,
                    duration: 600.ms,
                  ),

                  const SizedBox(height: 16),

                  Center(
                    child: Text(
                      'TMM',
                      style: DesignSystem.headingXL.copyWith(
                        letterSpacing: 2.0,
                        shadows: [
                          Shadow(
                            offset: Offset(0, 2),
                            blurRadius: 4.0,
                            color: Colors.black.withAlpha(150),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ).animate().fadeIn(duration: 600.ms, curve: Curves.easeInOut, delay: 200.ms),

                  const SizedBox(height: 8),

                  Center(
                    child: Text(
                      'Ваш финансовый помощник',
                      style: DesignSystem.headingS.copyWith( // Smaller heading
                        fontWeight: DesignSystem.medium,
                        shadows: [
                          Shadow(
                            offset: Offset(0, 1),
                            blurRadius: 3.0,
                            color: Colors.black.withAlpha(150),
                          ),
                        ],
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ).animate().fadeIn(
                    delay: 400.ms,
                    duration: 600.ms,
                  ),

                  const SizedBox(height: 28), // Reduced space before buttons

                  // Кнопка авторизации
                  Center(
                    child: FuturisticButton(
                      text: 'Login',
                      width: 200, // Smaller width
                      height: 44, // Smaller height
                      isPrimary: true,
                      onPressed: () {
                        Navigator.push(
                          context,
                          PageRouteBuilder(
                            pageBuilder: (context, animation, secondaryAnimation) => const LoginScreen(),
                            transitionsBuilder: (context, animation, secondaryAnimation, child) {
                              return FadeTransition(
                                opacity: animation,
                                child: child,
                              );
                            },
                            transitionDuration: const Duration(milliseconds: 500),
                          ),
                        );
                      },
                    ),
                  ).animate().fadeIn(
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                    delay: 600.ms,
                  ),

                  const SizedBox(height: 12), // Smaller gap

                  // Кнопка регистрации
                  Center(
                    child: FuturisticButton(
                      text: 'Sign Up',
                      width: 200, // Smaller width
                      height: 44, // Smaller height
                      isPrimary: false, // Changed back to secondary style
                      onPressed: () {
                        Navigator.push(
                          context,
                          PageRouteBuilder(
                            pageBuilder: (context, animation, secondaryAnimation) => const RegisterScreen(),
                            transitionsBuilder: (context, animation, secondaryAnimation, child) {
                              return FadeTransition(
                                opacity: animation,
                                child: child,
                              );
                            },
                            transitionDuration: const Duration(milliseconds: 500),
                          ),
                        );
                      },
                    ),
                  ).animate().fadeIn(
                    duration: 600.ms,
                    curve: Curves.easeInOut,
                    delay: 800.ms,
                  ),

                  const SizedBox(height: 8), // Smaller space after buttons

                  // Кнопка "Skip for now"
                  Center(
                    child: TextButton(
                      onPressed: () {
                        _navigateToHyperjump(context);
                      },
                      style: TextButton.styleFrom(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        backgroundColor: Colors.transparent,
                        foregroundColor: DesignSystem.textSecondary,
                      ),
                      child: Text(
                        'Skip for now',
                        style: DesignSystem.bodyS.copyWith( // Smaller text
                          fontWeight: DesignSystem.medium,
                          color: DesignSystem.textSecondary,
                        ),
                      ),
                    ),
                  ).animate().fadeIn(duration: 600.ms, delay: 1000.ms),
                ],
              ),
            ).animate().fadeIn(duration: 800.ms),
          ),
        ],
      ),
    );
  }
}

// Класс для представления звезды
class Star {
  final double x;
  final double y;
  final double size;
  final double blinkDuration;

  Star({
    required this.x,
    required this.y,
    required this.size,
    required this.blinkDuration,
  });
}

// Кастомный painter для отрисовки звезд
class StarsPainter extends CustomPainter {
  final List<Star> stars;
  final double animationValue;

  StarsPainter(this.stars, this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white;

    for (var star in stars) {
      // Вычисляем текущую прозрачность звезды
      final opacity = math.max(0.0, math.min(1.0, 0.3 + (0.7 - 0.3) *
          (math.sin(2 * math.pi * (animationValue + star.x * star.y) % 1) * 0.5 + 0.5)));

      paint.color = Colors.white.withAlpha((opacity * 255).toInt());

      canvas.drawCircle(
        Offset(star.x * size.width, star.y * size.height),
        star.size,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

