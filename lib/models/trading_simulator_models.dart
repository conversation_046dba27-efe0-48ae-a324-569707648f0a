import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'dart:math' as math;

/// Enum representing the different modes of the Crypto Trading Simulator
enum SimulatorMode {
  infinitePatterns,
  practice,
  custom,
}

/// Enum representing the different timeframes available for trading
enum TimeFrame {
  m30('30m', '30 min', Duration(minutes: 30)),
  h1('1h', '1 hour', Duration(hours: 1)),
  h4('4h', '4 hours', Duration(hours: 4)),
  d1('1d', '1 day', Duration(days: 1));

  const TimeFrame(this.apiValue, this.displayName, this.duration);
  final String apiValue;
  final String displayName;
  final Duration duration;
}

/// Enum representing the possible trade actions
enum TradeAction {
  buy(
    color: CupertinoColors.systemGreen,
    label: 'UP',
  ),
  sell(
    color: CupertinoColors.systemRed,
    label: 'DOWN',
  );

  final Color color;
  final String label;

  const TradeAction({
    required this.color,
    required this.label,
  });
}

/// Class representing a candle in a candlestick chart
class CandleData {
  final DateTime time;
  final double open;
  final double high;
  final double low;
  final double close;
  final double volume;

  CandleData({
    required this.time,
    required this.open,
    required this.high,
    required this.low,
    required this.close,
    required this.volume,
  });

  @override
  String toString() {
    return 'CandleData(time: [36m${time.toIso8601String()}[0m, open: $open, high: $high, low: $low, close: $close, volume: $volume)';
  }

  bool get isGreen => close >= open;

  // Timestamp in milliseconds for compatibility with candlesticks package
  DateTime get timestamp => time;

  /// Create a CandleData object from Binance API response
  factory CandleData.fromBinanceData(List<dynamic> data) {
    return CandleData(
      time: DateTime.fromMillisecondsSinceEpoch(data[0] as int),
      open: double.parse(data[1].toString()),
      high: double.parse(data[2].toString()),
      low: double.parse(data[3].toString()),
      close: double.parse(data[4].toString()),
      volume: double.parse(data[5].toString()),
    );
  }

  /// Generate a random candle based on a previous candle
  factory CandleData.generateRandom(
    CandleData previous, {
    double volatilityFactor = 1.0,
    bool? forceTrend,
    math.Random? random,
  }) {
    // Используем переданный random или генерируем случайное число на основе времени
    final double randomValue = random != null 
        ? random.nextDouble() 
        : DateTime.now().millisecondsSinceEpoch % 100 / 100;
    
    final isUptrend = forceTrend ?? (randomValue > 0.5);

    // Base volatility (0.1% to 0.5% normally)
    final baseVolatility = 0.001 + (randomValue * 0.004);
    final appliedVolatility = baseVolatility * volatilityFactor;

    // Calculate new prices
    final basePrice = previous.close;
    final priceChange = basePrice * appliedVolatility * (isUptrend ? 1 : -1);

    // Используем random для генерации других случайных значений
    final randomHighLow = random != null ? random.nextDouble() * 0.002 : randomValue * 0.002;
    final randomVolume = random != null ? 0.8 + random.nextDouble() * 0.4 : 0.8 + randomValue * 0.4;

    final close = basePrice + priceChange;
    final open = previous.close;
    final high = math.max(open, close) * (1 + randomHighLow);
    final low = math.min(open, close) * (1 - randomHighLow);

    return CandleData(
      time: previous.time.add(const Duration(minutes: 30)),
      open: open,
      high: high,
      low: low,
      close: close,
      volume: previous.volume * randomVolume,
    );
  }
}

/// Class representing the result of a trade
class TradingResult {
  final TradeAction action;
  final CandleData entryCandle;
  final CandleData resultCandle;
  final bool isSuccess;
  final double profitAmount;
  final double profitPercentage;
  final double leverageMultiplier;

  TradingResult({
    required this.action,
    required this.entryCandle,
    required this.resultCandle,
    required this.isSuccess,
    required this.profitAmount,
    required this.profitPercentage,
    required this.leverageMultiplier,
  });

  /// Calculate the result of a trade
  factory TradingResult.calculate({
    required TradeAction action,
    required CandleData entryCandle,
    required CandleData resultCandle,
    required double tradeAmount,
    required double leverageMultiplier,
  }) {
    // Determine if the trade was successful
    final isSuccess = (action == TradeAction.buy && resultCandle.close > entryCandle.close) ||
                     (action == TradeAction.sell && resultCandle.close < entryCandle.close);

    // Calculate price change percentage
    final priceChange = (resultCandle.close - entryCandle.close).abs() / entryCandle.close;
    final profitPercentage = priceChange * 100;

    // Calculate profit amount
    final profitAmount = isSuccess
        ? tradeAmount * (profitPercentage / 100) * leverageMultiplier
        : -tradeAmount * (profitPercentage / 100) * leverageMultiplier;

    return TradingResult(
      action: action,
      entryCandle: entryCandle,
      resultCandle: resultCandle,
      isSuccess: isSuccess,
      profitAmount: profitAmount,
      profitPercentage: profitPercentage,
      leverageMultiplier: leverageMultiplier,
    );
  }
}

/// Class representing the statistics for Infinite Patterns mode
class TradingStats {
  int roundsPlayed;
  int wins;
  int losses;
  int deaths;
  int currentWinStreak;
  int maxWinStreak;

  TradingStats({
    this.roundsPlayed = 0,
    this.wins = 0,
    this.losses = 0,
    this.deaths = 0,
    this.currentWinStreak = 0,
    this.maxWinStreak = 0,
  });

  double get winPercentage =>
      roundsPlayed > 0 ? (wins / roundsPlayed) * 100 : 0;

  void addResult(bool isWin) {
    roundsPlayed++;

    if (isWin) {
      wins++;
      currentWinStreak++;
      if (currentWinStreak > maxWinStreak) {
        maxWinStreak = currentWinStreak;
      }
    } else {
      losses++;
      currentWinStreak = 0;
    }
  }

  void addDeath() {
    deaths++;
  }

  void reset() {
    roundsPlayed = 0;
    wins = 0;
    losses = 0;
    currentWinStreak = 0;
    // Keep deaths and maxWinStreak for the session
  }
}
