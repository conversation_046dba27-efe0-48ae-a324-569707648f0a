import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'fomo_psychological_model.dart';

/// Enum representing the different trader roles in the Anti FOMO Simulator
enum TraderRole {
  novice(
    'Novice Trader',
    'Small deposit (\$1,000), high leverage (10x), high risk, high potential returns',
    Colors.teal,
    1000.0,
    10.0,
    Icons.flight_takeoff,
  ),
  whale(
    'Whale',
    'Large capital (\$100,000), low leverage (2x), market impact, slower moves',
    Colors.blue,
    100000.0,
    2.0,
    Icons.water,
  ),
  darkpool(
    'Darkpool Trader',
    'Medium capital (\$10,000), medium leverage (5x), access to potentially misleading insider information',
    Colors.purple,
    10000.0,
    5.0,
    Icons.visibility_off,
  );

  const TraderRole(
    this.displayName,
    this.description,
    this.color,
    this.initialBalance,
    this.leverageMultiplier,
    this.icon,
  );

  final String displayName;
  final String description;
  final Color color;
  final double initialBalance;
  final double leverageMultiplier;
  final IconData icon;
}

/// Enum representing the different difficulty levels in the Anti FOMO Simulator
enum DifficultyLevel {
  easy(
    'Easy Mode',
    '30-second decision timer, lower risk multipliers, more forgiving losses',
    Colors.green,
    30,
    1.0,
  ),
  hard(
    'Hard Mode',
    '15-second decision timer, higher risk multipliers, more severe losses',
    Colors.orange,
    15,
    1.5,
  ),
  pro(
    'Pro Mode',
    '5-second decision timer, double losses compared to Hard mode',
    Colors.black,
    5,
    2.0,
  );

  const DifficultyLevel(
    this.displayName,
    this.description,
    this.color,
    this.timerDuration,
    this.riskMultiplier,
  );

  final String displayName;
  final String description;
  final Color color;
  final int timerDuration;
  final double riskMultiplier;
}

/// Enum representing the possible trade actions in the Anti FOMO Simulator
enum FomoTradeAction {
  buy(Icons.trending_up, 'BUY', Colors.green),
  sell(Icons.trending_down, 'SELL', Colors.red),
  hold(Icons.pause, 'HOLD', Colors.blue);

  const FomoTradeAction(this.icon, this.label, this.color);
  final IconData icon;
  final String label;
  final Color color;
}

/// Enum for different types of market scenarios
enum MarketScenario {
  normal,     // Standard market conditions
  volatile,   // High volatility market
  trending,   // Strong directional movement
  rangebound, // Price moving in a sideways range
  newsShock,  // Major news affecting the market
  flashCrash, // Sudden sharp decline
  reversal,   // Trend reversal
  breakout,   // Breaking out of a range
  distribution // Distribution phase
}

/// Class representing a social media message in the Anti FOMO Simulator
class ChatMessage {
  final String username;
  final String message;
  final DateTime timestamp;
  final bool isPositive;
  final int likes;

  const ChatMessage({
    required this.username,
    required this.message,
    required this.timestamp,
    required this.isPositive,
    required this.likes,
  });

  /// Generate a random positive message
  static String generatePositiveMessage(int hypeLevel) {
    final messages = [
      'This is just the beginning. FOMO kicking in!',
      'Just bought more! To the moon! 🚀',
      'Whales are accumulating, look at the orderbook!',
      'The technicals have never looked better!',
      'I\'m not selling until we hit 6 figures',
      'This is a multi-year opportunity we\'re seeing',
      'People still don\'t understand how early we are',
      'If you\'re not buying now, when will you buy?',
      'Diamond hands will be rewarded! 💎👐',
      'I just mortgaged my house to buy more',
      'The charts look incredibly bullish right now',
      'HOLD strong! Don\'t give your coins to the whales',
      'This breakout is just getting started',
      'I can see this pumping 5x from here easily',
      'Institutional adoption is accelerating',
      'This is the opportunity of a lifetime',
      'The fundamentals are stronger than ever',
      'I\'m feeling extremely bullish today',
      'This dip is the last chance to buy cheap',
      'Smart money is buying right now',
    ];

    return _selectRandomMessage(messages);
  }

  /// Generate a thematic positive message based on special market conditions
  static String generateThematicPositiveMessage(String theme, int hypeLevel) {
    Map<String, List<String>> thematicMessages = {
      'volatility': [
        'This volatility is a HUGE opportunity for traders! Buy!',
        'Whales creating volatility to accumulate cheaper shares!',
        'Volatility = opportunity! Look at these price swings!',
        'This shakeout is getting rid of weak hands. We go HIGHER from here!',
        'Volatility is the price we pay for outsized returns!',
      ],
      'breakout': [
        'MAJOR BREAKOUT CONFIRMED! Price target +50%!',
        'We just broke key resistance, moon mission starting NOW!',
        'This breakout is the real deal - massive volume coming in!',
        'Beautiful technical breakout forming - BUY signal confirmed!',
        'Perfect cup and handle breakout forming - massive upside incoming!',
      ],
      'regulation': [
        'New regulations will legitimize the market - BULLISH!',
        'Regulatory clarity is exactly what institutions needed to enter!',
        'Regulations will kill scam projects and boost legitimate ones like this!',
        'Government can\'t stop this - bullish regardless of regs!',
        'Smart money already positioned ahead of regulatory approval!',
      ],
      'earnings': [
        'Earnings coming in HOT! Buy before the news hits!',
        'Insiders know earnings will CRUSH estimates. Look at this buy pressure!',
        'Pre-earnings accumulation phase happening right now!',
        'Earnings will send this to ALL TIME HIGHS! Get in now!',
        'The smart money knows earnings will beat - they\'re buying now!',
      ],
      'accumulation': [
        'Classic accumulation pattern - whales loading up!',
        'Smart money accumulating before retail catches on!',
        'Watching the order flow - HUGE accumulation happening!',
        'Institutions quietly accumulating before the next leg up!',
        'This sideways price action is accumulation before takeoff!',
      ],
      'distribution': [
        'This isn\'t distribution - it\'s re-accumulation before higher highs!',
        'Don\'t be fooled by FUD about distribution - whales are BUYING!',
        'What looks like distribution is actually smart money buying!',
        'FUDsters calling this distribution to shake out weak hands!',
        'This consolidation is healthy before the next pump!',
      ],
      'capitulation': [
        'Final capitulation before the reversal - BUY THE FEAR!',
        'This is the capitulation event we\'ve been waiting for! Max opportunity!',
        'Weak hands capitulating, smart money BUYING!',
        'Perfect capitulation setup - historically the BEST time to buy!',
        'Market capitulation = generational buying opportunity!',
      ],
    };

    // If theme exists, use those messages, otherwise fall back to standard positive
    if (thematicMessages.containsKey(theme)) {
      return _selectRandomMessage(thematicMessages[theme]!);
    } else {
      return generatePositiveMessage(hypeLevel);
    }
  }
  
  /// Generate a random negative message
  static String generateNegativeMessage(int hypeLevel) {
    final messages = [
      'I\'m out. This looks like a bull trap.',
      'Selling before the major dump incoming',
      'The technicals look terrible, clear downtrend',
      'Whales are distributing, check the on-chain data',
      'Dead cat bounce before lower lows',
      'Don\'t catch falling knives, wait for confirmation',
      'This pattern has played out many times before - DOWN',
      'Smart money is exiting - follow them or get rekt',
      'The volume doesn\'t support this move up',
      'Higher time frames still bearish, don\'t be fooled',
      'Market structure is broken, exit while you can',
      'This rally is running out of steam, selling here',
      'Taking profits now, risk/reward not worth it',
      'Institutions are shorting at these levels',
      'Hope is not a strategy - protect your capital',
      'I\'ve seen this movie before, it doesn\'t end well',
      'This is going lower, weekly trend is still down',
      'Resistance levels will not be broken this time',
      'The macro environment doesn\'t support higher prices',
      'Exit while there\'s still liquidity',
    ];

    return _selectRandomMessage(messages);
  }
  
  /// Generate a thematic negative message based on special market conditions
  static String generateThematicNegativeMessage(String theme, int hypeLevel) {
    Map<String, List<String>> thematicMessages = {
      'volatility': [
        'This volatility is killing my trades! Getting out NOW!',
        'No one can trade this chop - staying in cash until it settles',
        'Insane volatility = market uncertainty. SELL!',
        'This is panic volatility! Markets in disarray!',
        'Volatility spiking = risk OFF. Protect your capital!',
      ],
      'breakout': [
        'This breakout attempt will fail like the last 3 times',
        'Fake breakout - this is a bull trap before the dump',
        'Don\'t fall for this false breakout pattern!',
        'Another failed breakout incoming - shorting this',
        'No volume on this breakout - complete fake out!',
      ],
      'regulation': [
        'Regulatory CRACKDOWN incoming! Exit before the news!',
        'Insiders know bad regulations are coming - they\'re selling now!',
        'Regulations will CRUSH this market. Get out while you can!',
        'Compliance costs will destroy profitability - SELL!',
        'Government regulation will kill innovation in this space!',
      ],
      'earnings': [
        'Earnings will massively disappoint - smart money SELLING!',
        'Insider selling before terrible earnings report!',
        'Market doesn\'t realize how bad earnings will be!',
        'Exit before post-earnings crash! Warning signs everywhere!',
        'Pre-earnings dump happening now - avoid the bloodbath!',
      ],
      'accumulation': [
        'What people think is accumulation is actually distribution!',
        'This isn\'t accumulation - it\'s the calm before the crash!',
        'Falling for the accumulation narrative will DESTROY your portfolio!',
        'Don\'t be fooled by "accumulation" - whales are EXITING!',
        'This sideways action isn\'t accumulation - it\'s distribution!',
      ],
      'distribution': [
        'Classic distribution pattern forming - whales EXITING!',
        'Textbook distribution phase - dump incoming!',
        'Smart money distributing their bags to retail!',
        'Final distribution before the crash - GET OUT NOW!',
        'Institutions distributing to unsuspecting retail!',
      ],
      'capitulation': [
        'This isn\'t even close to capitulation - MUCH lower prices coming!',
        'Real capitulation will be 50% lower from here!',
        'Capitulation phase just beginning - protect your capital!',
        'Market hasn\'t capitulated yet - worst is still ahead!',
        'Don\'t try to catch this falling knife - true capitulation coming!',
      ],
    };

    // If theme exists, use those messages, otherwise fall back to standard negative
    if (thematicMessages.containsKey(theme)) {
      return _selectRandomMessage(thematicMessages[theme]!);
    } else {
      return generateNegativeMessage(hypeLevel);
    }
  }

  /// Select a random message from the list
  static String _selectRandomMessage(List<String> messages) {
    final random = math.Random();
    final index = random.nextInt(messages.length);
    return messages[index];
  }

  /// Generate a random username for chat messages
  static String generateRandomUsername() {
    final usernames = [
      'CryptoKing', 'MoonShot', 'DiamondHands', 'SatoshiLite', 'HODLer',
      'CoinMaster', 'Whale_Alert', 'BullMarket', 'ChartWizard', 'TokenGuru',
      'DeFiMaster', 'BlockchainBaron', 'CryptoPundit', 'TradingView', 'AlphaSeeker',
      'TokenTrader', 'NFT_Collector', 'GasFeeVictim', 'MetaverseMogul', 'AltcoinAddict',
      'BearWhisperer', 'CoinCollector', 'DCAMaster', 'EthTrader', 'FibonacciWizard',
      'BTFD_King', 'LeverageLord', 'LiquidityHunter', 'TechnicalTrader', 'StableCoinStacker',
      'TMM_Investor', 'TMM_Trader', 'TMM_Guru', 'TMM_Whale', 'TMM_Master',
      'TMM_Alpha', 'TMM_Beta', 'TMM_Wizard', 'TMM_Bear', 'TMM_Legend',
      'TMM_General', 'TMM_Analyst', 'TMM_Lion', 'TMM_Tiger', 'TMM_Shark',
      'RippleRider', 'GenesisBlock', 'CardanoKing', 'ChainLink_Marine', 'CandlestickCarol',
      'TradeNinja', 'MetaverseMonarch', 'AnchorAce', 'SolanaSeeker', 'PolkadotPioneer',
    ];

    final random = math.Random();
    final index = random.nextInt(usernames.length);
    return usernames[index];
  }
}

/// Class representing a news ticker item in the Anti FOMO Simulator
class TickerNewsItem {
  final String text;
  final bool isSpecial;
  final DateTime timestamp;

  TickerNewsItem({
    required this.text,
    this.isSpecial = false,
    required this.timestamp,
  });

  /// Generate a random news item
  static TickerNewsItem generateRandom({bool forceSpecial = false}) {
    final regularNews = [
      'Bitcoin trading volume reaches new monthly high',
      'Ethereum gas fees drop to lowest level in weeks',
      'Major exchange announces new token listing',
      'Institutional investors increase crypto holdings',
      'New DeFi protocol launches with high APY',
      'Regulatory clarity expected in coming months',
      'Mining difficulty adjusts upward by 3.2%',
      'Altcoin season indicators show mixed signals',
      'Futures open interest grows across major exchanges',
      'Technical indicators suggest market indecision',
      'Analysts divided on next market direction',
      'Bitcoin dominance drops below key level',
      'Meme tokens gaining traction on social media',
      'Layer-2 solutions see increased adoption',
      'NFT market showing signs of recovery',
      'New staking platform offers competitive yields',
      'Whale addresses accumulating mid-cap altcoins',
      'Network activity metrics reach quarterly high',
      'Cross-chain bridges report record transaction volume',
      'DEX volumes outpace centralized exchanges',
      'Major VC firm announces new crypto investment fund',
      'Bitcoin mining hashrate hits all-time high',
      'Trading experts predict sideways action ahead',
      'Exchange reserves continue downward trend',
      'On-chain metrics show accumulation phase',
      'Smart money flow index turns slightly positive',
      'Regional adoption growing in emerging markets',
      'Top developer leaves project to start new venture',
      'Governance proposal passes with overwhelming support',
      'Market sentiment index remains in neutral zone',
    ];

    final specialNews = [
      'BREAKING: Major country announces Bitcoin adoption!',
      'URGENT: Exchange hack reported, markets reacting!',
      'ALERT: Central bank announces new crypto regulations!',
      'FLASH: Major institutional player enters crypto market!',
      'CRITICAL: Network upgrade causing price volatility!',
      'BREAKING: Software vulnerability discovered in major protocol!',
      'URGENT: Regulatory crackdown announced in key market!',
      'ALERT: Major exchange halts withdrawals without warning!',
      'FLASH: Top project founder sells entire token holdings!',
      'CRITICAL: Stablecoin loses peg amid liquidity concerns!',
      'BREAKING: Unexpected hard fork announced for top blockchain!',
      'URGENT: Central bank digital currency pilot launching next week!',
      'ALERT: SEC files charges against prominent crypto company!',
      'FLASH: Major fund announces 10% portfolio allocation to crypto!',
      'CRITICAL: Market-wide liquidation cascade begins!',
    ];

    final isSpecial = forceSpecial || math.Random().nextDouble() < 0.15;
    final messagePool = isSpecial ? specialNews : regularNews;
    
    // Use math.Random for better randomness
    final random = math.Random().nextInt(messagePool.length);
    return TickerNewsItem(
      text: messagePool[random],
      isSpecial: isSpecial,
      timestamp: DateTime.now(),
    );
  }
}

/// Class representing the social hype meter in the Anti FOMO Simulator
class SocialHype {
  int level; // 0-100

  SocialHype({this.level = 50});

  /// Update the hype level based on scenario interest and add randomness
  void update(int scenarioInterest) {
    final baseHype = level * 0.6; // Reduce previous hype influence
    final scenarioInfluence = scenarioInterest * 1.2; // Increase scenario influence
    final randomFactor = math.Random().nextInt(10); // Random element

    level = math.min(100, math.max(0, (baseHype + scenarioInfluence + randomFactor).toInt()));
  }

  Color get color {
    if (level < 20) {
      return Colors.blue;
    } else if (level < 40) {
      return Colors.green;
    } else if (level < 60) {
      return Colors.yellow;
    } else if (level < 80) {
      return Colors.orange;
    } else {
      return Colors.red;
    }
  }
}
