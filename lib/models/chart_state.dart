import 'candle.dart';

/// Класс для хранения состояния графика между раундами
class ChartState {
  /// Все свечи (250)
  final List<Candle> allCandles;
  
  /// Индекс последней видимой свечи (обычно 242)
  final int lastVisibleIndex;
  
  /// Цена точки входа
  final double entryPrice;
  
  /// Время точки входа
  final int entryTime;
  
  /// Конструктор
  ChartState({
    required this.allCandles,
    required this.lastVisibleIndex,
    required this.entryPrice,
    required this.entryTime,
  });
  
  /// Создать копию с новыми значениями
  ChartState copyWith({
    List<Candle>? allCandles,
    int? lastVisibleIndex,
    double? entryPrice,
    int? entryTime,
  }) {
    return ChartState(
      allCandles: allCandles ?? this.allCandles,
      lastVisibleIndex: lastVisibleIndex ?? this.lastVisibleIndex,
      entryPrice: entryPrice ?? this.entryPrice,
      entryTime: entryTime ?? this.entryTime,
    );
  }
}
