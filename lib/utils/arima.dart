import 'dart:math';
import 'package:flutter/foundation.dart';

/// ARIMA (AutoRegressive Integrated Moving Average) model implementation
///
/// This class implements a simplified ARIMA model for time series forecasting.
/// ARIMA consists of three components:
/// - AR (AutoRegressive): Uses past values for prediction
/// - I (Integrated): Removes trend through differencing
/// - MA (Moving Average): Uses past errors for prediction
class ArimaModel {
  // ARIMA parameters
  final int p; // AR order
  final int d; // Differencing order
  final int q; // MA order

  // Model coefficients
  List<double> _arCoefficients = []; // AR coefficients (phi)
  List<double> _maCoefficients = []; // MA coefficients (theta)
  double _intercept = 0.0; // Intercept term (c)

  // Historical data
  List<double> _originalSeries = [];
  List<List<double>> _differencedSeries = [];
  List<double> _errors = []; // Residuals for MA component

  // Model state
  bool _isTrained = false;

  /// Creates a new ARIMA model with specified parameters
  ///
  /// [p] is the order of the AR component
  /// [d] is the order of differencing
  /// [q] is the order of the MA component
  ArimaModel({
    required this.p,
    required this.d,
    required this.q,
  }) {
    // Initialize coefficients with default values
    _arCoefficients = List.filled(p, 0.0);
    _maCoefficients = List.filled(q, 0.0);
  }

  /// Creates a new ARIMA model with pre-defined coefficients
  ///
  /// This is useful when you want to use coefficients determined externally
  /// or when loading a previously trained model
  ArimaModel.withCoefficients({
    required this.p,
    required this.d,
    required this.q,
    required List<double> arCoefficients,
    required List<double> maCoefficients,
    required double intercept,
  }) {
    if (arCoefficients.length != p) {
      throw ArgumentError('AR coefficients length must match p');
    }
    if (maCoefficients.length != q) {
      throw ArgumentError('MA coefficients length must match q');
    }

    _arCoefficients = List.from(arCoefficients);
    _maCoefficients = List.from(maCoefficients);
    _intercept = intercept;
    _isTrained = true;
  }

  /// Creates an ARIMA(1,1,1) model with default coefficients
  ///
  /// This is a common ARIMA configuration that works well for many time series
  factory ArimaModel.arima111() {
    return ArimaModel.withCoefficients(
      p: 1,
      d: 1,
      q: 1,
      arCoefficients: [0.7], // Default AR coefficient
      maCoefficients: [0.3], // Default MA coefficient
      intercept: 0.0,
    );
  }

  /// Apply differencing to the time series
  ///
  /// Differencing helps remove trends from the data
  List<List<double>> _difference(List<double> series, int d) {
    if (d <= 0) return [List.from(series)];

    List<List<double>> result = [List.from(series)];

    for (int i = 0; i < d; i++) {
      List<double> diff = [];
      List<double> prevSeries = result.last;

      for (int j = 1; j < prevSeries.length; j++) {
        diff.add(prevSeries[j] - prevSeries[j - 1]);
      }

      result.add(diff);
    }

    return result;
  }

  /// Reverse the differencing to get the original scale
  double _undifference(List<List<double>> differencedSeries, List<double> forecasts) {
    // Start with the forecast of the differenced series
    double result = forecasts.last;

    // Work backwards through the differencing levels
    for (int i = differencedSeries.length - 2; i >= 0; i--) {
      result += differencedSeries[i].last;
    }

    return result;
  }

  /// Train the ARIMA model on historical data
  void train(List<double> timeSeries) {
    if (timeSeries.length <= p + d + q) {
      throw ArgumentError('Time series length must be greater than p + d + q');
    }

    _originalSeries = List.from(timeSeries);
    _differencedSeries = _difference(_originalSeries, d);

    // Use the most differenced series for AR and MA modeling
    List<double> workingSeries = _differencedSeries.last;

    // Simple estimation of AR coefficients using Yule-Walker equations
    // This is a simplified approach - a full implementation would use more sophisticated methods
    if (p > 0) {
      _estimateArCoefficients(workingSeries);
    }

    // Calculate residuals (errors)
    _errors = _calculateResiduals(workingSeries);

    // Simple estimation of MA coefficients
    // This is a simplified approach - a full implementation would use more sophisticated methods
    if (q > 0) {
      _estimateMaCoefficients(_errors);
    }

    _isTrained = true;
  }

  /// Estimate AR coefficients using a simplified approach
  void _estimateArCoefficients(List<double> series) {
    // For ARIMA(1,d,q), use lag-1 autocorrelation as the AR coefficient
    if (p == 1) {
      double sumProduct = 0;
      double sumSquared = 0;

      for (int i = 1; i < series.length; i++) {
        sumProduct += series[i] * series[i - 1];
        sumSquared += series[i - 1] * series[i - 1];
      }

      _arCoefficients[0] = sumSquared > 0 ? (sumProduct / sumSquared).clamp(-0.99, 0.99) : 0.7;
    } else {
      // For higher-order AR models, use default values
      // A full implementation would use the Yule-Walker equations or Levinson-Durbin algorithm
      for (int i = 0; i < p; i++) {
        _arCoefficients[i] = 0.7 / (i + 1);
      }
    }
  }

  /// Estimate MA coefficients using a simplified approach
  void _estimateMaCoefficients(List<double> errors) {
    // For ARIMA(p,d,1), use a simple correlation between errors
    if (q == 1) {
      double sumProduct = 0;
      double sumSquared = 0;

      for (int i = 1; i < errors.length; i++) {
        sumProduct += errors[i] * errors[i - 1];
        sumSquared += errors[i - 1] * errors[i - 1];
      }

      _maCoefficients[0] = sumSquared > 0 ? (sumProduct / sumSquared).clamp(-0.99, 0.99) : 0.3;
    } else {
      // For higher-order MA models, use default values
      // A full implementation would use more sophisticated methods
      for (int i = 0; i < q; i++) {
        _maCoefficients[i] = 0.3 / (i + 1);
      }
    }
  }

  /// Calculate residuals (errors) for the MA component
  List<double> _calculateResiduals(List<double> series) {
    List<double> residuals = List.filled(series.length, 0);

    // For the first p values, we can't calculate AR component, so residuals are just the values
    for (int i = 0; i < p; i++) {
      residuals[i] = series[i];
    }

    // For the rest, calculate residuals as actual - predicted
    for (int i = p; i < series.length; i++) {
      double arComponent = _intercept;

      // Add AR component
      for (int j = 0; j < p; j++) {
        if (i - j - 1 >= 0) {
          arComponent += _arCoefficients[j] * series[i - j - 1];
        }
      }

      // Add MA component (if we have enough history)
      double maComponent = 0;
      for (int j = 0; j < q; j++) {
        if (i - j - 1 >= 0) {
          maComponent += _maCoefficients[j] * residuals[i - j - 1];
        }
      }

      // Calculate residual
      residuals[i] = series[i] - (arComponent + maComponent);
    }

    return residuals;
  }

  /// Forecast future values
  ///
  /// [steps] is the number of steps to forecast
  List<double> forecast(int steps) {
    if (!_isTrained) {
      debugPrint('ARIMA: Model must be trained before forecasting');
      // Return a simple trend forecast instead of throwing an error
      return _simpleTrendForecast(steps);
    }

    if (steps <= 0) {
      debugPrint('ARIMA: Steps must be positive');
      return [];
    }

    try {
      debugPrint('ARIMA: Starting forecast for $steps steps');
      debugPrint('ARIMA: Original series length: ${_originalSeries.length}');

      if (_originalSeries.length < 3) {
        debugPrint('ARIMA: Not enough data points for proper forecasting, using simple trend');
        return _simpleTrendForecast(steps);
      }

      // Get the differenced series we're working with
      List<double> workingSeries = _differencedSeries.last;
      debugPrint('ARIMA: Differenced series length: ${workingSeries.length}');

      // Prepare forecasts array
      List<double> forecasts = [];

      // Create a copy of the errors array for forecasting
      List<double> forecastErrors = List.from(_errors);

      // For each forecast step
      for (int step = 0; step < steps; step++) {
        // Start with the intercept
        double forecast = _intercept;

        // Add AR component
        for (int i = 0; i < p; i++) {
          int idx = workingSeries.length - 1 - i + forecasts.length;
          if (idx >= workingSeries.length) {
            // Use previously forecasted values
            forecast += _arCoefficients[i] * forecasts[idx - workingSeries.length];
          } else if (idx >= 0) {
            // Use actual values from history
            forecast += _arCoefficients[i] * workingSeries[idx];
          }
        }

        // Add MA component
        for (int i = 0; i < q; i++) {
          int idx = forecastErrors.length - 1 - i;
          if (idx >= 0) {
            forecast += _maCoefficients[i] * forecastErrors[idx];
          }
        }

        // Add the forecast to our list
        forecasts.add(forecast);

        // For future MA components, assume error is 0
        forecastErrors.add(0);
      }

      // Convert the differenced forecasts back to the original scale
      List<double> undifferencedForecasts = [];

      for (int i = 0; i < forecasts.length; i++) {
        // Create a temporary list with the current forecast
        List<double> tempForecasts = forecasts.sublist(0, i + 1);

        // Undifference to get the forecast in the original scale
        double undifferencedForecast = _undifference(_differencedSeries, tempForecasts);
        undifferencedForecasts.add(undifferencedForecast);
      }

      debugPrint('ARIMA: Forecast completed successfully');
      debugPrint('ARIMA: Forecasts: $undifferencedForecasts');

      return undifferencedForecasts;
    } catch (e) {
      debugPrint('ARIMA: Error during forecasting: $e');
      return _simpleTrendForecast(steps);
    }
  }

  /// Generate a simple trend forecast when ARIMA can't be used
  List<double> _simpleTrendForecast(int steps) {
    debugPrint('ARIMA: Using simple trend forecast');

    if (_originalSeries.isEmpty) {
      debugPrint('ARIMA: No historical data, returning default values');
      return List.filled(steps, 50.0); // Default neutral value
    }

    // Use the last value as base
    double lastValue = _originalSeries.last;
    debugPrint('ARIMA: Last value: $lastValue');

    // Calculate a simple trend if we have at least 2 points
    double trend = 0.0;
    if (_originalSeries.length >= 2) {
      // Use the last few points to calculate trend
      int startIdx = max(0, _originalSeries.length - 5);
      double startValue = _originalSeries[startIdx];
      double endValue = _originalSeries.last;
      int periods = _originalSeries.length - 1 - startIdx;

      if (periods > 0) {
        trend = (endValue - startValue) / periods;
      }

      debugPrint('ARIMA: Calculated trend: $trend per period');
    }

    // Generate forecasts with the trend
    List<double> forecasts = [];
    for (int i = 0; i < steps; i++) {
      double forecast = lastValue + trend * (i + 1);
      // Ensure forecast is within valid range
      forecast = forecast.clamp(0.0, 100.0);
      forecasts.add(forecast);
    }

    debugPrint('ARIMA: Simple trend forecasts: $forecasts');
    return forecasts;
  }

  /// Get the model parameters as a map
  Map<String, dynamic> getParameters() {
    return {
      'p': p,
      'd': d,
      'q': q,
      'ar_coefficients': _arCoefficients,
      'ma_coefficients': _maCoefficients,
      'intercept': _intercept,
    };
  }

  /// Check if the model is trained
  bool get isTrained => _isTrained;
}
