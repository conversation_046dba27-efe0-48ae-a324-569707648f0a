import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Улучшенные анимации для современной навигационной панели
class EnhancedNavigationAnimations {
  
  /// Создает анимацию пульсации для активной кнопки
  static Widget createPulseAnimation({
    required Widget child,
    required bool isActive,
    required AnimationController controller,
  }) {
    final pulseAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.easeInOut,
    ));

    return AnimatedBuilder(
      animation: pulseAnimation,
      builder: (context, _) {
        return Transform.scale(
          scale: isActive ? pulseAnimation.value : 1.0,
          child: child,
        );
      },
    );
  }

  /// Создает анимацию свечения для активной кнопки
  static Widget createGlowEffect({
    required Widget child,
    required bool isActive,
    required Color glowColor,
    double glowRadius = 20.0,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(25),
        boxShadow: isActive
            ? [
                BoxShadow(
                  color: glowColor.withOpacity(0.3),
                  blurRadius: glowRadius,
                  spreadRadius: 2,
                ),
                BoxShadow(
                  color: glowColor.withOpacity(0.1),
                  blurRadius: glowRadius * 1.5,
                  spreadRadius: 4,
                ),
              ]
            : null,
      ),
      child: child,
    );
  }

  /// Создает анимацию морфинга иконки
  static Widget createIconMorph({
    required IconData activeIcon,
    required IconData inactiveIcon,
    required bool isActive,
    required Color color,
    double size = 24.0,
    Duration duration = const Duration(milliseconds: 250),
  }) {
    return AnimatedSwitcher(
      duration: duration,
      transitionBuilder: (child, animation) {
        return ScaleTransition(
          scale: animation,
          child: RotationTransition(
            turns: Tween<double>(begin: 0.8, end: 1.0).animate(animation),
            child: child,
          ),
        );
      },
      child: Icon(
        isActive ? activeIcon : inactiveIcon,
        key: ValueKey(isActive),
        color: color,
        size: size,
      ),
    );
  }

  /// Создает анимацию появления текста
  static Widget createTextReveal({
    required String text,
    required bool isVisible,
    required TextStyle style,
    Duration duration = const Duration(milliseconds: 300),
  }) {
    return AnimatedSwitcher(
      duration: duration,
      transitionBuilder: (child, animation) {
        return SlideTransition(
          position: Tween<Offset>(
            begin: const Offset(0, 0.5),
            end: Offset.zero,
          ).animate(CurvedAnimation(
            parent: animation,
            curve: Curves.easeOutCubic,
          )),
          child: FadeTransition(
            opacity: animation,
            child: child,
          ),
        );
      },
      child: isVisible
          ? Text(
              text,
              key: ValueKey(text),
              style: style,
            )
          : const SizedBox.shrink(),
    );
  }

  /// Создает анимацию волны при нажатии
  static Widget createRippleEffect({
    required Widget child,
    required VoidCallback onTap,
    Color? rippleColor,
    double radius = 25.0,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          HapticFeedback.lightImpact();
          onTap();
        },
        borderRadius: BorderRadius.circular(radius),
        splashColor: rippleColor?.withOpacity(0.2) ?? Colors.blue.withOpacity(0.2),
        highlightColor: rippleColor?.withOpacity(0.1) ?? Colors.blue.withOpacity(0.1),
        child: child,
      ),
    );
  }

  /// Создает анимацию подпрыгивания
  static Widget createBounceAnimation({
    required Widget child,
    required AnimationController controller,
    double bounceHeight = 0.1,
  }) {
    final bounceAnimation = Tween<double>(
      begin: 0.0,
      end: bounceHeight,
    ).animate(CurvedAnimation(
      parent: controller,
      curve: Curves.elasticOut,
    ));

    return AnimatedBuilder(
      animation: bounceAnimation,
      builder: (context, _) {
        return Transform.translate(
          offset: Offset(0, -bounceAnimation.value * 10),
          child: child,
        );
      },
    );
  }

  /// Создает анимацию градиентного фона
  static Widget createGradientBackground({
    required Widget child,
    required bool isActive,
    required List<Color> activeGradient,
    required List<Color> inactiveGradient,
    Duration duration = const Duration(milliseconds: 400),
  }) {
    return AnimatedContainer(
      duration: duration,
      curve: Curves.easeInOut,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isActive ? activeGradient : inactiveGradient,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: child,
    );
  }

  /// Создает анимацию частиц при активации
  static Widget createParticleEffect({
    required Widget child,
    required bool isActive,
    required Color particleColor,
  }) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        child,
        if (isActive) ...[
          Positioned(
            top: -5,
            right: -5,
            child: AnimatedOpacity(
              duration: const Duration(milliseconds: 500),
              opacity: isActive ? 1.0 : 0.0,
              child: Container(
                width: 8,
                height: 8,
                decoration: BoxDecoration(
                  color: particleColor,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: particleColor.withOpacity(0.5),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ],
    );
  }

  /// Создает анимацию дыхания (breathing effect)
  static Widget createBreathingEffect({
    required Widget child,
    required bool isActive,
    Duration duration = const Duration(milliseconds: 2000),
  }) {
    return TweenAnimationBuilder<double>(
      tween: Tween<double>(begin: 1.0, end: isActive ? 1.02 : 1.0),
      duration: duration,
      curve: Curves.easeInOut,
      builder: (context, scale, _) {
        return Transform.scale(
          scale: scale,
          child: child,
        );
      },
    );
  }

  /// Создает анимацию магнитного притяжения
  static Widget createMagneticEffect({
    required Widget child,
    required bool isHovered,
    double magneticStrength = 1.1,
  }) {
    return AnimatedScale(
      scale: isHovered ? magneticStrength : 1.0,
      duration: const Duration(milliseconds: 200),
      curve: Curves.easeOutBack,
      child: child,
    );
  }

  /// Создает анимацию неонового свечения
  static Widget createNeonGlow({
    required Widget child,
    required bool isActive,
    required Color neonColor,
    double intensity = 1.0,
  }) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20),
        boxShadow: isActive
            ? [
                BoxShadow(
                  color: neonColor.withOpacity(0.5 * intensity),
                  blurRadius: 20,
                  spreadRadius: 2,
                ),
                BoxShadow(
                  color: neonColor.withOpacity(0.3 * intensity),
                  blurRadius: 40,
                  spreadRadius: 4,
                ),
                BoxShadow(
                  color: neonColor.withOpacity(0.1 * intensity),
                  blurRadius: 60,
                  spreadRadius: 8,
                ),
              ]
            : null,
      ),
      child: child,
    );
  }

  /// Создает анимацию жидкого морфинга
  static Widget createLiquidMorph({
    required Widget child,
    required bool isActive,
    Duration duration = const Duration(milliseconds: 600),
  }) {
    return AnimatedContainer(
      duration: duration,
      curve: Curves.elasticOut,
      transform: Matrix4.identity()
        ..scale(isActive ? 1.05 : 1.0)
        ..rotateZ(isActive ? 0.02 : 0.0),
      child: child,
    );
  }
}

/// Кастомные кривые анимации для навигации
class NavigationCurves {
  static const Curve smoothEntry = Cubic(0.25, 0.46, 0.45, 0.94);
  static const Curve smoothExit = Cubic(0.55, 0.06, 0.68, 0.19);
  static const Curve elasticEntry = Cubic(0.68, -0.55, 0.265, 1.55);
  static const Curve magneticPull = Cubic(0.17, 0.67, 0.83, 0.67);
  static const Curve liquidMotion = Cubic(0.25, 0.46, 0.45, 0.94);
}

/// Константы для анимаций
class NavigationAnimationConstants {
  static const Duration fastTransition = Duration(milliseconds: 200);
  static const Duration normalTransition = Duration(milliseconds: 300);
  static const Duration slowTransition = Duration(milliseconds: 500);
  static const Duration breathingDuration = Duration(milliseconds: 2000);
  
  static const double defaultScale = 1.0;
  static const double activeScale = 1.1;
  static const double hoverScale = 1.05;
  
  static const double defaultOpacity = 0.7;
  static const double activeOpacity = 1.0;
  static const double inactiveOpacity = 0.5;
} 