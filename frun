#!/bin/bash

# Функция для проверки наличия Flutter
check_flutter() {
    if ! command -v flutter &> /dev/null; then
        echo "Ошибка: Flutter не найден. Убедитесь, что Flutter установлен и добавлен в PATH."
        exit 1
    fi
}

# Функция для проверки, находимся ли мы в директории проекта Flutter
check_flutter_project() {
    if [ ! -f "pubspec.yaml" ]; then
        echo "Ошибка: pubspec.yaml не найден. Убедитесь, что вы находитесь в директории проекта Flutter."
        exit 1
    fi
}

# Функция для запуска Flutter
run_flutter() {
    echo "Запуск Flutter приложения..."
    flutter run "$@"
}

# Основная функция
main() {
    check_flutter
    check_flutter_project
    run_flutter "$@"
}

# Запуск основной функции с передачей всех аргументов
main "$@"
