# Инструкция по добавлению иконок криптовалют

## Добавленные токены

В проект были добавлены следующие токены:
- ZRX (0x)
- FET (Fetch.ai)
- BAT (Basic Attention Token)
- XMR (Monero)
- ZEC (Zcash)

## Как заменить заглушки на реальные изображения

1. Перейдите на сайт CoinMarketCap: https://coinmarketcap.com/
2. Найдите каждый токен по его символу (ZRX, FET, BAT, XMR, ZEC)
3. Скачайте логотип токена (обычно это можно сделать, кликнув правой кнопкой мыши на логотип и выбрав "Сохранить изображение как...")
4. Сохраните изображение в формате PNG
5. Переименуйте файл в соответствии с символом токена в нижнем регистре (например, `zrx.png`)
6. Замените файлы-заглушки в директории `assets/images/crypto_icons/` на скачанные изображения:
   - `assets/images/crypto_icons/zrx.png`
   - `assets/images/crypto_icons/fet.png`
   - `assets/images/crypto_icons/bat.png`
   - `assets/images/crypto_icons/xmr.png`
   - `assets/images/crypto_icons/zec.png`

## Рекомендации по изображениям

1. Размер изображений должен быть примерно 64x64 пикселя
2. Формат файлов - PNG с прозрачным фоном
3. Имена файлов должны быть в нижнем регистре и соответствовать символу токена

## Проверка

После замены изображений запустите приложение и убедитесь, что иконки отображаются корректно в списке криптовалют.

```
flutter run
```

## Альтернативный способ

Если у вас возникли проблемы с получением изображений с CoinMarketCap, вы можете использовать другие источники:
- CryptoIcons: https://cryptoicons.org/
- CoinGecko API: https://www.coingecko.com/en/api
- CryptoCompare: https://www.cryptocompare.com/

## Дополнительная информация

Список доступных иконок хранится в файле `lib/services/local_crypto_icons_service.dart`. Если вы добавляете новые иконки, не забудьте обновить список `_availableIcons` в этом файле.
