#!/bin/bash

# Путь к Flutter
FLUTTER_PATH="/Users/<USER>/Downloads/flutter/bin"

# Проверка существования директории Flutter
if [ ! -d "$FLUTTER_PATH" ]; then
    echo "Ошибка: Flutter не найден по пути $FLUTTER_PATH"
    exit 1
fi

# Добавление Flutter в PATH
export PATH="$FLUTTER_PATH:$PATH"

echo "Flutter добавлен в PATH для текущей сессии."
echo "Теперь вы можете использовать команду 'flutter run' напрямую."
echo "Текущий PATH: $PATH"

# Проверка, что Flutter доступен
flutter --version
