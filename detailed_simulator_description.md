# Detailed Description of Crypto Trading Simulator and Anti FOMO Simulator

## 1. Crypto Trading Simulator

### 1.1 Overview
The Crypto Trading Simulator is a tool designed to help users practice cryptocurrency trading without risking real money. It provides realistic market conditions to improve trading skills through different game modes.

### 1.2 Game Modes

#### 1.2.1 Custom Mode
- **Purpose**: Allows users to tailor their trading experience with specific settings
- **Configuration**:
  - Users select specific cryptocurrencies
  - Users choose timeframes (30m to 1d)
  - Users set leverage (1x to 1000x)
- **Workflow**:
  1. User selects "Custom Mode" from the mode selection screen
  2. User is directed to a settings page to configure their experience
  3. User can select a specific cryptocurrency or use "Next Symbol" button for random selection
  4. User selects timeframe and leverage
  5. User clicks "Start Trading" to begin
  6. Chart displays with 300 candles, with the rightmost candle centered
  7. User makes predictions by clicking UP/DOWN buttons
  8. Entry point appears on the chart after buttons are pressed
  9. Results are calculated based on price movement after the entry point
  10. Profit/loss is displayed, replacing action buttons until next round
  11. User can click "Next Round" to continue trading

#### 1.2.2 Infinite Patterns Mode
- **Purpose**: Provides endless trading scenarios with random coins and timeframes
- **Features**:
  - Tracks statistics (rounds played, win percentage, deaths, win streak)
  - Random coins and timeframes for each round
  - Leverage selection
  - Balance persistence between rounds
- **Workflow**:
  1. User selects "Infinite Patterns" from mode selection screen
  2. User selects leverage
  3. Game starts with $1000 initial balance
  4. Each round uses a random coin and timeframe
  5. User makes predictions with UP/DOWN buttons
  6. Results affect the persistent balance
  7. When balance reaches zero, "Game Over" is displayed with options:
     - Top Up (restores $1000 and adds a death)
     - Restart Game
     - Back to Menu

### 1.3 Trading Logic

#### 1.3.1 Chart Display
- Charts display 300 candles with the rightmost candle centered
- Entry point shows the last candle before UP/DOWN buttons are pressed
- Timeframe options range from 30-minute (default) to 1-day
- Price changes are limited to 0.1-0.5% per candle normally, 5-15% during news events

#### 1.3.2 Trade Execution
- User selects UP (buy) or DOWN (sell) prediction
- Trade correctness is determined by comparing the entry candle price with the result candle price
- Profit/loss is calculated based on:
  - Price change percentage
  - Selected leverage
  - 10% of current balance is used for each trade
- Formula: `profit = tradeAmount * (priceChange / 100) * leverageMultiplier`

#### 1.3.3 Balance Management
- Initial balance: $1000
- Balance is updated after each trade
- When balance reaches zero, game over state is triggered
- In Infinite Patterns mode, balance persists between rounds

## 2. Anti FOMO Simulator

### 2.1 Overview
The Anti FOMO Simulator is designed to help users overcome the Fear Of Missing Out (FOMO) in trading. It simulates various market scenarios to train emotional control and decision-making under pressure.

### 2.2 Game Modes

#### 2.2.1 Trader Roles
- **Novice Trader**:
  - Small deposit ($1000)
  - High leverage (10x)
  - High risk, high potential returns
  - Represented with teal color and paper airplane icon

- **Whale**:
  - Large capital ($100,000)
  - Low leverage (2x)
  - Market impact, slower moves
  - Represented with blue color

- **Darkpool Trader**:
  - Medium capital ($10,000)
  - Medium leverage (5x)
  - Access to potentially misleading insider information
  - Represented with purple color

#### 2.2.2 Difficulty Levels
- **Easy Mode**:
  - 30-second decision timer
  - Lower risk multipliers
  - More forgiving losses

- **Hard Mode**:
  - 15-second decision timer
  - Higher risk multipliers
  - More severe losses

- **Pro Mode**:
  - 5-second decision timer
  - Double losses compared to Hard mode
  - Black-colored button with gray-black background

### 2.3 Game Mechanics

#### 2.3.1 Social Hype Meter
- Visualized as a temperature gauge with 3 color-changing sections (blue to red)
- Represents market sentiment and social media buzz
- Affects trade outcomes and chat message frequency
- Higher hype increases potential profits but also potential losses
- Formula: `newHype = (baseHype * 0.6) + (scenarioInfluence * 1.2) + randomFactor`

#### 2.3.2 Chat System
- Displays fake social media messages that create social pressure
- Features include:
  - Dynamic message frequency based on social hype (2-6 seconds)
  - Diverse, non-repetitive usernames (44+ unique names)
  - Message pools based on market conditions and hype level
  - Reactions to user actions (buy/sell/hold)
  - Maximum 5 messages visible with scrolling

#### 2.3.3 News Ticker
- Scrolling ticker above the chart with smooth transitions
- Displays market news and special announcements
- Introduces chaos with random events
- Different styling for special announcements

#### 2.3.4 Trading Logic
- User selects action: BUY, SELL, or HOLD
- Timer forces decision (defaults to HOLD if timer expires)
- Optimal action is calculated based on market conditions
- Trade success depends on:
  - Whether user's action matches optimal action
  - Current market scenario
  - Social hype level
  - Selected difficulty
  - Selected trader role

#### 2.3.5 Profit/Loss Calculation
1. Base profit/loss percentage is determined
2. Adjusted based on volatility and social hype
3. Modified by difficulty multiplier
4. Applied role-specific multipliers:
   - Novice: 5x multiplier on percentage
   - Whale: 0.7x percentage but applied to 100x larger capital
   - Darkpool: 1.2x multiplier, but with trap mechanism on high hype
5. Converted to dollar amount based on role's starting capital
6. Formula: `profit = (baseAmount * profitPercent * multipliers) / 100`

### 2.4 Chart System
- Displays 250 candles with last candle positioned left-center
- Shows 20 future candles after user action
- Uses Binance API for historical data with 20+ trading pairs
- Supports 4 timeframes (30m, 1h, 4h, 1d)
- Implements market phases and technical indicators

### 2.5 Game Flow
1. User selects trader role (Novice, Whale, Darkpool)
2. User selects difficulty (Easy, Hard, Pro)
3. Game starts with role-specific initial balance
4. Timer begins counting down
5. User must make a decision (BUY/SELL/HOLD) before timer expires
6. Trade result is calculated and balance is updated
7. Future candles are revealed to show actual market movement
8. After 5-second delay, next round begins
9. Game continues until user quits or balance reaches zero
10. On game over, user can restart with same settings or return to menu

## 3. Technical Implementation Details

### 3.1 Crypto Trading Simulator Implementation

#### 3.1.1 Core Components
- **CryptoTradingSimulatorScreen**: Main component that orchestrates the simulator
- **GameModeSelectionScreen**: Displays mode selection options with animations
- **CustomModeSettingsScreen**: Configuration screen for Custom Mode
- **InfinitePatternsLeverageScreen**: Leverage selection for Infinite Patterns
- **Chart**: Renders candlestick charts using Lightweight Charts library
- **AntiFomoTradePanel**: Displays balance and trade buttons
- **InfinitePatternsStatsPanel**: Shows statistics in Infinite Patterns mode

#### 3.1.2 State Management
- Uses React hooks for state management:
  - `useTradingLogic`: Manages game state for Custom Mode
  - `useInfiniteTradingLogic`: Manages game state for Infinite Patterns
  - `useRandomSymbols`: Handles random cryptocurrency selection

#### 3.1.3 Data Sources
- Fetches real market data from Binance API
- `fetchCandles` function retrieves historical candle data
- Parameters include:
  - Symbol (e.g., 'BTCUSDT')
  - Timeframe (e.g., '30m', '1h', '4h', '1d')
  - Limit (number of candles, typically 300)

#### 3.1.4 Trade Calculation Logic
```javascript
// Determine trade success
const success = (action === 'buy' && resultCandle.close > entryCandle.close) ||
               (action === 'sell' && resultCandle.close < entryCandle.close);

// Calculate price change percentage
const priceChange = Math.abs(resultCandle.close - entryCandle.close) / entryCandle.close;
const profitPercent = priceChange * 100;

// Apply leverage
const profit = success
  ? TRADE_AMOUNT * (profitPercent / 100) * leverageMultiplier
  : -TRADE_AMOUNT * (profitPercent / 100) * leverageMultiplier;
```

### 3.2 Anti FOMO Simulator Implementation

#### 3.2.1 Core Components
- **AntiFOMOSimulatorScreen**: Main component that orchestrates the simulator
- **AntiFOMOSimulatorModeSelection**: Displays role selection cards
- **EnhancedChart**: Renders candlestick charts with additional indicators
- **FakeChat**: Simulates social media messages
- **NewsTickerWidget**: Displays scrolling news updates
- **SocialHypeMeter**: Visual representation of market sentiment

#### 3.2.2 FOMO Engine
- **BinanceFOMOEngine**: Core logic engine that uses real market data
- **FOMOEngine**: Alternative engine that can generate synthetic data
- Key functions:
  - `executeTrade`: Calculates trade outcomes
  - `generateCandles`: Creates chart data
  - `calculateOptimalAction`: Determines best action for current market
  - `applyGameModeMultipliers`: Adjusts profits based on selected role

#### 3.2.3 Social Hype System
```javascript
// Calculate new hype level
const baseHype = socialHype * 0.6; // Reduce previous hype influence
const scenarioInfluence = scenarioInterest * 1.2; // Increase scenario influence
const randomFactor = Math.floor(Math.random() * 10); // Random element

const newHype = Math.min(100, Math.max(0, baseHype + scenarioInfluence + randomFactor));
```

#### 3.2.4 Timer Implementation
```javascript
// Start timer based on difficulty
const timerDuration = difficulty === 'easy' ? 30 : difficulty === 'hard' ? 15 : 5;
setTimeLeft(timerDuration);

// Timer countdown
timerRef.current = setInterval(() => {
  setTimeLeft((prev) => {
    if (prev <= 1) {
      // Time's up - execute HOLD action automatically
      clearInterval(timerRef.current);
      handleAutoTrade();
      return 0;
    }
    return prev - 1;
  });
}, 1000);
```

## 4. Integration Between Simulators

### 4.1 Shared Components
- Both simulators use the same chart rendering technology
- Trade panel UI components are shared between simulators
- Balance display formatting is consistent across both simulators

### 4.2 Design Consistency
- Both simulators use a dark theme with gradient backgrounds
- Color schemes are consistent (green for positive, red for negative)
- Button styles and animations are standardized
- Font family (Rethink Sans) is used throughout both simulators

### 4.3 Navigation Flow
- Both simulators are accessible from the Games page
- Each simulator has its own mode selection screen
- "Back to Menu" button navigates to the respective mode selection screen
- Mode selection screens have a "Back" button to return to the Games page

## 5. Data Management and Algorithms

### 5.1 Candle Generation and Manipulation

#### 5.1.1 Real Data Approach
- Fetches historical data from Binance API
- Processes and formats candle data for chart display
- Splits candles into visible and hidden portions for prediction
- Example API call:
```javascript
const fetchCandles = async (symbol, interval, limit) => {
  try {
    const response = await fetch(
      `https://api.binance.com/api/v3/klines?symbol=${symbol}&interval=${interval}&limit=${limit}`
    );
    const data = await response.json();
    return data.map(candle => ({
      time: candle[0] / 1000,
      open: parseFloat(candle[1]),
      high: parseFloat(candle[2]),
      low: parseFloat(candle[3]),
      close: parseFloat(candle[4]),
      volume: parseFloat(candle[5])
    }));
  } catch (error) {
    console.error('Error fetching candles:', error);
    return [];
  }
};
```

#### 5.1.2 Synthetic Data Generation
- Creates realistic market patterns when real data is unavailable
- Implements market phases (accumulation, markup, distribution, markdown)
- Adds noise and volatility based on market conditions
- Ensures price changes are realistic (0.1-0.5% normally, 5-15% during events)

### 5.2 Pattern Detection and Scenario Generation

#### 5.2.1 Technical Patterns
- Detects common chart patterns (head and shoulders, double tops, etc.)
- Identifies trend lines and support/resistance levels
- Uses these patterns to create educational trading scenarios

#### 5.2.2 Market Scenarios
- Implements various market scenarios:
  - Bull and bear traps
  - Fake breakouts
  - News-driven volatility
  - Consolidation periods
  - Liquidation cascades
- Each scenario has specific characteristics that affect optimal trading decisions

### 5.3 User Experience Optimization

#### 5.3.1 Performance Considerations
- Chart rendering is optimized for mobile devices
- Data is processed efficiently to minimize lag
- Animations are hardware-accelerated where possible

#### 5.3.2 Educational Elements
- Both simulators focus on teaching important trading concepts
- Anti FOMO Simulator specifically targets emotional control
- Crypto Trading Simulator emphasizes technical analysis and pattern recognition
- Results provide feedback on decision quality, not just profit/loss

## 6. Conclusion

Both the Crypto Trading Simulator and Anti FOMO Simulator are sophisticated educational tools designed to help users improve their trading skills in a risk-free environment. They combine real market data with carefully designed game mechanics to create engaging and instructive experiences.

The Crypto Trading Simulator focuses on technical trading skills with its Custom Mode and Infinite Patterns mode, allowing users to practice making predictions based on chart patterns across various timeframes and leverage settings.

The Anti FOMO Simulator addresses the psychological aspects of trading by simulating social pressure and market hype, teaching users to make rational decisions despite external influences. Its three distinct roles (Novice Trader, Whale, Darkpool Trader) provide different perspectives on market dynamics.

Together, these simulators offer a comprehensive training platform for developing both the technical and psychological skills necessary for successful trading.
