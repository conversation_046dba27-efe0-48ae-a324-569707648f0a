#!/bin/bash

# Функция для поиска Flutter в системе
find_flutter() {
    # Проверяем стандартные пути установки Flutter
    local flutter_paths=(
        "/Users/<USER>/Downloads/flutter/bin/flutter"
        "$HOME/flutter/bin/flutter"
        "$HOME/development/flutter/bin/flutter"
        "/usr/local/flutter/bin/flutter"
        "/opt/flutter/bin/flutter"
    )
    
    for path in "${flutter_paths[@]}"; do
        if [ -f "$path" ]; then
            echo "$path"
            return 0
        fi
    done
    
    # Если Flutter не найден в стандартных путях, пробуем найти через which
    local which_flutter=$(which flutter 2>/dev/null)
    if [ -n "$which_flutter" ]; then
        echo "$which_flutter"
        return 0
    fi
    
    return 1
}

# Проверка, находимся ли мы в директории проекта Flutter
check_flutter_project() {
    if [ ! -f "pubspec.yaml" ]; then
        echo "Ошибка: pubspec.yaml не найден. Убедитесь, что вы находитесь в директории проекта Flutter."
        exit 1
    fi
}

# Основная функция
main() {
    # Находим Flutter
    local flutter_path=$(find_flutter)
    if [ -z "$flutter_path" ]; then
        echo "Ошибка: Flutter не найден в системе. Убедитесь, что Flutter установлен и добавлен в PATH."
        exit 1
    fi
    
    # Проверяем, находимся ли мы в директории проекта Flutter
    check_flutter_project
    
    # Запускаем Flutter
    echo "Запуск Flutter приложения с использованием $flutter_path..."
    "$flutter_path" run "$@"
}

# Запуск основной функции
main "$@"
