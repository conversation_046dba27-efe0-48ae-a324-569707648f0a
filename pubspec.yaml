name: finance_ai
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2

  # For date formatting
  intl: ^0.19.0

  # For launching URLs
  url_launcher: ^6.2.2

  # For state management
  provider: ^6.0.5

  # For HTTP requests
  http: ^1.1.0

  # For WebSocket connections
  webview_flutter: ^4.4.2

  # For local storage
  shared_preferences: ^2.2.2

  # For animations
  flutter_animate: ^4.3.0

  # For environment variables
  flutter_dotenv: ^5.1.0

  # For SVG rendering (already included above)
  flutter_svg: ^2.0.9

  # For charts
  fl_chart: ^1.0.0
  candlesticks: ^2.1.0

  # For machine learning and linear regression
  ml_linalg: ^13.0.0
  webview_flutter_android: ^4.4.2
  webview_flutter_wkwebview: ^3.20.0
  webview_flutter_web: ^0.2.2+4

  # For cryptographic functions
  crypto: ^3.0.3

  # For SVG rendering (already included above)
  cached_network_image: ^3.3.0
  flutter_markdown: ^0.7.7+1
  markdown: ^7.1.1
  html_unescape: ^2.0.0
  flutter_html: ^3.0.0-beta.2
  flutter_launcher_icons: ^0.14.3
  flutter_native_splash: ^2.3.8
  google_fonts: ^6.1.0
  flutter_staggered_animations: ^1.1.1
  flutter_staggered_grid_view: ^0.7.0
  shimmer: ^3.0.0
  lottie: ^3.3.1
  flutter_slidable: ^4.0.0
  flutter_local_notifications: ^19.2.1
  timezone: ^0.10.1
  flutter_localizations:
    sdk: flutter

  # Added for the new dependencies
  web_socket_channel: ^3.0.3
  syncfusion_flutter_gauges: ^29.2.7+1
  video_player: ^2.9.5
  chewie: ^1.11.3

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.3

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - .env
    - logo/bottom_navigation_bar/
    - logo/Sinusoid/
    - logo/TMM/
    - logo/Learn/Games/
    - logo/Learn/Clicked/
    - logo/Learn/Unclicked/
    - logo/Learn/Navigation_bar/

    - logo/Learn/Games/Crypto_Trading_Simulator.png
    - logo/Learn/Games/Anti_FOMO.png
    - assets/images/crypto_icons/
    - assets/tradingview_chart.html

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font.
  # Temporarily comment out the Inter font until the font files are added
  # fonts:
  #   - family: Inter
  #     fonts:
  #       - asset: fonts/Inter/Inter-Light.ttf
  #         weight: 300
  #       - asset: fonts/Inter/Inter-Regular.ttf
  #         weight: 400
  #       - asset: fonts/Inter/Inter-Medium.ttf
  #         weight: 500
  #       - asset: fonts/Inter/Inter-SemiBold.ttf
  #         weight: 600
  #       - asset: fonts/Inter/Inter-Bold.ttf
  #         weight: 700

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
