# Crypto Trading Simulator

A Flutter-based cryptocurrency trading simulator that helps users practice trading strategies in a risk-free environment.

## Features

### Game Modes

1. **Infinite Patterns Mode**
   - Trade with randomly selected cryptocurrency pairs
   - Multiple timeframe options (30m, 1h, 4h, 1d)
   - Track your balance, win rate, and performance
   - Death counter for tracking losses

2. **Practice Mode**
   - Learn common trading patterns
   - Predefined scenarios for different market conditions
   - Perfect for beginners learning technical analysis

3. **Custom Mode**
   - Configure your own trading environment
   - Select specific cryptocurrencies and timeframes
   - Adjust leverage and initial balance

### Trading Features

- Real-time candlestick charts using TradingView's lightweight charts
- Leverage options from 1x to 100x
- Entry point markers for trade tracking
- Smooth animations and iOS-like UI
- Dark mode support

### Technical Implementation

- Built with Flutter and Dart
- Uses Provider for state management
- Integrates with Binance API for real market data
- WebView-based chart rendering for optimal performance
- Responsive design for all screen sizes

## Getting Started

1. Clone the repository
2. Install dependencies:
   ```bash
   flutter pub get
   ```
3. Run the app:
   ```bash
   flutter run -d edge
   ```

## Dependencies

- flutter: ^3.0.0
- provider: ^6.0.5
- http: ^1.1.0
- webview_flutter: ^4.4.2
- webview_flutter_web: ^0.2.2+4
- shared_preferences: ^2.2.2
- intl: ^0.18.1
- flutter_dotenv: ^5.1.0

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a new Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.