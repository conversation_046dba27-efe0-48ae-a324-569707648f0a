import 'package:flutter/material.dart';
import '../models/course.dart';
import '../widgets/course_card.dart';
import '../widgets/app_bottom_navigation.dart';

class CoursesScreen extends StatefulWidget {
  const CoursesScreen({super.key});

  @override
  State<CoursesScreen> createState() => _CoursesScreenState();
}

class _CoursesScreenState extends State<CoursesScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<Course> _courses = Course.getMockCourses();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Learn'),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(
              icon: Icon(Icons.book),
              text: 'Courses',
            ),
            Tab(
              icon: Icon(Icons.article),
              text: 'Materials',
            ),
            Tab(
              icon: Icon(Icons.games),
              text: 'Games',
            ),
          ],
          onTap: (index) {
            if (index == 1) {
              // Navigate to Materials screen
              Navigator.pushNamed(context, '/materials');
              _tabController.animateTo(0); // Reset to Courses tab
            } else if (index == 2) {
              // Navigate to Games screen
              Navigator.pushNamed(context, '/games');
              _tabController.animateTo(0); // Reset to Courses tab
            }
          },
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        physics: const NeverScrollableScrollPhysics(), // Disable swiping
        children: [
          // Courses tab
          ListView.builder(
            itemCount: _courses.length,
            itemBuilder: (context, index) {
              final course = _courses[index];
              return CourseCard(
                course: course,
                onTap: () {
                  Navigator.pushNamed(
                    context,
                    '/course_detail',
                    arguments: course,
                  );
                },
              );
            },
          ),
          
          // Materials tab (placeholder, navigation handled in onTap)
          const Center(
            child: Text('Materials Tab - Redirecting...'),
          ),
          
          // Games tab (placeholder, navigation handled in onTap)
          const Center(
            child: Text('Games Tab - Redirecting...'),
          ),
        ],
      ),
      bottomNavigationBar: AppBottomNavigation(
        currentIndex: 2,
        onTap: (index) {
          if (index != 2) {
            switch (index) {
              case 0:
                Navigator.pushReplacementNamed(context, '/news');
                break;
              case 1:
                Navigator.pushReplacementNamed(context, '/crypto_markets');
                break;
              case 3:
                Navigator.pushReplacementNamed(context, '/profile');
                break;
            }
          }
        },
      ),
    );
  }
}
