import 'package:flutter/material.dart';
import 'dart:io' show Platform;
import 'package:flutter/foundation.dart' show kIsWeb;

enum DeviceType {
  mobile,
  tablet,
  desktop,
}

class DeviceUtils {
  static DeviceType getDeviceType(BuildContext context) {
    // Get the width of the screen
    final width = MediaQuery.of(context).size.width;

    // Check if running on web
    if (kIsWeb) {
      if (width < 600) {
        return DeviceType.mobile;
      } else if (width < 1200) {
        return DeviceType.tablet;
      } else {
        return DeviceType.desktop;
      }
    }

    // Check platform for native apps
    if (Platform.isAndroid || Platform.isIOS) {
      if (width < 600) {
        return DeviceType.mobile;
      } else {
        return DeviceType.tablet;
      }
    }

    // For desktop platforms (Windows, macOS, Linux)
    return DeviceType.desktop;
  }

  static bool isMobile(BuildContext context) {
    return getDeviceType(context) == DeviceType.mobile;
  }

  static bool isTablet(BuildContext context) {
    return getDeviceType(context) == DeviceType.tablet;
  }

  static bool isDesktop(BuildContext context) {
    return getDeviceType(context) == DeviceType.desktop;
  }
}
