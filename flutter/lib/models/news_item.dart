class NewsItem {
  final String id;
  final String title;
  final String description;
  final String imageUrl;
  final DateTime publishedAt;
  final String source;
  final String url;
  final SentimentType sentiment;
  final List<String> tags;
  final NewsCategory category;

  NewsItem({
    required this.id,
    required this.title,
    required this.description,
    required this.imageUrl,
    required this.publishedAt,
    required this.source,
    required this.url,
    required this.sentiment,
    required this.tags,
    required this.category,
  });

  // Mock data factory
  static List<NewsItem> getMockItems() {
    return [
      NewsItem(
        id: '1',
        title: 'Bitcoin Surges Past \$60,000 as Institutional Adoption Grows',
        description: 'Bitcoin has surpassed \$60,000 for the first time in weeks as institutional investors continue to show interest in the cryptocurrency.',
        imageUrl: 'https://via.placeholder.com/300x200?text=Bitcoin',
        publishedAt: DateTime.now().subtract(const Duration(hours: 2)),
        source: 'CryptoNews',
        url: 'https://example.com/news/1',
        sentiment: SentimentType.positive,
        tags: ['BTC', 'Macro'],
        category: NewsCategory.crypto,
      ),
      NewsItem(
        id: '2',
        title: 'Ethereum Upgrade Delayed: What This Means for Investors',
        description: 'The much-anticipated Ethereum upgrade has been delayed by developers, raising questions about the future of the network.',
        imageUrl: 'https://via.placeholder.com/300x200?text=Ethereum',
        publishedAt: DateTime.now().subtract(const Duration(hours: 5)),
        source: 'BlockchainToday',
        url: 'https://example.com/news/2',
        sentiment: SentimentType.neutral,
        tags: ['Ethereum', 'DeFi'],
        category: NewsCategory.crypto,
      ),
      NewsItem(
        id: '3',
        title: 'SEC Rejects Another Bitcoin ETF Application',
        description: 'The Securities and Exchange Commission has rejected another application for a Bitcoin ETF, citing market manipulation concerns.',
        imageUrl: 'https://via.placeholder.com/300x200?text=SEC',
        publishedAt: DateTime.now().subtract(const Duration(hours: 8)),
        source: 'FinanceDaily',
        url: 'https://example.com/news/3',
        sentiment: SentimentType.negative,
        tags: ['BTC', 'SEC', 'Macro'],
        category: NewsCategory.crypto,
      ),
      NewsItem(
        id: '4',
        title: 'New AI Tool Predicts Crypto Market Movements with 70% Accuracy',
        description: 'A new artificial intelligence tool claims to predict cryptocurrency market movements with 70% accuracy, based on social media sentiment analysis.',
        imageUrl: 'https://via.placeholder.com/300x200?text=AI+Crypto',
        publishedAt: DateTime.now().subtract(const Duration(hours: 12)),
        source: 'TechCrypto',
        url: 'https://example.com/news/4',
        sentiment: SentimentType.positive,
        tags: ['AI', 'BTC', 'Ethereum'],
        category: NewsCategory.crypto,
      ),
      NewsItem(
        id: '5',
        title: 'Apple Stock Reaches All-Time High After Strong Earnings Report',
        description: 'Apple shares have reached an all-time high following a quarterly earnings report that exceeded analyst expectations.',
        imageUrl: 'https://via.placeholder.com/300x200?text=Apple',
        publishedAt: DateTime.now().subtract(const Duration(hours: 24)),
        source: 'StockMarketNews',
        url: 'https://example.com/news/5',
        sentiment: SentimentType.positive,
        tags: ['Macro'],
        category: NewsCategory.stocks,
      ),
    ];
  }
}

enum SentimentType {
  positive,
  neutral,
  negative,
}

enum NewsCategory {
  all,
  crypto,
  stocks,
}
