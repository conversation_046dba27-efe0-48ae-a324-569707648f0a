import 'package:flutter/material.dart';
import '../models/news_item.dart';
import '../utils/device_type.dart';

class NewsCard extends StatelessWidget {
  final NewsItem newsItem;
  final VoidCallback onTap;

  const NewsCard({
    super.key,
    required this.newsItem,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final isDesktop = DeviceUtils.isDesktop(context);

    return Card(
      margin: EdgeInsets.symmetric(
        vertical: 8.0,
        horizontal: isDesktop ? 24.0 : 16.0,
      ),
      elevation: 2.0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12.0),
        child: isDesktop
            ? _buildDesktopLayout(context)
            : _buildMobileLayout(context),
      ),
    );
  }

  // Desktop layout with image on the right
  Widget _buildDesktopLayout(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Content on the left
          Expanded(
            flex: 3,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Source, date and sentiment
                Row(
                  children: [
                    // Source
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                      decoration: BoxDecoration(
                        color: Colors.blue.withAlpha(25), // ~0.1 opacity
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: Text(
                        newsItem.source,
                        style: TextStyle(
                          color: Colors.blue[800],
                          fontSize: 12.0,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8.0),

                    // Date
                    Text(
                      _formatDate(newsItem.publishedAt),
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 12.0,
                      ),
                    ),
                    const SizedBox(width: 12.0),

                    // Sentiment indicator
                    _buildSentimentIndicator(),
                  ],
                ),
                const SizedBox(height: 12.0),

                // Title
                Text(
                  newsItem.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18.0,
                  ),
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12.0),

                // Description
                Text(
                  newsItem.description,
                  style: const TextStyle(fontSize: 14.0),
                  maxLines: 4,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 12.0),

                // Tags
                Wrap(
                  spacing: 8.0,
                  runSpacing: 8.0,
                  children: newsItem.tags.map((tag) => _buildTag(tag)).toList(),
                ),

                const SizedBox(height: 12.0),

                // Action buttons
                Row(
                  children: [
                    _buildActionButton(
                      icon: Icons.bookmark_border,
                      label: 'Save',
                      onPressed: () {},
                    ),
                    const SizedBox(width: 16.0),
                    _buildActionButton(
                      icon: Icons.share,
                      label: 'Share',
                      onPressed: () {},
                    ),
                    const SizedBox(width: 16.0),
                    _buildActionButton(
                      icon: Icons.open_in_new,
                      label: 'Read More',
                      onPressed: onTap,
                      isPrimary: true,
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(width: 24.0),

          // Image on the right
          Expanded(
            flex: 1,
            child: Stack(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(8.0),
                  child: Image.network(
                    newsItem.imageUrl,
                    height: 180,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    errorBuilder: (context, error, stackTrace) {
                      return Container(
                        height: 180,
                        width: double.infinity,
                        color: Colors.grey[300],
                        child: const Icon(Icons.image_not_supported, size: 50),
                      );
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Mobile layout with image on top
  Widget _buildMobileLayout(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // News image with sentiment indicator
        Stack(
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12.0),
                topRight: Radius.circular(12.0),
              ),
              child: Image.network(
                newsItem.imageUrl,
                height: 150,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    height: 150,
                    width: double.infinity,
                    color: Colors.grey[300],
                    child: const Icon(Icons.image_not_supported, size: 50),
                  );
                },
              ),
            ),
            Positioned(
              top: 10,
              right: 10,
              child: _buildSentimentIndicator(),
            ),
          ],
        ),

        // News content
        Padding(
          padding: const EdgeInsets.all(12.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Source and date
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    newsItem.source,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12.0,
                    ),
                  ),
                  Text(
                    _formatDate(newsItem.publishedAt),
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12.0,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8.0),

              // Title
              Text(
                newsItem.title,
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16.0,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8.0),

              // Description
              Text(
                newsItem.description,
                style: const TextStyle(fontSize: 14.0),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8.0),

              // Tags
              Wrap(
                spacing: 6.0,
                runSpacing: 6.0,
                children: newsItem.tags.map((tag) => _buildTag(tag)).toList(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required String label,
    required VoidCallback onPressed,
    bool isPrimary = false,
  }) {
    return TextButton.icon(
      onPressed: onPressed,
      icon: Icon(
        icon,
        color: isPrimary ? Colors.blue : Colors.grey[700],
        size: 18,
      ),
      label: Text(
        label,
        style: TextStyle(
          color: isPrimary ? Colors.blue : Colors.grey[700],
          fontWeight: isPrimary ? FontWeight.bold : FontWeight.normal,
        ),
      ),
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: isPrimary
              ? const BorderSide(color: Colors.blue, width: 1)
              : BorderSide.none,
        ),
      ),
    );
  }

  Widget _buildSentimentIndicator() {
    Color backgroundColor;
    IconData icon;
    String label;

    switch (newsItem.sentiment) {
      case SentimentType.positive:
        backgroundColor = Colors.green;
        icon = Icons.trending_up;
        label = 'Positive';
        break;
      case SentimentType.negative:
        backgroundColor = Colors.red;
        icon = Icons.trending_down;
        label = 'Negative';
        break;
      case SentimentType.neutral:
      default:
        backgroundColor = Colors.grey;
        icon = Icons.trending_flat;
        label = 'Neutral';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.white, size: 14.0),
          const SizedBox(width: 4.0),
          Text(
            label,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 12.0,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTag(String tag) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12.0),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Text(
        '#$tag',
        style: TextStyle(
          color: Colors.blue[800],
          fontSize: 12.0,
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
