import 'package:flutter/material.dart';
import '../models/news_item.dart';

class FilterPanel extends StatelessWidget {
  final List<String> selectedTags;
  final SentimentType? selectedSentiment;
  final Function(String) onTagToggle;
  final Function(SentimentType?) onSentimentChanged;

  const FilterPanel({
    super.key,
    required this.selectedTags,
    required this.selectedSentiment,
    required this.onTagToggle,
    required this.onSentimentChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tags filter
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Text(
            'Filter by tags',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.grey[700],
            ),
          ),
        ),
        SizedBox(
          height: 40,
          child: ListView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(horizontal: 8.0),
            children: [
              _buildTagChip('Macro', context),
              _buildTagChip('AI', context),
              _buildTagChip('BTC', context),
              _buildTagChip('Ethereum', context),
              _buildTagChip('Memes', context),
              _buildTagChip('DeFi', context),
              _buildTagChip('RWA', context),
              _buildTagChip('NFT', context),
              _buildTagChip('SEC', context),
              _buildTagChip('AirDrop', context),
            ],
          ),
        ),
        
        // Sentiment filter
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Filter by sentiment',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[700],
                ),
              ),
              const SizedBox(height: 8.0),
              Row(
                children: [
                  _buildSentimentButton(null, 'All', context),
                  const SizedBox(width: 8.0),
                  _buildSentimentButton(SentimentType.positive, 'Positive', context),
                  const SizedBox(width: 8.0),
                  _buildSentimentButton(SentimentType.neutral, 'Neutral', context),
                  const SizedBox(width: 8.0),
                  _buildSentimentButton(SentimentType.negative, 'Negative', context),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTagChip(String tag, BuildContext context) {
    final isSelected = selectedTags.contains(tag);
    
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0),
      child: FilterChip(
        label: Text(tag),
        selected: isSelected,
        onSelected: (selected) {
          onTagToggle(tag);
        },
        backgroundColor: Colors.grey[200],
        selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
        checkmarkColor: Theme.of(context).primaryColor,
        labelStyle: TextStyle(
          color: isSelected ? Theme.of(context).primaryColor : Colors.black,
          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
        ),
      ),
    );
  }

  Widget _buildSentimentButton(SentimentType? sentiment, String label, BuildContext context) {
    final isSelected = selectedSentiment == sentiment;
    
    Color backgroundColor;
    Color textColor;
    
    if (isSelected) {
      backgroundColor = _getSentimentColor(sentiment);
      textColor = Colors.white;
    } else {
      backgroundColor = Colors.grey[200]!;
      textColor = _getSentimentColor(sentiment);
    }
    
    return ElevatedButton(
      onPressed: () {
        onSentimentChanged(sentiment);
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: backgroundColor,
        foregroundColor: textColor,
        padding: const EdgeInsets.symmetric(horizontal: 12.0),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20.0),
        ),
      ),
      child: Text(label),
    );
  }

  Color _getSentimentColor(SentimentType? sentiment) {
    switch (sentiment) {
      case SentimentType.positive:
        return Colors.green;
      case SentimentType.negative:
        return Colors.red;
      case SentimentType.neutral:
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }
}
