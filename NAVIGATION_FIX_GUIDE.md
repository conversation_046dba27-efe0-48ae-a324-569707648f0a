# 🔧 Исправление навигации кнопки Sinusoid

## 🚨 Проблема
Кнопка Sinusoid в навигационной панели вела на неправильную страницу.

## ✅ Решение

### Что было исправлено:
1. **Добавлен централизованный обработчик навигации** в `AppBottomNavigation`
2. **Исправлен маршрут для Sinusoid** - теперь используется `/sinusoid` для `ReactorSinusoidScreen`
3. **Обеспечена консистентность** навигации во всех экранах

### Изменения в коде:

#### `lib/widgets/app_bottom_navigation.dart`

```dart
// Добавлен централизованный обработчик
void _handleNavigation(int index) {
  if (index == widget.currentIndex) return;

  // Используем пользовательский callback если он предоставлен
  if (widget.onTap != null) {
    widget.onTap!(index);
    return;
  }

  // Иначе используем встроенную навигационную логику
  switch (index) {
    case 0:
      Navigator.pushReplacementNamed(context, '/news');
      break;
    case 1:
      Navigator.pushReplacementNamed(context, '/charts');
      break;
    case 2:
      // ИСПРАВЛЕНО: Правильный маршрут для Sinusoid Reactor
      Navigator.pushReplacementNamed(context, '/sinusoid');
      break;
    case 3:
      Navigator.pushReplacementNamed(context, '/courses');
      break;
    case 4:
      Navigator.pushReplacementNamed(context, '/profile');
      break;
  }
}
```

### Преимущества исправления:

✅ **Правильная навигация**: Sinusoid теперь ведет на `/sinusoid` (ReactorSinusoidScreen)  
✅ **Консистентность**: Одинаковая логика для всех экранов  
✅ **Обратная совместимость**: Сохранены существующие onTap callbacks  
✅ **Надежность**: Централизованное управление маршрутами  

### Как работает:

1. При нажатии на любую кнопку навигации вызывается `_handleNavigation(index)`
2. Если экран предоставил свой `onTap` callback - используется он
3. Если callback не предоставлен - используется встроенная логика с правильными маршрутами
4. Для кнопки Sinusoid (index = 2) всегда используется маршрут `/sinusoid`

### Соответствие маршрутов:

| Индекс | Экран | Маршрут | Целевой экран |
|--------|-------|---------|---------------|
| 0 | News | `/news` | NewsScreen |
| 1 | Charts | `/charts` | ChartsScreen |
| 2 | **Sinusoid** | **`/sinusoid`** | **ReactorSinusoidScreen** |
| 3 | Learn | `/courses` | CoursesScreen |
| 4 | Profile | `/profile` | ProfileScreen |

## 🎯 Результат

Теперь кнопка Sinusoid корректно навигирует на экран `ReactorSinusoidScreen` через маршрут `/sinusoid`, что соответствует ожиданиям пользователей - ведет на "sinusoid reactor". 