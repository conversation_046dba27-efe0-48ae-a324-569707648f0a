import 'package:flutter/material.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:provider/provider.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'dart:math' as math;
import 'dart:ui';
import '../providers/auth_provider.dart';
import 'hyperjump_animation_screen.dart';
import 'terms_of_service_screen.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({Key? key}) : super(key: key);

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> with SingleTickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();

  bool _acceptedTerms = false;

  late AnimationController _starsController;
  final List<Star> _stars = [];

  @override
  void initState() {
    super.initState();

    // Создаем звезды для фона
    for (int i = 0; i < 100; i++) {
      _stars.add(Star(
        x: math.Random().nextDouble() * 1.0,
        y: math.Random().nextDouble() * 1.0,
        size: math.Random().nextDouble() * 2.0 + 1.0,
        blinkDuration: (math.Random().nextDouble() * 2.0 + 3.0) * 1000,
      ));
    }

    // Инициализируем контроллер анимации для звезд
    _starsController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 5000),
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    _starsController.dispose();
    super.dispose();
  }

  void _register() async {
    if (_formKey.currentState!.validate()) {
      if (!_acceptedTerms) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Пожалуйста, примите условия пользовательского соглашения'),
            backgroundColor: Colors.red,
          ),
        );
        return;
      }

      final authProvider = Provider.of<AuthProvider>(context, listen: false);

      final success = await authProvider.register(
        _nameController.text.trim(),
        _emailController.text.trim(),
        _passwordController.text.trim(),
      );

      if (success && mounted) {
        // Переход к анимации гиперпрыжка
        Navigator.pushReplacement(
          context,
          PageRouteBuilder(
            pageBuilder: (context, animation, secondaryAnimation) => const HyperjumpAnimationScreen(),
            transitionsBuilder: (context, animation, secondaryAnimation, child) {
              return FadeTransition(
                opacity: animation,
                child: child,
              );
            },
            transitionDuration: const Duration(milliseconds: 500),
          ),
        );
      }
    }
  }

  void _registerWithSocial(String provider) async {
    final authProvider = Provider.of<AuthProvider>(context, listen: false);

    bool success = false;

    // Вызываем соответствующий метод в зависимости от провайдера
    switch (provider) {
      case 'google':
        success = await authProvider.registerWithGoogle();
        break;
      case 'facebook':
        success = await authProvider.registerWithFacebook();
        break;
      case 'linkedin':
        success = await authProvider.registerWithLinkedIn();
        break;
    }

    if (success && mounted) {
      // Переход к анимации гиперпрыжка
      Navigator.pushReplacement(
        context,
        PageRouteBuilder(
          pageBuilder: (context, animation, secondaryAnimation) => const HyperjumpAnimationScreen(),
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: child,
            );
          },
          transitionDuration: const Duration(milliseconds: 500),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final authProvider = Provider.of<AuthProvider>(context);

    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Анимированные звезды на фоне
          AnimatedBuilder(
            animation: _starsController,
            builder: (context, child) {
              return CustomPaint(
                size: Size(size.width, size.height),
                painter: StarsPainter(_stars, _starsController.value),
              );
            },
          ),

          // Эффект размытия
          BackdropFilter(
            filter: ImageFilter.blur(sigmaX: 3, sigmaY: 3),
            child: Container(
              color: Colors.white.withOpacity(0.1),
            ),
          ),

          // Форма регистрации
          Center(
            child: SingleChildScrollView(
              child: Container(
                width: math.min(400, size.width * 0.9),
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.3),
                      blurRadius: 10,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Form(
                  key: _formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Убираем логотип TMM и надпись TMM
                      const SizedBox(height: 10),

                      // Поле для имени
                      TextFormField(
                        controller: _nameController,
                        style: TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          labelText: 'Имя',
                          labelStyle: TextStyle(color: Colors.white70),
                          prefixIcon: Icon(Icons.person, color: Colors.white70),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.white30),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.white30),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.white),
                          ),
                          filled: true,
                          fillColor: Colors.black45,
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Пожалуйста, введите ваше имя';
                          }
                          return null;
                        },
                      ).animate().fadeIn(duration: 500.ms, delay: 200.ms),

                      const SizedBox(height: 16),

                      // Поле для email
                      TextFormField(
                        controller: _emailController,
                        keyboardType: TextInputType.emailAddress,
                        style: TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          labelText: 'Email',
                          labelStyle: TextStyle(color: Colors.white70),
                          prefixIcon: Icon(Icons.email, color: Colors.white70),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.white30),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.white30),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.white),
                          ),
                          filled: true,
                          fillColor: Colors.black45,
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Пожалуйста, введите ваш email';
                          }
                          if (!value.contains('@')) {
                            return 'Пожалуйста, введите корректный email';
                          }
                          return null;
                        },
                      ).animate().fadeIn(duration: 500.ms, delay: 400.ms),

                      const SizedBox(height: 16),

                      // Поле для пароля
                      TextFormField(
                        controller: _passwordController,
                        obscureText: true,
                        style: TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          labelText: 'Пароль',
                          labelStyle: TextStyle(color: Colors.white70),
                          prefixIcon: Icon(Icons.lock, color: Colors.white70),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.white30),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.white30),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(color: Colors.white),
                          ),
                          filled: true,
                          fillColor: Colors.black45,
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Пожалуйста, введите пароль';
                          }
                          if (value.length < 6) {
                            return 'Пароль должен содержать не менее 6 символов';
                          }
                          return null;
                        },
                      ).animate().fadeIn(duration: 500.ms, delay: 600.ms),

                      const SizedBox(height: 24),

                      // Чекбокс для пользовательского соглашения
                      Row(
                        children: [
                          Checkbox(
                            value: _acceptedTerms,
                            onChanged: (value) {
                              setState(() {
                                _acceptedTerms = value ?? false;
                              });
                            },
                            fillColor: MaterialStateProperty.resolveWith<Color>((states) {
                              if (states.contains(MaterialState.selected)) {
                                return Colors.grey[800]!;
                              }
                              return Colors.grey[600]!;
                            }),
                            checkColor: Colors.white,
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _acceptedTerms = !_acceptedTerms;
                                });
                              },
                              child: RichText(
                                text: TextSpan(
                                  text: 'Я принимаю ',
                                  style: TextStyle(color: Colors.white70),
                                  children: [
                                    TextSpan(
                                      text: 'Пользовательское соглашение',
                                      style: TextStyle(
                                        color: Colors.white,
                                        decoration: TextDecoration.underline,
                                      ),
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = () {
                                          Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                              builder: (context) => const TermsOfServiceScreen(),
                                            ),
                                          );
                                        },
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ).animate().fadeIn(duration: 500.ms, delay: 700.ms),

                      const SizedBox(height: 24),

                      // Кнопка регистрации
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: authProvider.isLoading ? null : _register,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[900],
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            elevation: 0,
                            disabledBackgroundColor: Colors.grey[900]!.withOpacity(0.5),
                          ),
                          child: authProvider.isLoading
                              ? const SizedBox(
                                  height: 20,
                                  width: 20,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Text(
                                  'Завершить регистрацию',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                        ),
                      ).animate().fadeIn(duration: 500.ms, delay: 800.ms),

                      const SizedBox(height: 24),

                      // Разделитель
                      Row(
                        children: [
                          Expanded(
                            child: Divider(
                              color: Colors.white.withOpacity(0.3),
                              thickness: 1,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Text(
                              'или',
                              style: TextStyle(
                                color: Colors.white.withOpacity(0.7),
                                fontSize: 14,
                              ),
                            ),
                          ),
                          Expanded(
                            child: Divider(
                              color: Colors.white.withOpacity(0.3),
                              thickness: 1,
                            ),
                          ),
                        ],
                      ).animate().fadeIn(duration: 500.ms, delay: 1000.ms),

                      const SizedBox(height: 24),

                      // Кнопки социальных сетей
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          // Google
                          _SocialButton(
                            svgAsset: 'assets/images/google_logo.svg',
                            fallbackIcon: Icons.g_mobiledata,
                            color: Colors.white,
                            onPressed: () => _registerWithSocial('google'),
                          ).animate().fadeIn(duration: 500.ms, delay: 1200.ms),

                          // Facebook
                          _SocialButton(
                            svgAsset: 'assets/images/facebook_logo.svg',
                            fallbackIcon: Icons.facebook,
                            color: const Color(0xFF1877F2),
                            onPressed: () => _registerWithSocial('facebook'),
                          ).animate().fadeIn(duration: 500.ms, delay: 1400.ms),

                          // LinkedIn
                          _SocialButton(
                            svgAsset: 'assets/images/linkedin_logo.svg',
                            fallbackIcon: Icons.link,
                            color: const Color(0xFF0A66C2),
                            onPressed: () => _registerWithSocial('linkedin'),
                          ).animate().fadeIn(duration: 500.ms, delay: 1600.ms),
                        ],
                      ),

                      // Сообщение об ошибке
                      if (authProvider.error.isNotEmpty)
                        Container(
                          margin: const EdgeInsets.only(top: 16),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.red.withOpacity(0.3)),
                          ),
                          child: Text(
                            authProvider.error,
                            style: const TextStyle(
                              color: Colors.red,
                              fontSize: 14,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ).animate().fadeIn(duration: 300.ms).shake(hz: 4),
                    ],
                  ),
                ),
              ),
            ),
          ).animate().slideY(
            begin: 1.0,
            end: 0.0,
            duration: 1.seconds,
            curve: Curves.easeOutQuad,
          ),
        ],
      ),
    );
  }
}

// Класс для представления звезды
class Star {
  final double x;
  final double y;
  final double size;
  final double blinkDuration;

  Star({
    required this.x,
    required this.y,
    required this.size,
    required this.blinkDuration,
  });
}

// Кастомный painter для отрисовки звезд
class StarsPainter extends CustomPainter {
  final List<Star> stars;
  final double animationValue;

  StarsPainter(this.stars, this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white;

    for (var star in stars) {
      // Вычисляем текущую прозрачность звезды
      final opacity = 0.3 + (0.7 - 0.3) *
          (math.sin(2 * math.pi * (animationValue + star.x * star.y) % 1) * 0.5 + 0.5);

      paint.color = Colors.white.withOpacity(opacity);

      canvas.drawCircle(
        Offset(star.x * size.width, star.y * size.height),
        star.size,
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

// Виджет кнопки для социальных сетей
class _SocialButton extends StatelessWidget {
  final String svgAsset;
  final IconData fallbackIcon;
  final Color color;
  final VoidCallback onPressed;

  const _SocialButton({
    required this.svgAsset,
    required this.fallbackIcon,
    required this.color,
    required this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(50),
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
          color: Colors.grey[900],
          shape: BoxShape.circle,
          border: Border.all(
            color: Colors.grey[800]!,
            width: 1,
          ),
        ),
        child: Center(
          child: _buildIcon(),
        ),
      ),
    );
  }

  Widget _buildIcon() {
    // Пытаемся загрузить SVG-изображение
    return SvgPicture.asset(
      svgAsset,
      width: 24,
      height: 24,
      placeholderBuilder: (context) {
        // Если изображение не найдено, используем запасную иконку
        return Icon(
          fallbackIcon,
          color: color,
          size: 24,
        );
      },
    );
  }
}
